//TJADB Project
TITLE:Koritsu Aoharu Gakuen Kouka
TITLEJA:鼓立あおはる学園校歌
SUBTITLE:--Blue-back Choir
SUBTITLEJA:ブルーバック合唱団
BPM:125
WAVE:Koritsu Aoharu Gakuen Kouka.ogg
OFFSET:-5.070
DEMOSTART:5.070


COURSE:Oni
LEVEL:10
BALLOON:10,40,25
SCOREINIT:1590
SCOREDIFF:0

#START
100101100101,
100100100101,
1111,
100101100100,

100101100101,
100100100101,
1111,
1110, //8

1001101010101010,
1001101010001010,
1001101010101010,
1001101010003000,

300000100010300000300300,
300000100010300000300300,
3333,
#BPMCHANGE 200
100000100000111110111110, //16

111111111111,
111111111111,
111111111111,
111111111111, //20

#BPMCHANGE 400
100000100000500080000000100000100000500080000000,
100000100000500080000000100000100000500080000000,
100000100000500080000000100000100000500080000000,
100000100000500080000000100000100000500080000000,

100000100000500080000000100000100000500080000000,
100000100000500080000000100000100000500080000000,
7,
#MEASURE 2/4
#BPMCHANGE 100
#BARLINEOFF
8, //28

#BPMCHANGE 120
#BARLINEON
111101111110,
#BPMCHANGE 140
111101111110,
#BPMCHANGE 160
111101111110,
#BPMCHANGE 180
111101111110,

#BPMCHANGE 200
111101111110,
#BPMCHANGE 220
111101111110,
#BPMCHANGE 240
111101111110,
#BPMCHANGE 125
#SCROLL 1.92
1
#SCROLL 1
1, //36

#MEASURE 4/4
10101011,
10101033,
30313131,
600000000000000000000000000000000008000000000000, //40

#MEASURE 3/4
101101101,
100101100,
100101101,
100101100,
101101100, //45

#MEASURE 4/4
#BPMCHANGE 220
#GOGOSTART
7,
0,
0,
8, //49
#GOGOEND

1010101110001010,
1010111010001010,
1011101010001010,
1010111010001010,

1011101010111010,
1010111010001010,
1011101010111011,
1010111010000000, //57

1011101010111010,
1011101010111010,
1011101010111010,
1011101010001000,

11110000,
0,
#MEASURE 2/4
0,
#BPMCHANGE 125
01, //65

#MEASURE 4/4
11,
1,
0,
3330,

1011101010111010,
1011101070000000,
00000830,
3330,
33, //74

6,
000000000000000000000008000000000000100010001000,
6,
000000000000000000000008000000000000100010001000,

6,
000000000000000000000000000000000000000000000008,
#MEASURE 2/4
00000011,
#BPMCHANGE 110
13,
#MEASURE 4/4
#BPMCHANGE 100
3,

#BPMCHANGE 160
#GOGOSTART
1111111111111111, //84

1011101110111011,
#BPMCHANGE 170
1011101110111011,
#BPMCHANGE 180
1011101110111011,
#BPMCHANGE 190
1011101110111011,
#BPMCHANGE 200
1011101110111011, //89

#BPMCHANGE 210
1010101011101010,
#BPMCHANGE 220
1011101010111010,
#BPMCHANGE 230
1011101010111010,
#BPMCHANGE 240
1011101010111010,

#BPMCHANGE 250
1110111011101110,
#BPMCHANGE 260
1110111011101110,
#BPMCHANGE 270
1110111011101110,
#BPMCHANGE 280
1110111011101110, //97

#MEASURE 5/4
#BPMCHANGE 350
11111,
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 280
3,
#GOGOSTART
3,
111111111111,
10000011,
#GOGOEND

1,
0,
0, //105
#END


COURSE:Hard
LEVEL:7
BALLOON:25,20
SCOREINIT:2560
SCOREDIFF:0


#START
100001100101,
100000000101,
1111,
1,

100001100101,
100000100101,
1111,
1, //8

1001101010001000,
1001101010001010,
1001101010001000,
1001101010003000,

30003033,
30003033,
3333,
#BPMCHANGE 200
1110, //16

1111,
1111,
1111,
1111, //20

#BPMCHANGE 400
1111,
1111,
1111,
1111,

1111,
1111,
500000000000000000000000000000000000000000000008,
#MEASURE 2/4
#BPMCHANGE 100
#BARLINEOFF
0, //28

#BPMCHANGE 120
#BARLINEON
1011,
#BPMCHANGE 140
1011,
#BPMCHANGE 160
1111,
#BPMCHANGE 180
1011,

#BPMCHANGE 200
1011,
#BPMCHANGE 220
1111,
#BPMCHANGE 240
1011,
#BPMCHANGE 125
#SCROLL 1.92
1
#SCROLL 1
1, //36

#MEASURE 4/4
10101011,
10101033,
3333,
600000000000000000000000000000000008000000000000, //40

#MEASURE 3/4
101101100,
1,
100101101,
1,
101101100, //45

#MEASURE 4/4
#BPMCHANGE 220
#GOGOSTART
7,
0,
0,
8, //49
#GOGOEND

11111011,
10111010,
11111011,
10111010,

11111011,
10111010,
11111011,
11111000, //57

11111011,
1011101010001010,
11111011,
1011101010001000,

11110000,
0,
#MEASURE 2/4
0,
#BPMCHANGE 125
01, //65

#MEASURE 4/4
11,
1,
0,
3330,

10001011,
17,
00000830,
3330,
33, //74

6,
000000000000000000000008000000000000100010001000,
6,
000000000000000000000008000000000000100010001000,

6,
000000000000000000000000000000000000000000000008,
#MEASURE 2/4
00000011,
#BPMCHANGE 110
13,
#MEASURE 4/4
#BPMCHANGE 100
3,

#BPMCHANGE 160
#GOGOSTART
0, //84

1111,
#BPMCHANGE 170
1111,
#BPMCHANGE 180
1011100010111000,
#BPMCHANGE 190
1011100010111000,
#BPMCHANGE 200
1011100010111000, //89

#BPMCHANGE 210
1010101011101010,
#BPMCHANGE 220
1010101010111010,
#BPMCHANGE 230
1010101010111010,
#BPMCHANGE 240
1010101010111010,

#BPMCHANGE 250
11111111,
#BPMCHANGE 260
11111111,
#BPMCHANGE 270
11111111,
#BPMCHANGE 280
11111111, //97

#MEASURE 5/4
#BPMCHANGE 350
11111,
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 280
3,
#GOGOSTART
3,
0,
30000011,
#GOGOEND

1,
0,
0, //105
#END


COURSE:Normal
LEVEL:6
BALLOON:19,15
SCOREINIT:3580
SCOREDIFF:0

#START
1001,
1001,
1011,
1,

1001,
1001,
1011,
1, //8

11,
1111,
11,
1113,

33,
30003033,
3333,
#BPMCHANGE 200
1110, //16

1110,
1001,
1110,
1, //20

#BPMCHANGE 400
1110,
1110,
1110,
1110,

1110,
1110,
500000000000000000000000000000000000000000000008,
#MEASURE 2/4
#BPMCHANGE 100
#BARLINEOFF
0, //28

#BPMCHANGE 120
#BARLINEON
1011,
#BPMCHANGE 140
1011,
#BPMCHANGE 160
1011,
#BPMCHANGE 180
1011,

#BPMCHANGE 200
1011,
#BPMCHANGE 220
1011,
#BPMCHANGE 240
1011,
#BPMCHANGE 125
#SCROLL 1.92
1
#SCROLL 1
1, //36

#MEASURE 4/4
10001011,
10001033,
3333,
600000000000000000000000000000000008000000000000, //40

#MEASURE 3/4
101,
1,
101,
1,
1, //45

#MEASURE 4/4
#BPMCHANGE 220
#GOGOSTART
9,
0,
0900,
8, //49
#GOGOEND

10101011,
1110,
10101011,
1110,

10101011,
10101011,
10101011,
1110, //57

10101011,
10111010,
10101011,
10111010,

11110000,
0,
#MEASURE 2/4
0,
#BPMCHANGE 125
01, //65

#MEASURE 4/4
11,
1,
0,
3330,

10001011,
17,
00000830,
3330,
33, //74

6,
000000000000000000000008000000000000300000000000,
6,
000000000000000000000008000000000000300000000000,

6,
000000000000000000000000000000000000000000000008,
#MEASURE 2/4
0,
#BPMCHANGE 110
33,
#MEASURE 4/4
#BPMCHANGE 100
3,

#BPMCHANGE 160
#GOGOSTART
0, //84

1111,
#BPMCHANGE 170
1111,
#BPMCHANGE 180
1111,
#BPMCHANGE 190
1111,
#BPMCHANGE 200
1111, //89

#BPMCHANGE 210
11111011,
#BPMCHANGE 220
10111011,
#BPMCHANGE 230
10111011,
#BPMCHANGE 240
10111011,

#BPMCHANGE 250
11111011,
#BPMCHANGE 260
11111011,
#BPMCHANGE 270
11111011,
#BPMCHANGE 280
11111111, //97

#MEASURE 5/4
#BPMCHANGE 350
11111,
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 280
3,
#GOGOSTART
3,
0,
30000011,
#GOGOEND

1,
0,
0, //105
#END


COURSE:Easy
LEVEL:5
BALLOON:15,10
SCOREINIT:4830
SCOREDIFF:0

#START
1,
1,
11,
1,

1,
1,
11,
1, //8

11,
1110,
11,
1110,

33,
33,
3333,
#BPMCHANGE 200
1110, //16

11,
1,
11,
1, //20

#BPMCHANGE 400
11,
1,
11,
1,

11,
1,
500000000000000000000000000000000000000000000008,
#MEASURE 2/4
#BPMCHANGE 100
#BARLINEOFF
0, //28

#BPMCHANGE 120
#BARLINEON
11,
#BPMCHANGE 140
11,
#BPMCHANGE 160
11,
#BPMCHANGE 180
11,

#BPMCHANGE 200
11,
#BPMCHANGE 220
11,
#BPMCHANGE 240
11,
#BPMCHANGE 125
#SCROLL 1.92
1
#SCROLL 1
1, //36

#MEASURE 4/4
11,
11,
3333,
600000000000000000000008000000000000000000000000, //40

#MEASURE 3/4
1,
1,
1,
1,
1, //45

#MEASURE 4/4
#BPMCHANGE 220
#GOGOSTART
9,
0,
0900,
8, //49
#GOGOEND

1110,
1110,
1110,
1110,

1111,
1110,
1111,
1110, //57

1111,
1111,
1111,
1111,

11110000,
0,
#MEASURE 2/4
0,
#BPMCHANGE 125
01, //65

#MEASURE 4/4
11,
1,
0,
3330,

11,
17,
00000830,
3330,
33, //74

6,
000000000000000000000008000000000000300000000000,
6,
000000000000000000000008000000000000300000000000,

6,
000000000000000000000000000000000000000000000008,
#MEASURE 2/4
0,
#BPMCHANGE 110
33,
#MEASURE 4/4
#BPMCHANGE 100
3,

#BPMCHANGE 160
#GOGOSTART
0, //84

1111,
#BPMCHANGE 170
1111,
#BPMCHANGE 180
1111,
#BPMCHANGE 190
1111,
#BPMCHANGE 200
1111, //89

#BPMCHANGE 210
1111,
#BPMCHANGE 220
1111,
#BPMCHANGE 230
1111,
#BPMCHANGE 240
1111,

#BPMCHANGE 250
1111,
#BPMCHANGE 260
1111,
#BPMCHANGE 270
1111,
#BPMCHANGE 280
1111, //97

#MEASURE 5/4
#BPMCHANGE 350
11111,
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 280
3,
#GOGOSTART
3,
0,
30000011,
#GOGOEND

1,
0,
0, //105
#END