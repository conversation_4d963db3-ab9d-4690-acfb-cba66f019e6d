//TJADB Project
TITLE:<PERSON>baneito
TITLEJA:束ね糸
SUBTITLE:--<PERSON><PERSON><PERSON> feat. <PERSON><PERSON>UBTITLEJA:はるなば feat.石黒千尋
BPM:270
WAVE:<PERSON>baneit<PERSON>.ogg
OFFSET:-1.978
DEMOSTART:58.755

//Shinuchi: 2920/2330/1400/980

COURSE:Oni
LEVEL:10
BALLOON:10,9
SCOREINIT:350,1030
SCOREDIFF:88

#START
100100100100000100101010,
22112210,
11110110,
22112210,

11110110,
200200100100200200101010,
11110110,
22112211, //8

21112101,
200100100100200000101010,
21112101,
21112210,

21112101,
200100100100200100101010,
22121211,
22121012, //16

02112211,
21120202,
0010101110201010,
1011202010201040,

0000400011112000,
21212112,
02121212,
1010202011112020,
1000201010112000,
0, //26

10201120,
10201121,
00201120,
1110201011102000,

10201120,
10201121,
0222,
1010101010101111, //34

20201120,
10201121,
00201120,
1110201011102000,

40111111,
2000102022100040,
00400400,
40040040, //42

10210212,
1000201110201110,
2000112010102020,
10210212,

1000201110201110,
2000102011112020,
10210212,
1000201110201110, //50

2000112010102020,
10210212,
1000201110201110,
2000102011112010,

200100220100,
200100220100,
200100220100,
400100220100,

40111111,
2220102011102010,
4000101020221020,
0020101020221020,
0020101011101070,
000000000000000008000400, //64

#GOGOSTART
01221212,
1010202010201120,
0010202010201022,
1020102010221020,

0010200010001111,
1010202010102222,
1010202010202222,
1010202010221020, //72

0010200010001111,
1010202010201120,
0010200010201022,
1020102010201120,

0010200010001111,
1010202010101120,
0020102010201111,
1020102011221020, //80

0020102210221120,
1022104000400040,
02111222,
11221212,

0010200011112000,
1010200010201120,
0010202010201120,
1111222210101020, //88

0020101120221020,
1022102010221020,
1010102010201022,
1020102210101070,

000000000000000000000000000008000000100100100100,
10221114,
04,
4343, //96
#GOGOEND

4,
2022,
42,
2022,

42,
22,
1000101110111010,
2010201022111020, //104

0020111020201010,
2020111020201020,
0020111020201110,
2020111020221020,

0020111020201110,
2020111020221020,
02121212,
300000000000300000000000600000000000000008000000, //112

#GOGOSTART
11210122,
11210220,
11210122,
11210220,

11210122,
11210220,
1010201000102220,
1020102010101111, //120

2010201020112020,
1010201120112020,
1010201020112020,
1010201120212020,

1010201020112020,
1010201120112020,
1022102210221120,
500000000000000000000000000000000008000000300000, //128
#GOGOEND

02112211,
21120202,
0010101110201010,
1011202010201040,

0000400011112000,
2011201120111120,
0022102210221020,
1111221020200000,

0,
0,
0, //139
#END


COURSE:Hard
LEVEL:8
BALLOON:8,7
SCOREINIT:400,1500
SCOREDIFF:95

#START
10110110,
11101120,
10110110,
11102210,

10110110,
11101120,
10110110,
11102210, //8

11101101,
11011010,
11101101,
11011020,

11101101,
11011010,
11101101,
11011012, //16

01102210,
21120202,
01102210,
22111114,

000000000000400000000000500000000008000000000000,
11101112,
02101122,
11221110,
10010040,
0, //26

10201120,
10201121,
0212,
10210120,

10201120,
10201121,
0222,
10010010, //34

10201120,
10201121,
00201120,
10210120,

40111111,
20201104,
00400400,
40040040, //42

10210210,
10120120,
4,
10210210,

10120120,
4,
10210210,
10120120, //50

4,
10210210,
10120120,
40111111,

20102210,
20102210,
20102210,
40102210, //58

4,
44,
40102012,
02102012,
02101117,
000000000000000008000400, //64

#GOGOSTART
01201120,
11201122,
01201120,
11201122,

01201120,
11201122,
01201120,
11201112, //72

01201120,
11201122,
01201120,
11201112,

01201120,
11201112,
02101210,
12101112, //80

02101210,
12040404,
00222222,
11111112,

01201120,
11201212,
01201210,
12101112, //88

02102210,
12101212,
01101210,
12101117,

000000000000000000000000000008000000100000100000,
10102014,
04,
4343, //96
#GOGOEND

4,
2022,
42,
2022,

42,
22,
10111111,
20201112, //104

00112210,
20102212,
02102210,
20102212,

02102210,
20102212,
02101212,
300000000000300000000000600000000000000008000000, //112

#GOGOSTART
10210120,
10210220,
10210120,
10210220,

10210120,
10210220,
10210120,
500000000000000000000008000000000000000000000000, //120

11210120,
11210220,
11210120,
11210220,

11210120,
11210220,
12101210,
500000000000000000000000000000000008000000300000, //128
#GOGOEND

01102210,
21120202,
01102210,
22111114,

000000000000400000000000500000000008000000000000,
11101112,
02101122,
11112200,

0,
0,
0, //139
#END


COURSE:Normal
LEVEL:7
BALLOON:8,7,14,5,5,8
SCOREINIT:540,2720
SCOREDIFF:130

#START
10110110,
1110,
10110110,
1120,

10110110,
1110,
10110110,
1120, //8

10101101,
1110,
10101101,
1120,

10101101,
1110,
10101101,
10102022, //16

0,
00020202,
07,
000000000000000000000000000000000008000000400000,

0411,
500000000000000000000000000008000000000000400000,
00101110,
100000100000100000000000500000000000000008000000,
10010040,
0, //26

1101,
10101101,
0101,
10010010,

1101,
10101107,
0,
8, //34

1101,
10101101,
0101,
10010010,

1103,
30003004,
00400400,
40040040, //42

10010010,
10200200,
4,
10010010,

10200100,
4,
10010010,
10200200, //50

4,
10010010,
10200100,
4,

1111,
2222,
1111,
2222, //58

4,
44,
600000000000000000000000000000000000000000000008,
00070000,
0,
00000804, //64

#GOGOSTART
0121,
20102011,
01,
100000000000000000000000500000000000000000000008,

0021,
20102011,
01,
100000000000000000000000500000000000000000000008, //72

0021,
20102011,
01,
10001007,

000000000000000000000000000008000000100000000000,
10001002,
01,
5, //80

000008000000000000000000000000000000000000000000,
00040404,
0111,
500000000000000000000000000000000008000000400000,

0402,
20202022,
02,
20002022, //88

0121,
20102011,
01,
10001007,

000000000000000000000000000008000000100000000000,
10001004,
04,
4343, //96
#GOGOEND

4,
2022,
42,
2022,

42,
22,
1111,
20202004, //104

00101110,
10000004,
00101110,
20000004,

00101110,
10000004,
00101110,
300000000000300000000000600000000000000008000000, //112

#GOGOSTART
1202,
10200220,
1202,
10200220,

1202,
10200220,
1202,
500000000000000000000008000000000000000000000000, //120

1111,
20200220,
1111,
10200220,

1111,
10200220,
1111,
500000000000000000000000000000000008000000300000, //128
#GOGOEND

0,
00020202,
07,
000000000000000000000000000000000008000000400000,

0411,
500000000000000000000000000008000000000000200000,
00202220,
11101100,

0,
0,
0, //139
#END


COURSE:Easy
LEVEL:5
BALLOON:7,6,7,12,7
SCOREINIT:360,3410
SCOREDIFF:85

#START
10110010,
11,
10110010,
12,

10110010,
11,
10110010,
12, //8

10101100,
11,
10101100,
12,

10101100,
11,
10101100,
10002002, //16

0,
00020202,
07,
000000000000000000000000000000000008000000400000,

0400,
500000000000000000000000000008000000000000400000,
0111,
1110,
10010040,
0, //26

1001,
10001001,
0001,
10010010,

1001,
10001007,
0,
8, //34

1001,
10001001,
0001,
10010010,

1003,
30003007,
0,
000000000000080000000000000000000000000000000000, //42

10010010,
10100100,
3,
20020020,

20200200,
4,
10010010,
10100100, //50

3,
20020020,
20200200,
4,

1110,
1110,
1110,
1110, //58

4,
44,
600000000000000000000000000000000000000000000008,
00070000,
0,
000000000000000800000400, //64

#GOGOSTART
0101,
00100011,
0101,
000000000000100000000000500000000000000000000008,

0101,
00100011,
0101,
000000000000100000000000500000000000000000000008, //72

0101,
00100011,
0101,
00100015,

000000000000000008000000000000000000100000000000,
10001002,
01,
5, //80

000008000000000000000000000000000000000000000000,
00040404,
0,
00000004,

0402,
00200022,
02,
22, //88

0101,
00100011,
0101,
00100015,

000000000000000008000000000000000000100000000000,
10001004,
04,
3333, //96
#GOGOEND

4,
2022,
42,
2022,

42,
22,
1110,
20202004, //104

0121,
10000004,
0121,
20000004,

0121,
10000004,
0111,
1130, //112

#GOGOSTART
1202,
1202,
1202,
1202,

1202,
1202,
1202,
500000000000000000000008000000000000000000000000, //120

1101,
1202,
1101,
1202,

1101,
1202,
1101,
500000000000000000000000000000000008000000300000, //128
#GOGOEND

0,
00020202,
07,
000000000000000000000000000000000008000000400000,

0400,
500000000000000000000000000008000000000000200000,
0222,
10101100,

0,
0,
0, //139
#END