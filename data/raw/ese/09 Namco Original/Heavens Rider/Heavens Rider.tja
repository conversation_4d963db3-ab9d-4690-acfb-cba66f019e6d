//TJADB Project
TITLE:Heaven’s Rider
TITLEJA:Heaven's Rider
SUBTITLE:--Rio Hamamoto
SUBTITLEJA:濱本理央(BNSI)
BPM:150
WAVE:Heavens Rider.ogg
OFFSET:-1.749
DEMOSTART:40.140

//Shinuchi: 5130/3870/2010/1390

COURSE:Oni
LEVEL:9
BALLOON:
SCOREINIT:460,1390
SCOREDIFF:118

#START
1020100010201202,
1020100010202202,
1020100010201202,
1020100010202202,

1020100010201202,
1020120120120120,
1020100010201202,
100000200000100000000000500000000000000008000000, //8

1020120210221202,
1020120220221202,
1020120210221202,
1020120220221202,

1120120210221202,
1120120220221202,
1120120210221202,
1120120210221120, //16

1002202020020020,
000500000000000000000008000000000000200000200200,
0002002020020222,
500000000000000000000000000008000000000000000000,

2002202020020020,
000500000000000000000008000000000000200000200200,
0002002020020222,
101010200200101010200200101010200200101010200200, //24

#GOGOSTART
1010220210102202,
1010220210002222,
1010220210102202,
1011220220220000,

1010220210112202,
1120120120120120,
1010220210112202,
1010220211202002, //32

101010200000100200000200100000200200100200000200,
101010200000100200200200100000000000400000000000,
101010200000100200000200100000200200100200000200,
101010200000100200000100200000100200000100200200,

101010200000100200000200100000200200100200000200,
101010200000100200000100200000100200000100200200,
101010200000100200000200100000200200100200000200,
101010200000100200000200100000000000000000000000, //40
#GOGOEND

2020220202020120,
2020211202020202,
2020202022021202,
0220202012021220,

2020220202020211,
2020120120120120,
1020202022020202,
0220202022221222, //48

1100001102112000,
1100001102212002,
1100001102112000,
1100001102212002,

2200002202112000,
2200002202212002,
2200002202112000,
2200002202212002, //56

1002202020020020,
000500000000000000000008000000000000200000200200,
0002002020020222,
101010200200101010200200101010200200101010200200,

1000200210002000,
00204005,
000008000000500000000008000000000000000000000000,
34,

2000200400000000,
4000202200200200,
1121020202021202,
100100200100000200000200100200000200101010100200, //68

#GOGOSTART
1001201202112202,
0011201202211202,
1001201202211212,
1011201202212212,

1001201202112212,
100000000100200000100200000200100200102010201020,
1001201202112202,
101010100200100000100200000200200200101010100200,
1,
#GOGOEND
0,
0, //79
#END


COURSE:Hard
LEVEL:8
BALLOON:8,8,9,10
SCOREINIT:580,2130
SCOREDIFF:148

#START
12101010,
12102020,
12101010,
12102020,

12101010,
1020300300300300,
12101010,
100000200000100000000000500000000000000008000000, //8

1010110110001000,
1010110110002000,
1010110110001000,
1010110110002000,

1010110110101000,
1010110110202000,
1010110110101000,
1010110110001020, //16

1002202020020020,
000500000000000000000008000000000000200000200200,
0002002020020222,
500000000000000000000000000008000000000000000000,

2002202020020020,
000500000000000000000008000000000000200000200200,
0002002020020222,
700000000000000000000000000000080000000000000000, //24

#GOGOSTART
1020100110102000,
12101000,
1020100110102000,
1020100110110000,

1020100110102000,
1020110110110110,
1020100110102000,
1020100110110000, //32

1110200011102000,
1110200040000000,
1110200011102000,
1110200030030030,

1110200011102000,
1020110110110110,
1110200011102000,
1110200030000000, //40
#GOGOEND

1010220010102200,
1010220030004000,
1010220010102200,
1010220011101110,

1010220010102200,
1010220220220220,
1010220011102200,
1010220022202220, //48

1100001101101000,
1100001101101000,
1100001101101000,
1100001101101000,

1100002202202000,
1100002202202000,
1100002202202000,
1100002202202000, //56

1002202020020020,
000500000000000000000008000000000000200000200200,
0002002020020222,
700000000000000000000000000000080000000000000000,

1212,
00204005,
000008000000500000000008000000000000000000000000,
34,

2000200400000000,
4000200200200200,
1101010101010100,
7008, //68

#GOGOSTART
1020110110002000,
1020110110000050,
000000000000000000000008000000000000100100100100,
1000200011102000,

1020110110000070,
0008,
1020110110102020,
1110102201102222,
1,
#GOGOEND
0,
0, //79
#END


COURSE:Normal
LEVEL:6
BALLOON:5,6,2,6,7,8
SCOREINIT:820,4660
SCOREDIFF:225

#START
1112,
1120,
1112,
1122,

1112,
1708,
1112,
100000000000100000000000500000000000000008000000, //8

1212,
10201110,
1212,
10201120,

1212,
10201120,
1212,
10201120, //16

12,
000500000000000000008000000000000000200000000000,
12,
500000000000000000000000000008000000000000000000,

12,
000500000000000000008000000000000000200000000000,
12,
700000000000000000000000000000080000000000000000, //24

#GOGOSTART
11201110,
11201000,
11201110,
11201100,

11201110,
100000100000200000000000500000000000000008000000,
3,
1210, //32

3434,
3440,
3434,
300000000000400000000000600000000000000008000000,

3434,
300000000000600000000000000000000000000008000000,
3,
3430, //40
#GOGOEND

1202,
0212,
1202,
0212,

1202,
000000000000500000000000000000000000000008000000,
1202,
1278, //48

30010010,
30010010,
30010010,
30010010,

40020020,
40020020,
40020020,
40020020, //56

12,
000500000000000000008000000000000000200000000000,
12,
700000000000000000000000000000080000000000000000,

1202,
00202005,
000008000000500000000008000000000000000000000000,
12,

22,
2000200200200000,
500000000000000000000000000000000008000000000000,
7008, //68

#GOGOSTART
12101020,
10102005,
000000000000000000000008000000000000000000000000,
1022,

12102007,
0008,
12101120,
600000000000000000000000000000000000000008000000,
3,
#GOGOEND
0,
0, //79
#END


COURSE:Easy
LEVEL:5
BALLOON:3,4,3,4,4
SCOREINIT:620,6100
SCOREDIFF:208

#START
11,
1120,
11,
1120,

11,
1708,
11,
1120, //8

1210,
1210,
1210,
1112,

1210,
1112,
1210,
1112, //16

12,
000500000000000000000008000000000000000000000000,
12,
500000000000000000000000000008000000000000000000,

12,
000500000000000000000008000000000000000000000000,
12,
700000000000000000000000000000080000000000000000, //24

#GOGOSTART
1211,
1210,
1211,
1210,

1211,
1210,
1,
1110, //32

3330,
3340,
3333,
3430,

3333,
300000000000600000000000000008000000000000000000,
3,
3430, //40
#GOGOEND

1202,
0202,
0202,
0210,

1202,
0202,
0202,
0210, //48

10010000,
10010000,
10010000,
10010000,

20020000,
20020000,
20020000,
20020000, //56

12,
000500000000000000000008000000000000000000000000,
12,
700000000000000000000000000000080000000000000000,

1212,
00102005,
000008000000500000000008000000000000000000000000,
12,

22,
2,
500000000000000000000000000000000008000000000000,
7008, //68

#GOGOSTART
1112,
10102005,
000000000000000000000008000000000000000000000000,
0012,

10102007,
0008,
3,
600000000000000000000000000008000000000000000000,
3,
#GOGOEND
0,
0, //79
#END