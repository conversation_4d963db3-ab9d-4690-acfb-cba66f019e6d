//TJADB Project
TITLE:Shoutoku Taiko no “<PERSON> <PERSON><PERSON><PERSON> Made Asuka”
TITLEJA:聖徳たいこの「日いずるまで飛鳥」
SUBTITLE:--<PERSON><PERSON><PERSON> (IOSYS) feat. miko
SUBTITLEJA:コバヤシユウヤ(IOSYS) feat.miko
BPM:100
WAVE:Shoutoku Taiko no Hi I<PERSON>ru Made Asuka.ogg
OFFSET:-2.519
DEMOSTART:50.381

//Technical Limitation: Inaccurate branching behaviour for the 5-7-4 gimmick in Oni.

//Shinuchi: 5170/2270/1610/1160

COURSE:Oni
LEVEL:9
BALLOON:10,5,5,5,10
SCOREINIT:400,1230
SCOREDIFF:95

#START
100202100200100202102200,
100202100200000202102200,
100202100200100202122202,
#BPMCHANGE 160
#SCROLL 0.62
7008,
#SCROLL 1
2000200020112210, //5

3001201002112210,
1001201002112211,
1001201002112210,
1021001002012222, //9

1001201002112210,
1001201002112211,
1001001001002210,
3000000022010020, //13

#SECTION
12022022,
500000000008000000200200100000000100000000100000,
2021002020102020, //16

#BRANCHSTART r,5,6
#N
#SECTION
100000000100000000200000500000000008000000100200,
#LEVELHOLD
1000202210201120, //N18
#E
#SECTION
100000000100000000200000500000000008000000100200,
1000202210201120, //E18
#M
#SECTION
100000000100000000200000500000000008000000100200,
#LEVELHOLD
1000202210201120, //M18

#BRANCHSTART r,7,8
#N
500000000000800000200200100000200200200200200200,
#LEVELHOLD
1010202210201020, //N20
#E
500000000000800000200200100000200200200200200200,
1010202210201020, //E20
#M
500000000000800000200200100000200200200200200200,
#LEVELHOLD
1010202210201020, //M20

#BRANCHSTART r,11,12
#N
1001001070000800,
10010120,
1001001000102000,
10010120,
1001001000304000, //N25
#E
1001001070000800, //E21
100
#SCROLL 1.25
20
#SCROLL 1.5
2
#SCROLL 1
1
#SCROLL 1.25
20
#SCROLL 1.5
2
#SCROLL 1
10200
#SCROLL 1.25
1, //E22
#SCROLL 1
10
#SCROLL 1.5
2
#SCROLL 1
10
#SCROLL 1.25
2
#SCROLL 1
1
#SCROLL 1.5
2
#SCROLL 1.25
20
#SCROLL 1
1020
#SCROLL 1.25
2
#SCROLL 1.5
1, //E23
#SCROLL 1
1
#SCROLL 1.25
2
#SCROLL 1.5
2
#SCROLL 1.25
202
#SCROLL 1
1
#SCROLL 1.25
20
#SCROLL 1.5
2
#SCROLL 1
10200
#SCROLL 1.25
1, //E24
#SCROLL 1
1001001000304000, //E25
#M
1001001070000800,
1222201202102120,
1221221202102222,
1222201202112122,
1221001000304000, //M25

#BRANCHSTART p,101,102
#N
1020102210201022, //N26
#E
1020102210201022, //E26
#M
1020102210201022, //M26
#BRANCHEND

1022122010201022,
1000201110002011,
1010200010210010,
1022102010221020,
1100001100000000, //31

#GOGOSTART
3021002010201120,
1021002010201122,
1020102210201020,
0020201022112211, //35

1021002010201120,
1021002010201122,
1022102010221120,
11110340, //39

1020112010201120,
1021012010201120,
1020112010201120,
0020201022112121, //43

1021012010221020,
3003003000000020,
1020112030030030,
0000002022020020, //47
#GOGOEND

1001201000201121,
1001201000201122,
1001201002211212,
1001001000000000, //51

#BPMCHANGE 200
#SCROLL 0.8
3004, //52

#SCROLL 1
1020112010120211,
1020112011210122,
1020112010120210,
1120112010221120, //56

1020112010120211,
1020112011211202,
1020112010112010,
1012221121112222, //60

1020112010120211,
1020112011211004,
0004000400000000,
1022102210222222, //64

1020112010112010,
1020101122222010,
2211102211112210,
#BPMCHANGE 160
#SCROLL 1.25
3, //68

#SCROLL 1
#GOGOSTART
3000000000100220,
30000122,
3020102210201020,
0020102211221212, //72

1021002010201120,
1021002010201120,
1022102010221212,
11110340, //76

1020112010201120,
1021012010201120,
1020112010201120,
0020201022112121, //80

1021012010221020,
3003003000000020,
1020112030030030,
0000002022020020, //84
#GOGOEND

3001201002112210,
1001201002112212,
1001201002112210,
1021001002012222, //88

1001201002112210,
1001201002112212,
1001001001002210,
3700,
8300,
0, //94
#END


COURSE:Hard
LEVEL:7
BALLOON:10,5,8
SCOREINIT:430,1770
SCOREDIFF:105

#START
100200000200100200102200,
100200000200100200102200,
100200000200100202200200,
#BPMCHANGE 160
#SCROLL 0.62
7008,
#SCROLL 1
2000200020202220, //5

3001002002001110,
1001002002002220,
1001002002002220,
1001001001004000,

1001002002001110,
1001002002002220,
1001001001002220,
3,

30002022,
500000000008000000100100100000000000000000000000,
30022220, //16

500000000000000008000000300000000000000000000000,
0000300010201110,
1020102010202220,
1,
300000000300000000300000600000000000000008000000, //21

10010120,
1001001000102000,
10010120,
1001001000304000, //25

1000102010201110,
1000102010202220,
1000201110002011,
1010200040040040,
600000000000000000000000000000000008000000000000,
1100001100000000, //31

#GOGOSTART
3000102010201110,
1000102010201110,
1000102010222010,
0020201022202220,

1000102010201110,
1000102010201110,
1022202010222020,
11110440,

1020111010201110,
1020111010201110,
1020111010222010,
0020201022202220,

1020111010202220,
3003003000000000,
1020111030030030,
0, //47
#GOGOEND

1001002002001110,
1001002002002220,
1001002002002220,
1001007000000800,

#BPMCHANGE 200
#SCROLL 0.8
3,
#SCROLL 1
10101110,
1000100010101110,
10101110,

1010111011101000,
10101110,
1000100010101110,
10101110,

1010111011101000,
10101110,
1000100011101000,
0,

1000100010101110,
10101110,
1000100011101000,
1010101010101110,
#BPMCHANGE 160
#SCROLL 1.25
3, //68

#SCROLL 1
#GOGOSTART
3000000000200200,
3002,
3000102010222010,
0020201022202220,

1000102010201110,
1000102010201110,
1022202010222020,
11110440,

1020111010201110,
1020111010201110,
1020111010222010,
0020201022202220,

1020111010201110,
3003003000000000,
1020111030030030,
0, //84
#GOGOEND

3001002002001110,
1001002002002220,
1001002002002220,
1001001001004000,

1001002002001110,
1001002002002220,
1001002002002020,
300000000000700000000000000000000000000000000008,
0300,
0, //94
#END


COURSE:Normal
LEVEL:6
BALLOON:4
SCOREINIT:460,2800
SCOREDIFF:125

#START
0111,
500000000008000000100000000000000000000000000000,
0111,
#BPMCHANGE 160
#SCROLL 0.62
500000000000000000000000000000000000000008000000,
#SCROLL 1
0, //5

10200120,
10200120,
10200120,
3003003003003000,

10200120,
10200120,
1111,
3,

3022,
500000000008000000000000300000000000000000000000,
1022, //16

500000000008000000000000300000000000000000000000,
1012,
100000100000200000000000500000000008000000000000,
1,
3003003030000000,

10010020,
10010220,
10010020,
10010340,

10121010,
10121010,
3434,
3000400040040040,
600000000000000000000000000000000008000000000000,
30030000, //31

#GOGOSTART
10121210,
10121210,
10121212,
0440,

10121210,
10121210,
500000000000000008000000500000000000000008000000,
33330440,

12121210,
12121210,
12121222,
0440,

10121210,
3003003000000000,
1020100030030030,
0, //47
#GOGOEND

10210120,
10210120,
10210120,
1001007000000800,
#BPMCHANGE 200
#SCROLL 0.8
3,

#SCROLL 1
10001110,
11101010,
10001110,
11111110,

10001110,
11101010,
10001110,
3333,

10001110,
11101010,
0,
3333,

10001110,
10001110,
1111,
#BPMCHANGE 160
#SCROLL 1.25
3, //68

#SCROLL 1
#GOGOSTART
30000400,
3004,
10121212,
0440,

10121210,
10121210,
500000000000000008000000500000000000000008000000,
33330440,

12121210,
12121210,
12121222,
0440,

10121210,
3003003000000000,
1020100030030030,
0, //84
#GOGOEND

10210120,
10210120,
10210120,
3003003003003000,

10210120,
10210120,
1111,
3,

0300,
0, //94
#END


COURSE:Easy
LEVEL:4
BALLOON:5,8,4,5
SCOREINIT:590,5800
SCOREDIFF:190

#START
11,
500000000000000000000008000000000000000000000000,
11,
#BPMCHANGE 160
#SCROLL 0.62
500000000000000000000000000000000000000008000000,
#SCROLL 1
0, //5

1011,
1011,
1011,
500000000000000000000000000008000000000000000000,

1011,
1011,
1011,
3,

1,
500000000000000000000000000008000000000000000000,
1, //16

500000000000000000000000000008000000000000000000,
11,
1110,
3,
7008, //21

1,
12,
1,
14,

1011,
1022,
2303,
0300,
9009,
8, //31

#GOGOSTART
1011,
1022,
20001005,
000000000000000000000000000008000000000000000000,

1011,
1022,
2022,
78,

1011,
1022,
20001005,
000000000000000000000000000008000000000000000000,

1011,
10030000,
10101003,
0, //47
#GOGOEND

1,
12,
1,
10010000,
#BPMCHANGE 200
#SCROLL 0.8
3, //52

#SCROLL 1
1,
1110,
1,
2220,

1,
1110,
1,
2222,

1,
1110,
0,
2222,

1,
1110,
7008,
#BPMCHANGE 160
#SCROLL 1.25
3, //68

#SCROLL 1
#GOGOSTART
10000100,
1,
10001005,
000000000000000000000000000008000000000000000000,

1011,
1022,
2011,
10030000,

1011,
1022,
20001005,
000000000000000000000000000008000000000000000000,

1011,
10030000,
10102003,
0, //84
#GOGOEND

1011,
1011,
1022,
500000000000000000000000000008000000000000000000,

1011,
1011,
1022,
3,

0300,
0, //94
#END