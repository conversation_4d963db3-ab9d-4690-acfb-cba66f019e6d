//TJADB Project
TITLE:Monochrome Utopia
TITLEJA:モノクロームユートピア
SUBTITLE:--<PERSON><PERSON><PERSON>(BNSI) feat. KOCHO
SUBTITLEJA:Shogo Nomura(BNSI) feat. KOCHO
BPM:112
WAVE:Monochrome Utopia.ogg
OFFSET:-3.690
DEMOSTART:3.690


COURSE:Oni
LEVEL:10
BALLOON:8,23,23
SCOREINIT:1460
SCOREDIFF:0

#START
#SCROLL 0.99
110022002100,
#BARLINEOFF
221220200000101020102210,
101000102020102200102010,
100010002010000022121000, //4

100000000000100000000000100000001000102002002000,
100020100100202010102010,
100000002212100010102000,
700000008000122120201220, //8

101000200010220010210000,
202010201000200010000010002002001000100020101000,
100020000000100020002000100020001010201120001000,
100020002010200022121020, //12

100002010000100010100000,
100220002000102020100000200020001000100020001000,
100100102212102020102000, //15

070000000000000000800000,
101022202020122211222110,
#BARLINEON
102020201000001000001000200010202020200010112000,
#BARLINEOFF
100000102210100020202220, //19

#BARLINEON
#SCROLL 1.5
300120100020101020121220,
100121021122120120122211,
#MEASURE 2/4
202212021211,
#MEASURE 4/4
122102100100210121121210, //23

#GOGOSTART
120201202220122102101210,
120201202210122101202102, //25
#GOGOEND

#MEASURE 5/4
100020111010201110102020
#BPMCHANGE 168
#SCROLL 0.99
200200100000200000200200100022002000,
#MEASURE 6/4
200020001000100010002000100100200100100200200200100000100100200000000000, //27

#MEASURE 4/4
#BPMCHANGE 112
#SCROLL 1.5
100010201020102012201022,
#MEASURE 9/8
102020201000
#BPMCHANGE 167.2
#SCROLL 0.99
202020100200200200
#BPMCHANGE 112
#SCROLL 1.5
100020201020202010002000, //29

#MEASURE 4/4
100010201222102010203000,
100010101010121012121222,
#GOGOSTART
100010201102200201102202,
100120101012101011022022, //33
#GOGOEND

100020112020101120111122,
#MEASURE 5/4
#BPMCHANGE 168
#SCROLL 0.99
100100200100201010100200000000200000
#BPMCHANGE 112
#SCROLL 1.5
001020101000101000202020, //35

#MEASURE 4/4
#GOGOSTART
100020100000200010002022100020100020200010102020,
120001112022121202202111,
120120101202201122102202,
120120101012101021021122, //39

121022001020120122020211,
102020101020102010001000202020001010101010202011,
200020101020101020021101,
002000200020211000202000101010111100400040002020, //43
#GOGOEND

202202,
#MEASURE 6/4
#BPMCHANGE 168
#SCROLL 0.99
100010101010200200101010001010202020100100200000100200100022020000000000, //45

#MEASURE 4/4
#BPMCHANGE 112
110002002100,
101000200000101020102210,
110122100112,
122121020212, //49

100100101100,
100000100100202010102010,
100021201120,
7,
8, //54
#END


COURSE:Hard
LEVEL:7
BALLOON:16,18,12,33,16,7,12,18
SCOREINIT:2980
SCOREDIFF:0

#START
#SCROLL 0.99
110002002100,
#BARLINEOFF
100200120010,
110020100110,
101021000200, //4

100100101102,
100020100100202000100000,
102102, 
708, //8

110201200100,
100010200001002000002000,
120020101021,
102021202200, //12

100002010000100010100000,
100000200100202000102010,
100100102000200020102000, //15

070000000000000000800000,
110222112011,
#BARLINEON
78,
#BARLINEOFF
100120102220, //19

#BARLINEON
#SCROLL 1.5
300000002000100000002000500000000000000000000008,
000020002020100020200022,
#MEASURE 2/4
221010,
#MEASURE 4/4
300000300000400022202020, //23

#GOGOSTART
100200102020200200102000,
100200102020200200102000, //25
#GOGOEND

#MEASURE 5/4
70
#BPMCHANGE 168
#SCROLL 0.99
000,
#MEASURE 6/4
000804, //27

#MEASURE 4/4
#BPMCHANGE 112
#SCROLL 1.5
100010201020102022201020,
#MEASURE 9/8
100020001000
#BPMCHANGE 167.2
#SCROLL 0.99
200000200200200000
#BPMCHANGE 112
#SCROLL 1.5
100020001000202020002000, //29

#MEASURE 4/4
100010202220102010203000,
100000001000100010001000500000000000000008000000,
#GOGOSTART
100010201100100200201100,
100020101000101022001020, //33
#GOGOEND

102120211020,
#MEASURE 5/4
#BPMCHANGE 168
#SCROLL 0.99
700000
#BPMCHANGE 112
#SCROLL 1.5
0800, //35

#MEASURE 4/4
#GOGOSTART
100100201020100100201020,
100100111020100100111000,
100120102000100120102000,
120120101010700000008000, //39

100020001020100100102000,
111010101000222011101020,
102112102020,
700802, //43
#GOGOEND

202202,
#MEASURE 6/4
#BPMCHANGE 168
#SCROLL 0.99
500000000000000000000000000000000000000000000000000000000008000000000000, //45

#MEASURE 4/4
#BPMCHANGE 112
110002002100,
110200120010,
110120100110,
101021020210, //49

100100101100,
100000100100202000102000,
102102,
7,
8, //54
#END


COURSE:Normal
LEVEL:5
BALLOON:12,13,7,25,12,4,9,13
SCOREINIT:4710
SCOREDIFF:0

#START
#SCROLL 0.99
100002000100,
#BARLINEOFF
100200020010,
102101,
101020000200, //4

1001,
100200020100,
102102, 
708, //8

1001,
100000000000200000000000000002000000000020000000,
102102,
102020000200, //12

1001,
100200020100,
102202, //15

070000000000000000800000,
100200201010,
#BARLINEON
78,
#BARLINEOFF
100100102020, //19

#BARLINEON
#SCROLL 1.5
300000000000100000000000500000000000000000000008,
002020102202,
#MEASURE 2/4
211,
#MEASURE 4/4
300300402200, //23

#GOGOSTART
100100002020200000002000,
100100002020200000002000, //25
#GOGOEND

#MEASURE 5/4
70
#BPMCHANGE 168
#SCROLL 0.99
000,
#MEASURE 6/4
08, //27

#MEASURE 4/4
#BPMCHANGE 112
#SCROLL 1.5
121121,
#MEASURE 9/8
100020
#BPMCHANGE 167.2
#SCROLL 0.99
100100000
#BPMCHANGE 112
#SCROLL 1.5
100010002000, //29

#MEASURE 4/4
102110101030,
100000001000100010000000500000000000000008000000,
#GOGOSTART
100010201000100100101000,
102110102010, //33
#GOGOEND

122122,
#MEASURE 5/4
#BPMCHANGE 168
#SCROLL 0.99
700000
#BPMCHANGE 112
#SCROLL 1.5
0800, //35

#MEASURE 4/4
#GOGOSTART
100100101000100100101000,
100100102000100100101000,
100100102000100100102000,
100100102000700000008000, //39

100020001000100100102000,
101110201110,
121122,
700802, //43
#GOGOEND

202202,
#MEASURE 6/4
#BPMCHANGE 168
#SCROLL 0.99
500000000000000000000000000000000000000000000000000000000008000000000000, //45

#MEASURE 4/4
#BPMCHANGE 112
1001,
100200020010,
100020100010,
101020000200, //49

1001,
100200020100,
102102,
7,
8, //54
#END


COURSE:Easy
LEVEL:4
BALLOON:9,11,5,20,9,3,7,11
SCOREINIT:8850
SCOREDIFF:0

#START
#SCROLL 0.99
1001,
#BARLINEOFF
100000010000,
11,
1002, //4

1001,
1200,
11, 
708, //8

1001,
1,
11,
1002, //12

1001,
1200,
12, //15

070000000000000000800000,
01,
#BARLINEON
78,
#BARLINEOFF
1, //19

#BARLINEON
#SCROLL 1.5
300000000000200000000000500000000000000000000008,
01,
#MEASURE 2/4
011,
#MEASURE 4/4
3340, //23

#GOGOSTART
102202,
102202, //25
#GOGOEND

#MEASURE 5/4
70
#BPMCHANGE 168
#SCROLL 0.99
000,
#MEASURE 6/4
08, //27

#MEASURE 4/4
#BPMCHANGE 112
#SCROLL 1.5
101101,
#MEASURE 9/8
10
#BPMCHANGE 167.2
#SCROLL 0.99
020
#BPMCHANGE 112
#SCROLL 1.5
2000, //29

#MEASURE 4/4
101101,
100000001000000010000000500000000000000008000000,
#GOGOSTART
101101,
102201, //33
#GOGOEND

102201,
#MEASURE 5/4
#BPMCHANGE 168
#SCROLL 0.99
700000
#BPMCHANGE 112
#SCROLL 1.5
0800, //35

#MEASURE 4/4
#GOGOSTART
101101,
101100,
101102,
202708, //39

101101,
102201,
102201,
78, //43
#GOGOEND

22,
#MEASURE 6/4
#BPMCHANGE 168
#SCROLL 0.99
500000000000000000000000000000000000000000000000000000000008000000000000, //45

#MEASURE 4/4
#BPMCHANGE 112
1001,
100000010000,
11,
1002, //49

1001,
1200,
11,
7,
8, //54
#END