//TJADB Project
TITLE:Many wow bang!
TITLEJA:Many wow bang!
SUBTITLE:--<PERSON> feat. Basho
BPM:134
WAVE:Many wow bang!.ogg
OFFSET:-5.415
DEMOSTART:23.325

//shinuchi: 9520/6610/2950/1990/1850

COURSE:Edit
LEVEL:9
BALLOON:9,9,22,22,9
SCOREINIT:650,2000
SCOREDIFF:168

#START
2002022020020020,
0020220020020020,
0020220020020020,
0020220020020020,

1020120012210010,
1020120012210010,
1221201012212010,
1120112030120000,
2202202022220220,
1121011201112112, //10

1120300211203002,
1010120300222020,
1010121101210012,
1010122100121011,

2210107000008011,
2210107000008010,
1021102110210120,
1121121120400020, //18

#GOGOSTART
1120302111203022,
1121112111220022,
1030210111210120,
3030110300102200,

1010121121030020,
1121112011210300,
1211121111211020,
1121112112030000, //26
#GOGOEND

0,
2020102010220000,
2020122010221020,
0,

1021102001200070,
0000000000800121,
1012011210120112,
1011212120303000, //34

1021102002100070,
0000000000800121,
1021011210210112,
1021103000101000,

1222200012222220,
0000122220004000,
1222200012222220,
0000122220404000, //42

#GOGOSTART
1012011210120120,
1221012210211020,
1121012011210210,
1122121104000020,

1012011210120120,
1221121201211020,
1121012211030220,
1122110700000800, //50
#GOGOEND

1202101202101202,
1201201011021022,
1201202101201201,
2101221100000000,
0,
0, //56
#END


COURSE:Oni
LEVEL:7
BALLOON:13,13,13,13,13,13,7,7,7,7,7,7,7,7,7
SCOREINIT:640,2090
SCOREDIFF:165

//Wii2: Balloons in #43-44 (all branches) are changed to big don.

#START
1001201000102000,
1001201010012010,
1011210010012010,
1011210010121010,

1010221020120120,
1010221020120120,
1010221020120120,
1010221022122212,
1,
0000200220202220, //10

#SECTION
#BRANCHSTART p,50,80
#N
1010200120222002,
1010110300102000,
1010201101012010,
1010201101012210,

1010202000201200,
2020202000201200,
1012012010120120,
1011202210300000, //n18

#E
1110200120222002,
1010110300102000,
1110201101012011,
1010201101012210,

1110222000201200,
2220222000201200,
1012012011120120,
1011202210300000, //e18

#M
1122201120222002,
1120120300102000,
1110221101112011,
1012201121012210,

1110222000221200,
2220222000221200,
1112012011120122,
1011202210300000, //m18

#SECTION
#BRANCHSTART p,50,80
#N
#GOGOSTART
1011201020222000,
1210121022220000,
1010020012020000,
1010110300202200,

1020201202020000,
1020201202020000,
2020201202001110,
3000300011030000, //n26
#GOGOEND

#E
#GOGOSTART
1111201021222000,
1210121022220000,
1110020012020000,
1110110300202200,

1022201202220000,
1022201202220000,
2220221202001110,
3000300011030000, //e26
#GOGOEND

#M
#GOGOSTART
1211201021222000,
1210121022220000,
1210020212020000,
1120210300212200,

1021201202120000,
1021201202120200,
2220221202002210,
3000300021030000, //m26
#GOGOEND

#SECTION
#BRANCHSTART p,50,80
#N
0,
0,
2020112022102210,
2022202212222220,

1001201000102070,
00000800,
1011201020220200,
1120120120222020,

1001201000222070,
00000800,
1010221022102210,
1011201022102210, //n38

#E
0,
0,
2220112022102210,
2022202212222220,

1121201110102070,
00000800,
1011201120220200,
1120120120222020,

1001201000222070,
00000800,
1110221022102210,
1011201022102210, //e38

#M
0,
0,
2120112022102210,
2021202212221220,

1121201210102070,
00000800,
1011202120220200,
1120120120212020,

1001201000222070,
00000800,
1210221022102210,
1101210222102210, //m38

#SECTION
#BRANCHSTART p,50,80
#N
1011201020222022,
1011201022102210,
1011202022102210,
1012201022102210, //n42

#E
1011201120222022,
1011201022102210,
1011202022102210,
1012201022102210, //e42

#M
1011201120222022,
1011201022102210,
1111222212102210,
1112211022102210, //m42

#SECTION
#BRANCHSTART p,50,80
#N
#GOGOSTART
1012010700000800,
1012010700000800,
1012011010120020,
1020101203000000,

1012010700000800,
1012010102202000,
1012011011020000,
1102010400000000, //n50
#GOGOEND

#E
#GOGOSTART
1112010700000800,
1112010700000800,
1112011010120020,
1120101203000000,

1112010700000800,
1112010102202000,
1112011011020000,
1112010400000000, //e50
#GOGOEND

#M
#GOGOSTART
1112010700000800,
1112010700000800,
1112021120120020,
1120122103000000,

1221010700000800,
1221010102212000,
2112011011020000,
2112010400000000, //m50
#GOGOEND

#SECTION
#BRANCHSTART p,50,80
#N
1202102020220200,
1202102022220200,
1202102022020000,
1112111100000000,
0,
0, //n56

#E
1202102022220200,
1202102022220200,
1202102022220200,
1112111100000000,
0,
0, //e56

#M
100200200100000000200000200100100200000020000000,
100200000200100000200000100200200100000020000000,
100200000200100000200000100200200200000020000000,
1112111100000000,
0,
0, //m56

#BRANCHEND
#END


COURSE:Hard
LEVEL:7
BALLOON:11,11
SCOREINIT:800,3060
SCOREDIFF:225

#START
1001101000102000,
1001101000102000,
1001101000102000,
1001101020022020,

1010222020020020,
1010222020020020,
1010222020020020,
1010222022202220,
3,
0, //10

11202210,
1010110300000000,
1010201101022000,
1010201101022000,

1010202000202200,
1010202000202200,
1011011010110000,
1011202220300000, //18

#GOGOSTART
1011100020222000,
1110111022220000,
1010010022020000,
1010110300000000,

1020201102020000,
1020201102020000,
2020201102001110,
3000300011030000, //26
#GOGOEND

0,
0,
22112121,
200000000000500000000008000000000000000000000000,

1001201000100070,
00000800,
1001101020220200,
1120120020222000, //34

1001201000200070,
00000800,
1010201022102210,
1001101022202220,

1002,
1002,
1102,
1000100022202220, //42

#GOGOSTART
1011010100002000,
1011010100002000,
1011011010110000,
1020101101000000,

1011010100002000,
1011010100002000,
1011011011020000,
1101010300000000, //50
#GOGOEND

1202002022020000,
1202002022020000,
1202002022020000,
1110222100000000,
0,
0, //56
#END


COURSE:Normal
LEVEL:4
BALLOON:8,8,8
SCOREINIT:1130,7180
SCOREDIFF:493

#START
11,
1110,
1110,
100000000000100000000000500000000008000000000000,

12,
12,
1120,
1120,
3,
0, //10

12,
3330,
1022,
1022,

1100,
2200,
10101110,
13, //18

#GOGOSTART
11102000,
11102000,
11102000,
3330,

22202000,
22202000,
12,
10101130, //26
#GOGOEND

0,
2,
2,
200000000000000000500000000008000000000000000000,

1101,
7008,
12,
1120, //34

1101,
7008,
12,
1120,

1002,
1002,
1102,
1110, //42

#GOGOSTART
11100020,
11100020,
11100020,
10101110,

11200020,
11200020,
11200020,
10101110, //50
#GOGOEND

1,
2,
1,
7008,
0,
0, //56
#END


COURSE:Easy
LEVEL:4
BALLOON:6,6,6
SCOREINIT:1070,10400
SCOREDIFF:773

#START
1,
1,
1,
1110,

12,
1,
12,
12,
3,
0, //10

12,
33,
12,
22,

1100,
2200,
1110,
13, //18

#GOGOSTART
12,
12,
11,
33,

22,
22,
12,
1113, //26
#GOGOEND

0,
2,
2,
2,

1100,
7008,
1,
1, //34

1100,
7008,
1,
1,

1002,
1002,
1002,
1011, //42

#GOGOSTART
1102,
1102,
1102,
1110,

1202,
1202,
1202,
1110, //50
#GOGOEND

1,
1,
1,
7008,
0,
0, //56
#END