//TJADB Project
TITLE:<PERSON><PERSON> no Oka
TITLEJA:宝の丘
SUBTITLE:--<PERSON><PERSON><PERSON> feat. Takane Yabuki
BPM:206.72
WAVE:<PERSON><PERSON> no Oka.ogg
OFFSET:-3.618
DEMOSTART:78.382

//shinuchi: 4680/3630/2800/1970/-

COURSE:Edit
LEVEL:9
BALLOON:5
SCOREINIT:1330
SCOREDIFF:0

#START
#MEASURE 3/4
#SCROLL 0.5
112120,
#BPMCHANGE 204.67
112120,
#BPMCHANGE 207.93
122121,
#BPMCHANGE 207.45
112222,

#BPMCHANGE 206
112122,
112122,
111221,
112121, //8

#SCROLL 1
#GOGOSTART
100011201020,
100011102020,
100011201020,
101011102020,

100011201020,
101022101020,
100011201020,
101111102020, //16

100011201020,
100011102020,
100011201020,
102022201020,

100011201020,
101022101020,
100011201020,
500000000000000008000000100000200000,//24
#GOGOEND

222222222000,
111110001020,
111020001020,
111122221000,

222222222000,
111110001010,
222010001020,
111020102020,

101110102010,
201120102000,
111122221000,
3, //36

#SCROLL 0.5
101111,
202210,
500000000008000000000000200000000000,
102220,

102211,
102210,
500000000008000000000000100000000000,
102011, //44

101111,
202210,
500000000008000000000000100000000000,
202220,

102222,
101210,
102210,
302011, //52

101111,
202210,
500000000008000000000000200000000000,
102220,

102211,
102210,
500000000008000000000000100000000000,
102011, //60

101111,
202210,
500000000008000000000000100000000000,
202220,

102222,
101210,
102210,
500000000008000000000000100000000000, //68

708,
102122,
102220,
101120102010,

102122,
102220,
101120102010,
101022101020, //76

102122,
102220,
101120102010,
102210,

102122,
102220,
101120102010,
140,
111110101010,
222220202020, //86

#SCROLL 1
#GOGOSTART
300011201020,
100011102020,
100011201020,
101011102020,

100011201020,
101022101020,
100011201020,
101111102020, //94

100011201020,
100011102020,
100011201020,
102022201020,

100011201020,
101022101020,
100011201020,
500000000000000008000000100000200000, //102

301011201020,
102011102020,
102011201020,
101011112020,

102011201020,
101022221020,
102011201020,
111111112020, //110

301011201020,
302011102020,
302011201020,
301011112020,

302011201020,
301022221020,
302011201020,
500000000000000008000000000000000000, //118
#GOGOEND

#SCROLL 0.5
122,
122,
122,
122,

102120,
102120,
102120,
102120, //126

#SCROLL 1.5 
422222222222,
422222222222,
422222222222,
334,

422222222222,
422222222222,
422222222222,
303044, //134

#SCROLL 0.5
0,
0,
0,
0,
0, //139
#END


COURSE:Oni
LEVEL:7
BALLOON:10,10,10,10,16
SCOREINIT:630,2150
SCOREDIFF:163

#START
#MEASURE 3/4
#SCROLL 0.5
2,
#BPMCHANGE 204.67
2,
#BPMCHANGE 207.93
100110,
#BPMCHANGE 207.45
102022,

#BPMCHANGE 206
122,
102220,
102222,
122222, //8

#SCROLL 1
#GOGOSTART
100210,
122,
100210,
112,

100210,
122,
102212,
121012, //16

100210,
122,
100210,
202120,

100210,
122,
102212,
102022, //24
#GOGOEND

5,
000008000000000000000000100000100000,
221011,
221,

5,
000008000000000000000000100000200000,
221012,
122,

112,
212020,
333,
3, //36

#SCROLL 0.5
101120,
201,
101120,
2,

102210,
102,
102210,
1, //44

101120,
201,
101120,
2,

102210,
102,
102210,
1, //52

7,
000008000000000000000000000000000000,
7,
000008000000000000000000000000000000,

101120,
122,
101120,
102011, //60

7,
000008000000000000000000000000000000,
7,
000008000000000000000000000000000000,

102210,
212,
102210,
201022, //68

122,
222022,
211,
111011,

122,
222022,
201011,
101110, //76

122,
222022,
211,
111011,

122,
222022,
212,
002022,
111111,
222222, //86

#SCROLL 1
#GOGOSTART
300210,
122,
300210,
112,

300210,
122,
302212,
121012, //94

300210,
122,
300210,
202120,

300210,
122,
302212,
121012, //102

112210,
112120,
112210,
112120,

112210,
112120,
112212,
121012, //110

112210,
112120,
112210,
221120,

112210,
112120,
112212,
1, //118
#GOGOEND

#SCROLL 0.5
222211,
222211,
022211,
222111,

112222,
112222,
022122,
122120, //126

344,
344,
344,
343,

7,
000000000000000000080000000000000000,
102212,
102022, //134

0,
0,
0,
0,
0, //139
#END


COURSE:Hard
LEVEL:5
BALLOON:9,9,9,20,9,9
SCOREINIT:680,3250
SCOREDIFF:185

#START
#MEASURE 3/4
#SCROLL 0.5
2,
#BPMCHANGE 204.67
2,
#BPMCHANGE 207.93
100110,
#BPMCHANGE 207.45
1,

#BPMCHANGE 206
344,
344,
344,
3, //8

#SCROLL 0.75
#GOGOSTART
100110,
122,
100110,
122,

100110,
122,
100220,
1, //16

100110,
122,
100110,
122,

100110,
122,
100220,
1, //24
#GOGOEND

5,
000008000000000000000000100000100000,
102011,
121,

5,
000008000000000000000000100000100000,
102011,
122,

111,
222020,
333,
3, //36

#SCROLL 0.5
122,
122,
122,
1,

112,
112,
112,
1, //44

122,
122,
122,
1,

112,
112,
112,
1, //52

7,
000008000000000000000000000000000000,
122,
1,

7,
000008000000000000000000000000000000,
112,
1, //60

7,
000008000000000000000000000000000000,
122,
1,

9,
0,
908,
0, //68

122,
222020,
211,
1,

122,
222020,
211,
1, //76

122,
222022,
211,
1,

122,
222022,
211,
020,
5,
000000000008000000000000000000000000, //86

#SCROLL 0.75
#GOGOSTART
100110,
122,
100110,
122,

100110,
122,
100220,
1, //94

100110,
122,
100110,
122,

100110,
122,
100220,
1, //102

300110,
122,
300110,
122,

300110,
122,
300220,
1, //110

300110,
122,
300110,
122,

300110,
122,
300220,
1, //118
#GOGOEND

#SCROLL 0.5
202,
202,
202,
202220,

202,
202,
100110,
1, //126

344,
344,
344,
343,

7,
8,
7,
8, //134

0,
0,
0,
0,
0, //139
#END


COURSE:Normal
LEVEL:4
BALLOON:6,6,6,15,20
SCOREINIT:630,4680
SCOREDIFF:180

#START
#MEASURE 3/4
#SCROLL 0.5
2,
#BPMCHANGE 204.67
2,
#BPMCHANGE 207.93
202,
#BPMCHANGE 207.45
2,

#BPMCHANGE 206
3,
3,
3,
3, //8

#SCROLL 0.75
#GOGOSTART
101,
122,
101,
1,

101,
122,
102,
1, //16

101,
122,
101,
2,

101,
122,
102,
1, //24
#GOGOEND

5,
000008000000000000000000000000000000,
111,
1,

5,
000008000000000000000000000000000000,
222,
2,

111,
222,
333,
3, //36

#SCROLL 0.5
1,
1,
102,
1,

2,
2,
202,
1, //44

1,
1,
102,
1,

2,
2,
202,
1, //52

7,
000008000000000000000000000000000000,
102,
1,

7,
000008000000000000000000000000000000,
202,
1, //60

7,
000008000000000000000000000000000000,
102,
1,

9,
0,
908,
0, //68

022,
202,
211,
101,

122,
202,
211,
101, //76

122,
202,
211,
101,

122,
202,
211,
010,
5,
000000000008000000000000000000000000, //86

#SCROLL 0.75
#GOGOSTART
101,
122,
101,
1,

101,
122,
102,
1, //94

101,
122,
101,
2,

101,
122,
102,
1, //102

303,
344,
303,
3,

303,
344,
304,
3, //110

303,
344,
303,
4,

303,
344,
304,
3, //118
#GOGOEND

#SCROLL 0.5
2,
2,
202,
2,

2,
2,
101,
1, //126

344,
344,
344,
344,

9,
0,
0,
9,

8,
0,
0,
0,
0, //139
#END


COURSE:Easy
LEVEL:4
BALLOON:3,3,3,7,12
SCOREINIT:530,6000
SCOREDIFF:165

#START
#MEASURE 3/4
#SCROLL 0.5
2,
#BPMCHANGE 204.67
2,
#BPMCHANGE 207.93
202,
#BPMCHANGE 207.45
2,

#BPMCHANGE 206
3,
3,
3,
3, //8

#SCROLL 0.75
#GOGOSTART
101,
1,
101,
1,

101,
1,
102,
2, //16

101,
1,
101,
2,

101,
1,
102,
2, //24
#GOGOEND

5,
000008000000000000000000000000000000,
111,
1,

5,
000008000000000000000000000000000000,
222,
2,

112,
2,
333,
3, //36

#SCROLL 0.5
1,
1,
101,
1,

1,
1,
102,
2, //44

1,
1,
101,
1,

2,
2,
202,
2, //52

7,
000008000000000000000000000000000000,
1,
1,

7,
000008000000000000000000000000000000,
2,
2, //60

7,
000008000000000000000000000000000000,
1,
1,

9,
0,
908,
0, //68

022,
2,
011,
1,

022,
2,
011,
1, //76

022,
2,
011,
1,

022,
2,
111,
020,
5,
000000000008000000000000000000000000, //86

#SCROLL 0.75
#GOGOSTART
101,
1,
101,
1,

101,
1,
102,
2, //94

101,
1,
101,
2,

101,
1,
102,
2, //102

303,
3,
303,
3,

303,
3,
304,
4, //110

303,
3,
303,
4,

303,
3,
304,
4, //118
#GOGOEND

#SCROLL 0.5
2,
2,
202,
2,

2,
2,
101,
1, //126

333,
3,
333,
3,

9,
0,
0,
9,

8,
0,
0,
0,
0, //139
#END