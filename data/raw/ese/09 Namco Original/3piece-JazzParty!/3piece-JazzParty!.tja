//TJADB Project
TITLE:3piece-<PERSON>Party!
TITLEJA:3piece-<PERSON>Party!
SUBTITLE:--Mikhail
BPM:160
WAVE:3piece-JazzParty!.ogg
OFFSET:-1.800
DEMOSTART:27.305

COURSE:Oni
LEVEL:9
BALLOON:6
SCOREINIT:580
SCOREDIFF:130


#START


3022211120202030,
0022211120202030,
0022211120202030,
03030223,
0022211122002030,
0022211122111030,
000000200200200100100100200000101010200000300000,
03030111,
11113030,//9
0020020210200202,
1020020210201120,
0020020210200202,
1020020210201222,
0020120210201202,
1020120210201120,
0020120210201222,
1021003000222111,
1020112200102020,
1021022110210010,
0010202010020020,
1002002010022110,
1012212210020020,
1002002000102020,
1002002010020020,
1000221121212211,
1020112012020210,
1020112010210120,
1020112010210120,
1122112121211221,
1022102110210120,
1022102110210120,
1022102110211120,
1221222122112211,
2211212030303030,//34

#GOGOSTART
0020120211120020,
1020120202122122,
1221221222111221,
1020102100120200,
3003004004003003,
0040040010202100,
1021120102002020,
1000222210101111,
2011102011102011,
1012021122112010,
2011102011102111,
2010120010120020,
1021002120020010,
0020002020010000,
1002002010002000,
202020202020700000000800,//50

#GOGOEND
3022211120202030,
0022211120202030,
0022211120202030,
03030223,
0020210022101030,
000000200000200010001000200200100100100000300000,
000000200000202020101010200200100100100000300000,
03030111,
1010101030003001,
14000000,


#END


COURSE:Hard
LEVEL:7
BALLOON:9
SCOREINIT:740
SCOREDIFF:200

#START
30000003,
00000003,
00000003,
03030223,
0000200002002030,
0000200022202030,
0000200020222030,
03060000,
000000000008000000000000300000000000300000000000,//9
0020020000200200,
0020020010001000,
0020020000200200,
0020020010001000,
1020020010200200,
1020020010001000,
1020020010200200,
3003003000000000,
1011100100102020,
1011000020220010,
0010202010020020,
1002002010002000,
1011101020020010,
1002002000102020,
1002002010020020,
1,
1020002010220020,
1020002010220020,
1020002010220020,
1020002010222000,
12121210,
12121210,
1020102010222000,
3,
00003333,//34

#GOGOSTART
0020102010110020,
1020102010110020,
1011002010110020,
1020102010110200,
3003004004003003,
0040040010102200,
1010220101002020,
7008,
1002001002001002,
0010020020001000,
1002001002001002,
0010010020020010,
1001000020020010,
0020002020010000,
1002002010002000,
500000000000000000000000000000000008000000000000,//50

#GOGOEND
30000003,
00000003,
00000003,
03030223,
0020220020202030,
000000200000200020002000200000200000200000300000,
0022222020202030,
03060000,
000000000008000000000000300000000000300000000000,
04000000,//289combo
,
,
,
#END

COURSE:Normal
LEVEL:5
BALLOON:4,9
SCOREINIT:1050
SCOREDIFF:310

#START
30000003,
00000003,
00000003,
03030003,
00000003,
00000003,
00000003,
03060000,
000000000008000000000000300000000000300000000000,//9
1202,
1222,
1202,
1222,
1212,
1222,
1212,
78,
1,
12,
1120,
1120,
12,
1120,
1120,
1,
1112,
1112,
1112,
1120,
10101110,
10101110,
10101110,
3,
,//34

#GOGOSTART
10201110,
10201110,
10202220,
10202220,
30030030,
03002020,
900000,
8,
10010010,
01002020,
10010010,
000000500000000000000000000000000008000000000000,
10001002,
000000200000000000200000500000000008000000000000,
1022,
500000000000000000000000000000000008000000000000,//50

#GOGOEND
30000003,
00000003,
00000003,
03030003,
00000003,
00000003,
00000003,
03060000,
000000000008000000000000300000000000300000000000,
04000000,//145combo
,
,
,
#END

COURSE:Easy
LEVEL:4
BALLOON:3,7
SCOREINIT:720
SCOREDIFF:240

#START
30000003,
00000003,
00000003,
03030003,
00000003,
00000003,
00000003,
03060000,
000000000008000000000000300000000000300000000000,//9
0202,
0222,
0202,
0222,
0101,
0111,
0101,
78,
1,
12,
2220,
2220,
12,
1,
12,
1,
1202,
1202,
1202,
1120,
1202,
1202,
1202,
3,
,//34

#GOGOSTART
1212,
1120,
1212,
1120,
30030030,
03002020,
900000,
8,
10010010,
01002020,
10010010,
000000500000000000000000000000000008000000000000,
11,
2220,
1022,
500000000000000000000000000000000008000000000000,//50

#GOGOEND
30000003,
00000003,
00000003,
03030003,
00000003,
00000003,
00000003,
03060000,
000000000080000000000000300000000000300000000000,
04000000,//118combo
,
,
,
#END
