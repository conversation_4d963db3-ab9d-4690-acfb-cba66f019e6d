//TJADB Project
TITLE:<PERSON><PERSON><PERSON> mo Yappappa
TITLEJA:太陽もヤッパッパー
SUBTITLE:--<PERSON>M:146
WAVE:Taiyou mo Yappappa.ogg
OFFSET:-1.987
DEMOSTART:28.300

COURSE:Edit
LEVEL:8
BALLOON:16
SCOREINIT:570
SCOREDIFF:148


#START


11211230,
11211230,
11211230,
3000003000003011,
11120212,
11120212,
11120212,
4,
1010221101102010,
1010201122101020,
1010221101102010,
1010201022102000,
1010221101102010,
1010201122101020,
1010221101102010,
1010201022102000,

#GOGOSTART
3030402210102022,
11210120,
3030402210102022,
11210120,
3030402210102022,
11210120,
3030402210102022,
1010201000102022,

#GOGOEND
1000200022101020,
12221020,
1000200022101020,
12221200,
1000200022101020,
12221020,
1000200022101020,
7008,
1010221201102010,
1010201022101020,
1010221201102010,
1010201022102000,
2222,
2222,
2222,
2222,
2020222202202020,
2020222202202020,
2020222202202020,
2020222202202020,
1020122212201020,
1020122212201020,
1020122212201020,
1020122212201020,
1020122212201020,
1020122212201020,
11111111,
100000100000100000100000500000000000000008000000,

#GOGOSTART
1010212022101020,
2020121022101020,
1010212022102010,
2020121022101020,
1010212022101020,
2020121022101020,
1010212022102010,
1010212022101020,

#GOGOEND
#MEASURE 3/4
302302,
302302,
302302,
302302,
312312,
312312,
312312,
312312,
301020121022,
101220121022,
101220300000,


#END


COURSE:Oni
LEVEL:4
SCOREINIT:730
SCOREDIFF:213


#START


10210220,
10210220,
10210220,
3,
10101100,
10101100,
10101100,
3,

#SECTION
#BRANCHSTART p,75,90
#N
10001100,
10001100,
10001100,
1000100011102000,
10201100,
10201100,
10201120,
1000100011102000,

#E
10001100,
10001100,
10001100,
1000100011102000,
10201100,
10201100,
10201120,
1000100011102000,

#M
10001100,
10001100,
10001100,
1000100011102000,
10201100,
10201100,
10201120,
1000100011102000,

#GOGOSTART
#SECTION
#BRANCHSTART p,75,90
#N
2020200011102000,
11010211,
2020200011102000,
11010211,
2020200011102000,
11010211,
2020200011102000,
11010211,

#E
2020200011102000,
1010001000201110,
2020200011102000,
1010001000201110,
2020200011102000,
1010001000201110,
2020200011102000,
1010001000201110,

#M
2020200011102000,
1010001000201110,
2020200011102000,
1010001000201110,
2020200011102000,
1010001000201110,
2020200011102000,
1010001000201110,

#GOGOEND
#SECTION
#BRANCHSTART p,75,90
#N
1000100010111000,
1010001010222020,
1000100010111000,
1010001010222020,
1000100010111000,
1010001010222020,
1000100010111020,
3,

#E
1000100010111000,
1010001010222020,
1000100010111000,
1010001010222020,
1000100010111000,
1010001010222020,
1000100010111000,
3,

#M
1000100010111020,
1010001010222020,
1000100010111020,
1010001010222020,
1000100010111020,
1010001010222020,
1000100010111020,
3,

#SECTION
#BRANCHSTART p,75,90
#N
10201100,
10201100,
10201120,
1000100011102000,
20000002,
20000002,
20000002,
20000102,
20020102,
20020102,
20020102,
200201010,
1000100010001011,
1000100010001011,
1000100010001011,
1000100010001011,
1000200010001011,
1000200010001011,
1211,
500000000000000000000008000000000000000000000000,

#E
10201120,
10201120,
10201120,
1000100011102000,
20000002,
20000002,
20000002,
20000102,
20020102,
20020102,
20020102,
200201010,
1000100010001011,
1000100010001011,
1000100010001011,
1000100010001011,
1000200010001011,
1000200010001011,
1211,
500000000000000000000008000000000000000000000000,

#M
10201120,
10201120,
10201120,
1000100011102000,
20000002,
20000002,
20000002,
20000102,
20020102,
20020102,
20020102,
20020101,
1000100010001011,
1000100010001011,
1000100010001011,
1000100010001011,
1000200010001011,
1000200010001011,
1211,
500000000000000000000008000000000000000000000000,

#GOGOSTART
#SECTION
#BRANCHSTART p,75,90
#N
1000101000111020,
1000101000111010,
1000101000111020,
1000101000111010,
1000101000111020,
1000101000111010,
1000101000111020,
1000101000111010,
#GOGOEND
#MEASURE 3/4
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102300,

#E
1000101000111020,
1000101000111010,
1000101000111020,
1000101000111010,
1020101000111020,
1020101000111010,
1020101000111020,
1020101000111010,
#GOGOEND
#MEASURE 3/4
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102300,

#M
1000101000111020,
1000101000111010,
1000101000111020,
1000101000111010,
1020101000111020,
1020101000111010,
1020101000111020,
1020101000111010,
#GOGOEND
#MEASURE 3/4
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102300,

#BRANCHEND


#END


COURSE:Hard
LEVEL:6
SCOREINIT:700
SCOREDIFF:170


#START


10210220,
10210220,
10210220,
3,
10101100,
10101100,
10101100,
3,
10001100,
10001100,
10001100,
1000100011102000,
10201100,
10201100,
10201120,
1000100011102000,

#GOGOSTART
2020200011102000,
11010211,
2020200011102000,
11010211,
2020200011102000,
11010211,
2020200011102000,
11010211,//24

#GOGOEND
1000100010111000,
1010001010222020,
1000100010111000,
1010001010222020,
1000100010111000,
1010001010222020,
1000100010111020,
3,//32
10201100,
10201100,
10201120,
1000100011102000,
20000002,
20000002,
20000002,
20000102,
20020102,
20020102,
20020102,
20020101,
1000100010001011,
1000100010001011,
1000100010001011,
1000100010001011,
1000200010001011,
1000200010001011,
1211,
500000000000000000000008000000000000000000000000,//52

#GOGOSTART
1000101000111020,
1000101000111010,
1000101000111020,
1000101000111010,
1000101000111020,
1000101000111010,
1000101000111020,
1000101000111010,//60

#GOGOEND
#MEASURE 3/4
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102101,
102300,
,
,
,
,
,
#END

COURSE:Normal
LEVEL:5
SCOREINIT:770
SCOREDIFF:220

#START
10010020,
10010020,
10010020,
10010020,
10101100,
10101100,
10101100,
3,
10001100,
10001100,
10001100,
1110,
10001100,
10001100,
10001100,
1110,//16

#GOGOSTART
20001100,
11010010,
20001100,
11010010,
20001100,
11010010,
20001100,
11010000,//24

#GOGOEND
10101200,
11011000,
10101200,
11011000,
10101200,
11011000,
10101200,
3,//32
10001100,
10001100,
10001100,
1110,
10000001,
10000001,
10000001,
10000001,
20000001,
10000001,
10000001,
10000001,
1111,
10101011,
1111,
10101011,
1111,
10101011,
1111,
500000000000000000000008000000000000000000000000,//52

#GOGOSTART
10110000,
10110000,
10110000,
10110000,
10110000,
10110000,
10110000,
10110000,//60

#GOGOEND
#MEASURE 3/4
102101,
102100,
102101,
102100,
102101,
102102,
102101,
102102,
102112,
102111,
102300,
,
,
,
,
,
#END

COURSE:Easy
LEVEL:4
SCOREINIT:750
SCOREDIFF:250

#START
10010000,
10010000,
10010000,
10010000,
1,
1,
1,
3,
11,
1110,
11,
1110,
11,
1110,
11,
1110,//16

#GOGOSTART
2200,
1,
2200,
1,
2200,
1,
2200,
1,//24

#GOGOEND
1120,
11,
1120,
11,
1120,
11,
1120,
3,
11,
1110,
11,
1110,
1,
1,
1,
1,
2,
2,
2,
2,
11,
11,
1111,
1110,
1111,
1110,
1111,
500000000000000000000008000000000000000000000000,//52

#GOGOSTART
11,
10001110,
11,
10001110,
11,
10001110,
11,
10001110,//60

#GOGOEND
#MEASURE 3/4
1,
12,
1,
12,
1,
12,
1,
12,
1,
12,
13,
,
,
,
,
,
#END
