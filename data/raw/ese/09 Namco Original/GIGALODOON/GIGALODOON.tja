//TJADB Project
TITLE:<PERSON><PERSON><PERSON><PERSON>OON
TITLEJA:G<PERSON><PERSON><PERSON>OON
SUBTITLE:--Lau<PERSON>
SUBTITLEJA:Laur
BPM:240
WAVE:GIGALODOON.ogg
OFFSET:-1.347
DEMOSTART:111.394

//Balloon Lengths on KFM unconfirmed.

COURSE:Edit
LEVEL:10
BALLOON:4,6,12,9
SCOREINIT:920
SCOREDIFF:0

#START
4,
0,
2,
2,

3020222020222020,
3020222022222222,
22020202,
0040404022222222,
222222222700, //9

8,
0,
0,
4011104000400040,

#GOGOSTART
1010201110201120,
1110201120101111,
1010201110201120,
1022102010221111, //17
#GOGOEND

10101012,
1110100011101020,
10101212,
1110100011011010,

#GOGOSTART
1111111111222000,
1111111111122000,
1111111111111112,
#GOGOEND
0040400040002222, //25

10101210,
12101011,
1000100010201011,
11101020,

10012222,
1000001120000000,
10012221,
0010200020112000, //33

#GOGOSTART
1111101020101010,
1111101020102010,
1111101020101010,
1111101020102010,
#GOGOEND

1212,
10201022,
#GOGOSTART
1010101011221122,
#SCROLL 3
70000080
#SCROLL 1
22112211, //41
#GOGOEND

20202021,
2000200020112010,
20202021,
2000102022221020,

#GOGOSTART
1111111011102010,
1111112011102010,
1111112012102010,
100100100100100100200000100010001000200020002000, //49
#GOGOEND

30301210,
30301211,
3000300010201011,
1444,

#GOGOSTART
1111111011102010,
1111112011102010,
1111112012102010,
100100100100100100200000100010001000200020002000, //57
#GOGOEND

30220220,
10220220,
20210210,
20210210,

2222221020102222,
2210201022221210,
2222221020102222,
2210221222221210, //65

12210210,
20010210,
22210210,
20010210,

#SCROLL 3
500000000000000008000000
#SCROLL 1
#GOGOSTART
100010001000100010001000,
100010001000100010001000
#SCROLL 5
500000000000000008000000,
#SCROLL 1
100010001000100010001000100100100100101010100100,
1111111040002222, //73
#GOGOEND

12212212,
#BARLINEOFF
#BPMCHANGE 230
#SCROLL 0.72
12212212,
#BPMCHANGE 220
#SCROLL 0.58
12212212,
#BPMCHANGE 210
#SCROLL 0.52
12212212,

#BPMCHANGE 200
#SCROLL 0.5
12220222,
#MEASURE 3/4
002020222020,
#MEASURE 4/4
0020202000202220,
0020202220202210, //81

#BPMCHANGE 210
0020202020222210,
#BPMCHANGE 220
0020202020221210,
#BPMCHANGE 230
12121212,
#BPMCHANGE 240
1010101011112210, //85

#BARLINEON
#SCROLL 1
#GOGOSTART
3000
#GOGOEND
2221,
2020201020222010,
#GOGOSTART
3000
#GOGOEND
2221,
200000200000200000100000202020200200200000100000,

#GOGOSTART
3000
#GOGOEND
0220222110
#GOGOSTART
30,
00
#GOGOEND
22022022202211,
10202202,
02202020, //93

0333,
3000300030003011,
1333,
3000300030301111,

1022102210221022,
1010102210221111,
#GOGOSTART
12212212,
1022221040004000, //101
#GOGOEND

30303230,
30303232,
3000300030203011,
1000300030203011,

1022202010222020,
1022201010222010,
1022101010221010,
10220220, //109

#GOGOSTART
2011201011201010,
2011201011201010,
2111202210112210,
2021102111201120,

2011201011202010,
2011201011202010,
2212201021212010,
70000008, //117
#GOGOEND

4000111000222020,
2000111000221111,
#GOGOSTART
2111202210112210,
2021102111201120,

2011201011202010,
2011201011202010,
2212201021212010,
7000000000802222, //125
#GOGOEND

12211212,
2010201010222010,
1020201010201022,
2011104000400040,

#GOGOSTART
1110201111201120,
100100100100200010001000200100100100200000200000,
1110201111201120,
100100100100200010001000200100100100200100200100,

500000000000000000000000000000000008000000000000,
#GOGOEND
0,
0,
0, //137
#END


COURSE:Oni
LEVEL:10
BALLOON:16,6,9,14,11,9
SCOREINIT:1280
SCOREDIFF:0

#START
4,
0,
2,
2,

32222222,
3020202022202020,
44040404,
04447000,
0, //9

8,
0,
0,
4011104000400040,

#GOGOSTART
1010201010201110,
2010201020101110,
1010201010201110,
2010201020101110, //17
#GOGOEND

10101012,
10101012,
10101012,
10101012,

#GOGOSTART
1110111011102000,
1110111011102000,
1110111011102020,
#GOGOEND
04404040, //25

10101210,
12101011,
1000100010201011,
11101020,

10012002,
10012000,
10012002,
02102020, //33

#GOGOSTART
1110111020101010,
1110111020101010,
1110111020101010,
1110111020101010,
#GOGOEND

1212,
10201022,
#GOGOSTART
1010101011101110,
#SCROLL 3
7008
#SCROLL 1
0000, //41
#GOGOEND

2222,
20202021,
2222,
2000201020111020,

#GOGOSTART
1111101010102010,
1111102010102010,
1111101010102010,
1111102010102000, //49
#GOGOEND

1111,
10101011,
1000100010001011,
1444,

#GOGOSTART
1111101010102010,
1111102010102010,
1111101010102010,
1111102010102000, //57
#GOGOEND

30220220,
10220220,
20210210,
20210210,

500000000000000008000000200000000000500000000000, 
000008000000200000000000100100100100100000200000,
500000000000000008000000200000000000500000000000, 
000008000000200000000000100100100100100000200000, //65

10210210,
20010210,
20210210,
20010210,

#SCROLL 3
500000000008000000000000
#SCROLL 1
#GOGOSTART
700000000000000000000000,
000000000000000000800000
#SCROLL 5
500000000008
000000000000,
#SCROLL 1
7,
00084000, //73
#GOGOEND

20210210,
#BARLINEOFF
#BPMCHANGE 230
#SCROLL 0.72
20210210,
#BPMCHANGE 220
#SCROLL 0.58
20210210,
#BPMCHANGE 210
#SCROLL 0.52
20210210,

#BPMCHANGE 200
#SCROLL 0.5
11,
#MEASURE 3/4
1,
#MEASURE 4/4
11,
10000021, //81

#BPMCHANGE 210
00000021,
#BPMCHANGE 220
00000021,
#BPMCHANGE 230
02020202,
#BPMCHANGE 240
11111111, //85

#BARLINEON
#SCROLL 1
#GOGOSTART
3000
#GOGOEND
2221,
22212221,
#GOGOSTART
3000
#GOGOEND
2221,
22212221,

#GOGOSTART
30
#GOGOEND
20220
#GOGOSTART
3,
0
#GOGOEND
2202020,
10202202,
02202020, //93

0111,
10101011,
1111,
1000100010101011,

12121212,
1020102010201111,
#GOGOSTART
10202202,
02104040, //101
#GOGOEND

1111,
10101012,
1000100010001011,
1000100010101111,

12221222,
12221222,
12221222,
10220220, //109

#GOGOSTART
2000201011102020,
2000201011102020,
2010201011102010,
2020101011112010,

2000201011102020,
2000201011102020,
2010201011112010,
70000008, //117
#GOGOEND

4000111000202020,
2000111000202020,
#GOGOSTART
2010201011102010,
2020101011112010,

2000201011102020,
2000201011102020,
2010201011112010,
70000800, //125
#GOGOEND

11211212,
21212121,
11211212,
2011104000400040,

#GOGOSTART
1010201010201110,
2011102011112010,
1010201010201110,
2011102011112010,

500000000000000000000000000000000008000000000000,
#GOGOEND
0,
0,
0, //137
#END


COURSE:Hard
LEVEL:8
BALLOON:9,6,6,7,6,7,11,14,11
SCOREINIT:2160
SCOREDIFF:0

#START
4,
0,
2,
2,

32222000,
32222000,
22020202,
04447000,
0, //9

8,
0,
0,
0,

#GOGOSTART
11101011,
11101011,
11101011,
500000000000000000000000000000000008000000000000, //17
#GOGOEND

10101012,
10101012,
10101012,
10101012,

#GOGOSTART
7008,
7008,
7008,
#GOGOEND
0, //25

10101210,
11101011,
10101210,
11101020,

10012000,
10012000,
10012001,
01202020, //33

#GOGOSTART
11112011,
11112011,
11112011,
11112011,
#GOGOEND

1212,
10201022,
#GOGOSTART
7008,
#SCROLL 3
500000000000000008000000
#SCROLL 1
000000000000000000000000, //41
#GOGOEND

2222,
1222,
2222,
1222,

#GOGOSTART
11111120,
11121120,
11111120,
11121120, //49
#GOGOEND

1111,
10101011,
1111,
1444,

#GOGOSTART
11111120,
11121120,
11111120,
11121120, //57
#GOGOEND

3,
0,
2,
0,

500000000000000008000000200000000000500000000000, 
000008000000200000000000200000000000000000000000,
500000000000000008000000200000000000500000000000, 
000008000000200000000000200000000000000000000000, //65

10000210,
2,
20000210,
2,

#SCROLL 3
500000000008000000000000
#SCROLL 1
#GOGOSTART
700000000000000000000000,
000000000000000000800000
#SCROLL 5
500000000008
000000000000,
#SCROLL 1
7,
00080000, //73
#GOGOEND

10012000,
#BARLINEOFF
#BPMCHANGE 230
#SCROLL 0.72
10012000,
#BPMCHANGE 220
#SCROLL 0.58
10012000,
#BPMCHANGE 210
#SCROLL 0.52
10012000,

#BPMCHANGE 200
#SCROLL 0.5
11,
#MEASURE 3/4
1,
#MEASURE 4/4
11,
10000001, //81

#BPMCHANGE 210
00000001,
#BPMCHANGE 220
00000001,
#BPMCHANGE 230
7,
#BPMCHANGE 240
0008, //85

#BARLINEON
#SCROLL 1
#GOGOSTART
3
#GOGOEND
0,
0,
#GOGOSTART
3
#GOGOEND
0,
0,

#GOGOSTART
30
#GOGOEND
00000
#GOGOSTART
3,
0
#GOGOEND
0000000,
3,
0, //93

0111,
10101011,
1111,
10101011,

1111,
11101111,
#GOGOSTART
7,
08, //101
#GOGOEND

10101210,
10101210,
10101210,
10101210,

10221022,
10221022,
10221022,
1, //109

#GOGOSTART
20221122,
20221122,
20220221,
12211210,

20221122,
20221122,
11221210,
500000000000000000000000000008000000000000000000, //117
#GOGOEND

4,
2,
#GOGOSTART
10221221,
12211210,

10221122,
10221122,
10221221,
500000000000000000000000000008000000000000000000, //125
#GOGOEND

1222,
2222,
1222,
11120202,

#GOGOSTART
11212121,
11212121,
11212121,
33,

600000000000000000000000000000000008000000000000,
#GOGOEND
0,
0,
0, //137
#END


COURSE:Normal
LEVEL:7
BALLOON:5,5,6,5,6,9,12,10
SCOREINIT:3430
SCOREDIFF:0

#START
4,
0,
2,
2,

3,
3,
5,
0,
000000000000000000000000000000000000000000000008, //9

0,
0,
0,
0,

#GOGOSTART
1111,
1110,
1111,
500000000000000000000000000008000000000000000000, //17
#GOGOEND

1110,
1110,
1110,
1110,

#GOGOSTART
7008,
7008,
7008,
#GOGOEND
0, //25

1110,
11101000,
1110,
11101000,

12,
02,
12,
02, //33

#GOGOSTART
11101000,
11101000,
11101000,
11101000,
#GOGOEND

1212,
1212,
#GOGOSTART
7008,
#SCROLL 3
500000000000000008000000
#SCROLL 1
000000000000000000000000, //41
#GOGOEND

2222,
2,
2222,
2,

#GOGOSTART
11101000,
11101000,
11101000,
11101000, //49
#GOGOEND

1111,
2,
1111,
2,

#GOGOSTART
11101000,
11101000,
11101000,
11101000, //57
#GOGOEND

3,
0,
2,
0,

500000000000000008000000200000000000500000000000, 
000008000000200000000000200000000000000000000000,
500000000000000008000000200000000000500000000000, 
000008000000200000000000200000000000000000000000, //65

1001,
2,
2001,
2,

#SCROLL 3
500000000008000000000000
#SCROLL 1
#GOGOSTART
700000000000000000000000,
000000000000000000800000
#SCROLL 5
500000000008
000000000000,
#SCROLL 1
7,
00080000, //73
#GOGOEND

12,
#BARLINEOFF
#BPMCHANGE 230
#SCROLL 0.72
12,
#BPMCHANGE 220
#SCROLL 0.58
12,
#BPMCHANGE 210
#SCROLL 0.52
12,

#BPMCHANGE 200
#SCROLL 0.5
11,
#MEASURE 3/4
1,
#MEASURE 4/4
11,
10000001, //81

#BPMCHANGE 210
00000001,
#BPMCHANGE 220
00000001,
#BPMCHANGE 230
7,
#BPMCHANGE 240
0008, //85

#BARLINEON
#SCROLL 1
#GOGOSTART
3
#GOGOEND
0,
0,
#GOGOSTART
3
#GOGOEND
0,
0,

#GOGOSTART
30
#GOGOEND
00000
#GOGOSTART
3,
0
#GOGOEND
0000000,
3,
0, //93

0111,
1111,
1111,
1111,

1111,
1111,
#GOGOSTART
7,
08, //101
#GOGOEND

10101110,
10101110,
10101110,
10101110,

1212,
1212,
1212,
1, //109

#GOGOSTART
10220020,
10220020,
10220220,
1212,

10220020,
10220020,
1212,
500000000000000000000000000008000000000000000000, //117
#GOGOEND

4,
2,
#GOGOSTART
10220220,
1212,

10220010,
10220010,
10220220,
500000000000000000000000000008000000000000000000, //125
#GOGOEND

1,
0,
1,
0,

#GOGOSTART
11101010,
11101010,
11101010,
33,

600000000000000000000000000000000008000000000000,
#GOGOEND
0,
0,
0, //137
#END


COURSE:Easy
LEVEL:5
BALLOON:4,4,5,4,5,8,10,8
SCOREINIT:4960
SCOREDIFF:0

#START
4,
0,
2,
2,

3,
3,
5,
0,
000000000000000000000000000000000000000000000008, //9

0,
0,
0,
0,

#GOGOSTART
1110,
1110,
1110,
500000000000000000000000000008000000000000000000, //17
#GOGOEND

11,
1,
11,
1,

#GOGOSTART
7008,
7008,
7008,
#GOGOEND
0, //25

1110,
1,
1110,
1,

12,
02,
12,
02, //33

#GOGOSTART
1110,
1110,
1110,
1110,
#GOGOEND

11,
11,
#GOGOSTART
7008,
#SCROLL 3
500000000000000008000000
#SCROLL 1
000000000000000000000000, //41
#GOGOEND

22,
2,
22,
2,

#GOGOSTART
1110,
1,
1110,
1, //49
#GOGOEND

11,
2,
11,
2,

#GOGOSTART
1110,
1,
1110,
1, //57
#GOGOEND

3,
0,
2,
0,

500000000000000000000008000000000000500000000000, 
000000000008000000000000200000000000000000000000,
500000000000000000000008000000000000500000000000, 
000000000008000000000000200000000000000000000000, //65

1,
2,
2,
2,

#SCROLL 3
500000000008000000000000
#SCROLL 1
#GOGOSTART
700000000000000000000000,
000000000000000000800000
#SCROLL 5
500000000008
000000000000,
#SCROLL 1
7,
00080000, //73
#GOGOEND

1,
#BARLINEOFF
#BPMCHANGE 230
#SCROLL 0.72
1,
#BPMCHANGE 220
#SCROLL 0.58
1,
#BPMCHANGE 210
#SCROLL 0.52
1,

#BPMCHANGE 200
#SCROLL 0.5
11,
#MEASURE 3/4
1,
#MEASURE 4/4
11,
10000001, //81

#BPMCHANGE 210
00000001,
#BPMCHANGE 220
00000001,
#BPMCHANGE 230
7,
#BPMCHANGE 240
0008, //85

#BARLINEON
#SCROLL 1
#GOGOSTART
3
#GOGOEND
0,
0,
#GOGOSTART
3
#GOGOEND
0,
0,

#GOGOSTART
30
#GOGOEND
00000
#GOGOSTART
3,
0
#GOGOEND
0000000,
1,
0, //93

0111,
0111,
0111,
0111,

0111,
1110,
#GOGOSTART
7,
08, //101
#GOGOEND

1111,
1110,
1111,
1110,

11,
11,
11,
1, //109

#GOGOSTART
2211,
2211,
11,
1,

2211,
2211,
11,
1, //117
#GOGOEND

4,
2,
#GOGOSTART
1110,
1110,

2211,
2211,
11,
1, //125
#GOGOEND

1,
0,
1,
0,

#GOGOSTART
1111,
1111,
1111,
33,

600000000000000000000000000000000008000000000000,
#GOGOEND
0,
0,
0, //137
#END