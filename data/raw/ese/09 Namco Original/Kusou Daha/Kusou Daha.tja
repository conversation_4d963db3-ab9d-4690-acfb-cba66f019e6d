//TJADB Project
TITLE:<PERSON><PERSON>u Daha
TITLEJA:空想打破
SUBTITLE:--<PERSON><PERSON> feat. Moe / WADIVE RECORD
SUBTITLEJA:ノイ feat. 萌 / WADIVE RECORD
BPM:180
WAVE:<PERSON><PERSON>u Daha.ogg
OFFSET:-1.843
DEMOSTART:59.153

//Shinuchi: 10530/6040/3280/1990

COURSE:Oni
LEVEL:8
BALLOON:
SCOREINIT:630,2030
SCOREDIFF:175

#START
10022002,
11210211,
10022002,
11110103,

00201120,
1020101000100011,
11111113,
0020201011101000,
1011011101101000, //9

1002100210102020,
1022101010200020,
1022102010102020,
1011102010101000,

1002100210102020,
1022101010200020,
1022102010102010,
01012210, //17

1011101020200011,
1010202000102210,
1010202010111010,
0010200210102022,

1011101020200011,
1010202000102210,
1010202010111010,
0010200210102022, //25

5,
000000000008000000100000100000200000100000200000,
1011101020102010,
0010202110101000,

5,
000000000008000000100000100000200000100000200000,
1011101020102010,
0010202110102000, //33

2222,
20220220,
2222,
20220220,

10101110,
10112212,
1110001110001110,
0011100030000000,
1001101000100010,
01210010, //43

#GOGOSTART
1002100210102020,
0011200220102020,
1022201022201020,
1001001000202000,

1002100210102020,
0011101020102020,
1002201002101030,
0000001110202020, //51

1002102010021020,
1022102010201020,
1002102010021020,
1022102010200030,

0010200210102022,
1002101020101022,
1002202010001000,
#MEASURE 2/4
3333,
#GOGOEND
0, //60

#MEASURE 4/4
#GOGOSTART
3020102011102020,
1020102011202020,
1020102011102020,
1020102022202220,

1020102011201120,
1020102011201120,
12121111,
1010111020222022, //68
#GOGOEND

1011102010201020,
11030000,
0, //71
#END


COURSE:Hard
LEVEL:4
BALLOON:
SCOREINIT:710,3390
SCOREDIFF:210

#START
1,
10010010,
1,
11110103,

01,
10110101,
10101011,
01011010,
500000000000000000000000000008000000100000000000, //9

10101122,
10110201,
10110102,
02022110,

10101122,
10110201,
10110102,
0, //17

11102201,
10220101,
10101111,
01201120,

11102201,
10220101,
10101111,
01201120, //25

5,
000000000008000000100000100000000000100000100000,
01022011,
01101110,

5,
000000000008000000100000100000000000100000100000,
01022011,
0110, //33

2222,
20220020,
2222,
20220220,

1111,
10110010,
11011011,
01103000,
0,
0, //43

#GOGOSTART
10101122,
01202120,
10210210,
1001001000000000,

10101122,
0122,
10210103,
00010111, //51

10121010,
10121210,
10121010,
10120203,

01201120,
10212121,
1211,
#MEASURE 2/4
1111,
#GOGOEND
0, //60

#MEASURE 4/4
#GOGOSTART
30101122,
10120202,
10101122,
10120202,

30101212,
10101212,
30111111,
30114011, //68
#GOGOEND

1,
00030000,
0, //71
#END


COURSE:Normal
LEVEL:4
BALLOON:
SCOREINIT:1020,6460
SCOREDIFF:395

#START
1,
1,
1,
10010103,

01,
10010000,
10101005,
000000000000000000000000000000000008000000000000,
0, //9

1110,
10000101,
00000001,
01011000,

1110,
10000101,
00000001,
0, //17

1100,
1100,
10101001,
0,

1100,
1100,
10101001,
0, //25

5,
000000000000000000000000000008000000000000100000,
01001011,
01000000,

5,
000000000000000000000000000008000000000000100000,
01001011,
0110, //33

2220,
2,
2220,
2,

1011,
1,
10010010,
01003000,
0,
0, //43

#GOGOSTART
10101011,
0111,
10010010,
500000000000000000000000000008000000000000000000, 

10101011,
0111,
10010003,
00010110, //51

1111,
10101110,
1111,
10110103,

01,
10110101,
0011,
#MEASURE 2/4
500000000000000008000000,
#GOGOEND
0, //60

#MEASURE 4/4
#GOGOSTART
3110,
10010100,
1110,
10010100,

3110,
1120,
3111,
3140, //68
#GOGOEND

1,
00030000,
0, //71
#END



COURSE:Easy
LEVEL:3
BALLOON:
SCOREINIT:1090,11850
SCOREDIFF:800

#START
1,
1,
1,
10000003,

0,
1,
10000005,
000000000000000000000000000000000008000000000000,
0, //9

11,
10000001,
00000001,
0,

11,
10000001,
00000001,
0, //17

1,
1,
10001001,
0,

1,
1,
10001001,
0, //25

5,
000000000000000000000000000008000000000000000000,
1,
1,

5,
000000000000000000000000000008000000000000000000,
15,
000008000000000000000000100000000000000000000000, //33

2,
2,
2,
2,

11,
1,
1001,
03,
0,
0, //43

#GOGOSTART
10001001,
0,
10010000,
500000000000000000000000000008000000000000000000, 

10001001,
0,
10010003,
0, //51

1110,
11,
1110,
10010003,

01,
10010001,
0011,
#MEASURE 2/4
500000000000000008000000,
#GOGOEND
0, //60

#MEASURE 4/4
#GOGOSTART
31,
1,
11,
1,

31,
1,
31,
33, //68
#GOGOEND

1,
00030000,
0, //71
#END