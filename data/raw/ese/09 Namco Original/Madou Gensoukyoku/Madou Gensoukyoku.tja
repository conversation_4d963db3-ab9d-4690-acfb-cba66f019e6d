//TJADB Project
TITLE:<PERSON><PERSON>uk<PERSON>
TITLEJA:魔導幻想曲
SUBTITLE:--<PERSON><PERSON><PERSON>
BPM:150
WAVE:<PERSON><PERSON>.ogg
OFFSET:-1.665
DEMOSTART:6.465

//shinuchi: 5130/3550/2010/1180

COURSE:Oni
LEVEL:9
BALLOON:7,3,3,3,3,5,7,14,14
SCOREINIT:420,1250
SCOREDIFF:98

#START
2000200012021122,
#GOGOSTART
1001001021202010,
#GOGOEND
0211212111211121,

1021201000102021,
1001201011012000,
1021201000102021,
1001201011212212, //7

1012201000102021,
1011201011012022,
1012201000102021,
1011201011212212,

1010201020102210,
1010201020102212,
2010201020102210,
2010201020101122, //15

100200200200202020100100100100700000000000000000,
080000000000000000000000000000000000200200100100,
1121221211212212,
708708708708,

100000200000200200100000000000100200000200101010,
1010221010102210,
1010221010102211,
2222111122221111, //23

1010201010102210,
100200200100100200200200101010700000000000080000,
100000100000200000100000100000100000200200101010,
100000202020200000100000100200200200101010100100,

1021201211212011,
1021202122121011,
1021201212212011,
100000100000202010100100700000000000000000800000, //31

0202,
0000200000002222,
2202,
0000200000002222,

100000000200200000000000200000000000202020200000,
1002200020002222,
1002200020001211,
2112112112112112, //39

#GOGOSTART
1001102211011022,
1001102211012021,
1001102211011022,
1001102211011221,

1001202211011022,
1001202211012021,
1011221101221221,
2211221122221111, //47

1021102211011022,
1021102211011221,
1021102211011222,
1021102211011221,

1021202211011022,
1021202211011221,
1021202211011222,
101010101010100200000300000000000000000000000000, //55
#GOGOEND

00040000,
1101001010101221,
00030000,
0112112112112112,

1012021021202010,
000000100000000200100000202020200200101010100000,
2022021021202210,
1022211122112211, //63

100000100000200000000000200200000200200000101010,
1021102010002000,
100000100000200000000000200200000200200000101010,
1021102010002000,

1220201220201220,
7008,
2110102110102110,
7008, //71

#MEASURE 3/4
#GOGOSTART
101110221110,
101110221220,
100000200200100000100100202020200000,
100000200100200000100100202020200000,

100000100100100000100100101010101010,
100000100100100000200200202020202020,
222210211120,
100200100200100000200200101010101010, //79
#GOGOEND

102020202212,
#MEASURE 4/4
#BARLINEOFF
1,
0, //82
#END


COURSE:Hard
LEVEL:7
BALLOON:12,2,2,2,2,10,10
SCOREINIT:550,2260
SCOREDIFF:138

#START
#BARLINEOFF
0,
#BARLINEON
#GOGOSTART
9009,
#GOGOEND
8,

10210120,
10201120,
10210120,
10221020, //7

20120210,
22021010,
22120210,
2020101020201110,

1010001000102220,
1010001000102222,
2010001000102220,
2010001000102220, //15

500000000000000000000000000000000000000000000008,
0,
11211121,
708708708708,

12210101,
1010222010102220,
1010222010102220,
1111100000001000, //23

1010201010102220,
34434430,
1010201010101110,
34430030,

1010222010102220,
1010222010102220,
1011101110101000,
2020222040400000, //31

0202,
0000200000002222,
2202,
0000200000001111,

1002200000002000,
1002200000001111,
1002200000002000,
500000000000000000000000000000000008000000000000, //39

#GOGOSTART
1001100010011000,
1001100020002000,
1001100010011000,
1001100010002220,

1001100010011000,
1001100020002000,
11201120,
1110202011102020, //47

1001100010011000,
1001100010002220,
1001100010011000,
1001100010002220,

2002200020022000,
2002200020002220,
2002200020022000,
1001100300000000, //55
#GOGOEND

00040000,
1101001010101000,
00030000,
1110111011101110,

2002002020202010,
0010001000101110,
2002002020202011,
1011101110100010, //63

11002200,
01111020,
11002200,
01111020,

1020201020201110,
7008,
2010102010102220,
7008, //71

#MEASURE 3/4
#GOGOSTART
101110100000,
101110100000,
344300,
344300,

102220100000,
102220100000,
344344,
304040111111, //79
#GOGOEND

1,
#MEASURE 4/4
#BARLINEOFF
0,
0, //82
#END


COURSE:Normal
LEVEL:5
BALLOON:10,2,2,2,2,7,7
SCOREINIT:690,4180
SCOREDIFF:188

#START
#BARLINEOFF
0,
#BARLINEON
#GOGOSTART
9009,
#GOGOEND
8,

1212,
1110,
1212,
10111000, //7

2121,
2220,
2121,
20222000,

11010101,
11010102,
22020202,
22020202, //15

500000000000000000000000000000000000000000000008,
0,
11101110,
708708708708,

0,
1212,
1212,
3300, //23

11101110,
30030030,
11101110,
30030030,

1212,
1212,
11101110,
22204400, //31

0202,
0202,
0202,
0202,

1202,
1202,
1202,
500000000000000000000000000000000008000000000000, //39

#GOGOSTART
10101011,
1122,
10101011,
10001011,

10101011,
1122,
500000000008000000000000500000000008000000000000,
600000000008000000000000600000000008000000000000, //47

1110,
10101022,
1110,
10101022,

1110,
10101022,
1110,
3000000300000000, //55
#GOGOEND

00040000,
500000000000000000000000000000000000000008000000,
00030000,
1111,

500000000000000000000000000000000000000008000000,
0,
600000000000000000000000000000000000000008000000,
0, //63

11002200,
01111000,
11002200,
01111000,

10110110,
7008,
20220220,
7008, //71

#MEASURE 3/4
#GOGOSTART
101101,
101100,
303303,
303300,

102102,
102100,
304304,
304300, //79
#GOGOEND

4,
#MEASURE 4/4
#BARLINEOFF
0,
0, //82
#END


COURSE:Easy
LEVEL:3
BALLOON:8,2,2,2,2,6,6
SCOREINIT:570,6130
SCOREDIFF:163

#START
#BARLINEOFF
0,
#BARLINEON
#GOGOSTART
9009,
#GOGOEND
8,

11,
1110,
11,
1110, //7

22,
2220,
22,
2220,

1001,
1001,
2002,
2002, //15

500000000000000000000000000000000000000000000008,
0,
1111,
708708708708,

0,
1210,
1210,
3300, //23

1111,
30030030,
1111,
30030030,

11,
1110,
1111,
2240, //31

0202,
0202,
0202,
0202,

1202,
1202,
1202,
500000000000000000000000000008000000000000000000, //39

#GOGOSTART
1110,
1011,
1110,
11,

1110,
1011,
500000000008000000000000500000000008000000000000,
600000000008000000000000600000000008000000000000, //47

1110,
1012,
1110,
1012,

1110,
1012,
1110,
3000000300000000, //55
#GOGOEND

00040000,
500000000000000000000000000000000000000008000000,
00030000,
1111,

500000000000000000000000000000000000000008000000,
0,
600000000000000000000000000000000000000008000000,
0, //63

12,
1120,
12,
1120,

20020020,
7008,
20020020,
7008, //71

#MEASURE 3/4
#GOGOSTART
11,
11,
33,
33,

11,
11,
33,
33, //79
#GOGOEND

4,
#MEASURE 4/4
#BARLINEOFF
0,
0, //82
#END