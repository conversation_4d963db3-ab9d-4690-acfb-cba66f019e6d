//TJADB Project
TITLE:SoulStone -Yamikui Circus Troupe-
TITLEJA:SoulStone -闇喰イサァカス団-
SUBTITLE:--SEIFU-MEIGETSU (Drop × <PERSON>ra Hatsuki)
SUBTITLEJA:清風明月（Drop×葉月ゆら）
BPM:150
WAVE:SoulStone Yamikui Circus Troupe.ogg
OFFSET:-0.779
DEMOSTART:56.160


COURSE:Oni
LEVEL:10
BALLOON:
SCOREINIT:930
SCOREDIFF:0

#START
#SCROLL 1.5
3,
500000000000000000000000000000000000000008000000,

#SCROLL 1
33,
300000300222,
111221111221,
111111222222101010101010,

2111211121112111,
2111211121112111,
2011201120112011,
111111222222100000300000, //10

1011202210120220,
1011202210121110,
1011202210120220,
3010301030221111,

1011202210120220,
1011202210121110,
211111111111211111111111,
2000000021012020, //18

1020101110210120,
1022101121212010,
1020112010220120,
1020112011212212,

1020101120120220,
1020101122112010,
2211221121121121,
100000000000100000000000202020100100100100100100, //26

100000200000200000202020100100100100200200200200,
100000200000200000202020100100100100200200100200,
100000200000200000202020100100100100200200100100,
100000202020100000100100202020100100200200100100,

1022112211112222,
1022112211112212,
100000200200100100200200101010101010200200100100,
1020201122112211,
100000000000000000000000200200101010200000000000, //35

#GOGOSTART
1121112111221122,
1121112112211221,
1121112122112211,
100100200100100100200100201010101010200100100100,

1122112212212212,
1122112212212212,
2112112121121120,
400000000400000000400000200200101010200200101010, //43

1121112111221122,
1121112112211221,
1121112122112211,
100100200100100100200100201010101010200100100100,

1122112212221222,
100100200200100100200200202020101010101010100000,
3003003030030030, //50
#GOGOEND

#BPMCHANGE 212
#SCROLL 0.7
1,
#SCROLL 0.8
1,
#SCROLL 0.85
1,
#SCROLL 0.9
122222221111,

10210120,
#SCROLL 0.95
10210120,
#SCROLL 1
211111211111,
33333333, //58

#GOGOSTART
111222111222,
121222121222,
111222111222,
112122112122,

100100200111200100200100,
100100200111200100200100,
#BPMCHANGE 207
#SCROLL 1.02
111
#BPMCHANGE 197
#SCROLL 1.08
211
#BPMCHANGE 187
#SCROLL 1.13
211
#BPMCHANGE 177
#SCROLL 1.2
222,
#MEASURE 15/16
#BPMCHANGE 160
#SCROLL 1.33
100000000000000000000000
#BPMCHANGE 142
#SCROLL 1
101010001000
#BPMCHANGE 97
#SCROLL 1.46
100
#BPMCHANGE 93
#SCROLL 1.53
100
#BPMCHANGE 77
#SCROLL 1.84
100, //66
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 150
#SCROLL 0.95
1020202020020020,
#SCROLL 0.96
100200200200200200222222,
1020202020020020,
#SCROLL 0.97
400200400200400200222222,

1020202020020020,
#SCROLL 0.98
100200200200200200222222,
1020202020020020,
#SCROLL 0.99
400200400200400200222222,
1,
#SCROLL 1
0, //76

3,
300000211111,
33,
300000000000000000000000200200101010200000000000,

1002102010221020,
1002102010021110,
3003003030030030,
222222111111222111200000, //84

#SCROLL 1.5
#GOGOSTART
1121112111221122,
1121112112211221,
1121112122112211,
101010200100101010200100201010101010200100100100,

1122112212221222,
100100200200100100200200202020101010201020102000,
3003003030030030, //91
#GOGOEND

#SCROLL 1
1011202210120220,
1011202210121110,
221111211111221111211211,
200000000000000000000000200200101010200000200000,

3,
0,
0, //98
#END


COURSE:Hard
LEVEL:7
BALLOON:3
SCOREINIT:1810
SCOREDIFF:0

#START
#SCROLL 1.5
3,
500000000000000000000000000000000000000008000000,

#SCROLL 1
33,
300000300111,
33,
500000000000000000000000000000000008000000000000,

1111,
1111,
1011101110111011,
1033, //10

1020102010020020,
12121110,
1020102010020020,
3010301030101110,

1020102010020020,
12121110,
500000000000000000000000000000000000000008000000,
3022, //18

1000200010110010,
1000100010222010,
1000100010110020,
2000102220000000,

1000200010110020,
2000102220001000,
1000100010010010,
1000100011101110, //26

12221000,
12221000,
12221011,
1000100022201110,

12221111,
12221111,
1020202011101110,
11111111,
34, //35

#GOGOSTART
1020111010201110,
100000200000100100100000500000000000000008000000,
1020111010201110,
500000000008000000200200500000000000000008000000,

2022202220202020,
2022202220202011,
1001102010011020,
3003003030202020, //43

1020111010201110,
100000200000100100100000500000000000000008000000,
1020111010201110,
100000000200000000200000500000000000000008000000, 

2011102020111020,
2011102020111110,
3003003030030030, //50
#GOGOEND

#BPMCHANGE 212
#SCROLL 0.7
1,
#SCROLL 0.8
1,
#SCROLL 0.85
1,
#SCROLL 0.9
500000000000000000000000000000000000000008000000,

10210120,
#SCROLL 0.95
10210120,
#SCROLL 1
21121121,
33333333, //58

#GOGOSTART
1212,
111102202200,
1212,
111101102200,

10201120,
10201120,
#BPMCHANGE 207
#SCROLL 1.02
101
#BPMCHANGE 197
#SCROLL 1.08
101
#BPMCHANGE 187
#SCROLL 1.13
101
#BPMCHANGE 177
#SCROLL 1.2
111,
#MEASURE 15/16
#BPMCHANGE 160
#SCROLL 1.33
10000000
#BPMCHANGE 142
#SCROLL 1.49
7000
#BPMCHANGE 97
0
#BPMCHANGE 93
0
#BPMCHANGE 77
8, //66
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 150
#SCROLL 0.95
1020202020020020,
#SCROLL 0.96
12222220,
1020202020020020,
#SCROLL 0.97
42424242,

1020202020020020,
#SCROLL 0.98
12222220,
1020202020020020,
#SCROLL 0.99
42424242,
3,
#SCROLL 1
0, //76

3,
3,
33,
33,

1111,
1000100010011110,
3003003030030030,
2220111022204000, //84

#GOGOSTART
1020111010201110,
100000200000100100100000500000000000000008000000,
1020111010201110,
100000000200000000200000500000000000000008000000, 

2011102020111020,
2011102020111110,
3003003030030030, //91
#GOGOEND

1020102010020020,
12121110,
500000000000000000000000000000000000000008000000,
3022,

3,
0,
0, //98
#END


COURSE:Normal
LEVEL:6
BALLOON:2
SCOREINIT:3420
SCOREDIFF:0

#START
3,
500000000000000000000000000008000000000000000000,

33,
33,
33,
500000000000000000000000000000000008000000000000, 

1111,
1110,
1111,
500000000000000000000000000000000008000000000000, //10

1210,
10201110,
1210,
3333,

1210,
10201110,
500000000000000000000000000000000008000000000000,
3, //18

1000100010010010,
2220,
1000100020020020,
20022000,

1000200010020020,
20022010,
1000100010010010,
500000000000000000000008000000000000000000000000, //26

1210,
1210,
10201011,
1110,

10201110,
10201110,
10201110,
22,
34, //35

#GOGOSTART
1000100010010010,
1001001020000010,
1001001010101010,
500000000000000000000000000008000000000000000000,

2002002000202000,
2002002000202000,
1001001010010010,
30033020, //43

1000100010010010,
1001001020000010,
1001001010101010,
500000000000000000000000000008000000000000000000, 

20202121,
2220,
3003003030030030,//50
#GOGOEND

#BPMCHANGE 212
#SCROLL 0.7
1,
#SCROLL 0.8
1,
#SCROLL 0.85
1,
#SCROLL 0.9
500000000000000000000000000000000008000000000000,

10010000,
#SCROLL 0.95
10010000,
#SCROLL 1
11,
3333, //58

#GOGOSTART
1112,
1120,
1112,
1120,

1211,
1211,
#BPMCHANGE 207
#SCROLL 1.02
1
#BPMCHANGE 197
#SCROLL 1.08
1
#BPMCHANGE 187
#SCROLL 1.13
1
#BPMCHANGE 177
#SCROLL 1.2
1,
#MEASURE 15/16
#BPMCHANGE 160
#SCROLL 1.33
10000000
#BPMCHANGE 142
#SCROLL 1.49
7000
#BPMCHANGE 97
0
#BPMCHANGE 93
0
#BPMCHANGE 77
8, //66
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 150
#SCROLL 0.95
2,
#SCROLL 0.96
22,
2,
#SCROLL 0.97
44,

2,
#SCROLL 0.98
22,
2,
#SCROLL 0.99
44,
3,
#SCROLL 1
0, //76

3,
3,
33,
33,

1111,
1110,
3003003030030030,
500000000000000000000000000008000000000000000000, //84

#GOGOSTART
1000100010010010,
1001001020000010,
1001001010101010,
500000000000000000000000000008000000000000000000,

20202121,
2220,
3003003030030030, //91
#GOGOEND

1210,
10201110,
500000000000000000000000000000000008000000000000,
3022,

3,
0,
0, //98
#END


COURSE:Easy
LEVEL:3
BALLOON:
SCOREINIT:5400
SCOREDIFF:0

#START
3,
500000000000000000000000000008000000000000000000,

33,
33,
33,
500000000000000000000008000000000000000000000000, 

1110,
1110,
1111,
500000000000000000000008000000000000000000000000, //10

11,
1110,
11,
3330,

11,
1110,
500000000000000000000000000008000000000000000000,
3, //18

11,
1110,
11,
22,

12,
11,
1110,
500000000000000000000008000000000000000000000000, //26

12,
12,
12,
1110,

1210,
1210,
1110,
22,
34, //35

#GOGOSTART
1111,
12,
1111,
500000000000000000000000000008000000000000000000,

2220,
2220,
11,
33, //43

1111,
12,
1111,
500000000000000000000000000008000000000000000000,

2220,
2220,
33, //50
#GOGOEND

#BPMCHANGE 212
#SCROLL 0.7
3,
#SCROLL 0.8
1,
#SCROLL 0.85
1,
#SCROLL 0.9
500000000000000000000000000008000000000000000000,

10010000,
#SCROLL 0.95
10010000,
#SCROLL 1
11,
3330, //58

#GOGOSTART
11,
12,
11,
12,

34,
34,
#BPMCHANGE 207
#SCROLL 1.02
1
#BPMCHANGE 197
#SCROLL 1.08
0
#BPMCHANGE 187
#SCROLL 1.13
1
#BPMCHANGE 177
#SCROLL 1.2
0,
#MEASURE 15/16
#BPMCHANGE 160
#SCROLL 1.33
10000000
#BPMCHANGE 142
#SCROLL 1.49
0000
#BPMCHANGE 97
0
#BPMCHANGE 93
0
#BPMCHANGE 77
0, //66
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 150
#SCROLL 0.95
2,
#SCROLL 0.96
0,
2,
#SCROLL 0.97
4,

2,
#SCROLL 0.98
0,
2,
#SCROLL 0.99
4,
3,
#SCROLL 1
0, //76

3,
3,
33,
33,

1110,
1110,
33,
500000000000000000000000000008000000000000000000, //84

#GOGOSTART
1111,
12,
1111,
500000000000000000000000000008000000000000000000,

2220,
2220,
33, //91
#GOGOEND

1110,
1110,
500000000000000000000000000008000000000000000000,
32,

3,
0,
0, //98
#END