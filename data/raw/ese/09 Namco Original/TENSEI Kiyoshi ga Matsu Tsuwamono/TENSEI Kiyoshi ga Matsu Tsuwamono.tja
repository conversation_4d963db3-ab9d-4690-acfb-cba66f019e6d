//TJADB Project
TITLE:TENSEI -<PERSON><PERSON> ga <PERSON><PERSON>suwamono-
TITLEJA:転生〈TENSEI〉-喜与志が待つ強者-
SUBTITLE:--Reincarnated Wada Kiyoshi prod. Licht Tsuboi (BNSI)
SUBTITLEJA:転生和田喜与志 prod. 坪井リヒト(BNSI)
BPM:85
WAVE:TENSEI Kiyoshi ga Matsu Tsuwamono.ogg
OFFSET:-1.914
DEMOSTART:85.519

COURSE:Edit
LEVEL:10
BALLOON:12,22
SCOREINIT:1000
SCOREDIFF:0

#START
11000000,
11000000,
4,
#MEASURE 2/4
0, //4

#MEASURE 4/4
#BPMCHANGE 200
#GOGOSTART
1020112010201120,
1020112011210120,
1020112011021020,
1020112011221020,

1020112010201120,
1020112011021120,
1020112010201120,
3000122010201000,
#GOGOEND
1022101022101022,
1022112211112210, //14

1110201000102010,
1011201011102010,
1110201000102010,
1011201111102012,

1110201000102010,
1011201011102010,
1110221111221110,
1011221121122012, //22

1110201000102010,
1011201011102010,
2111211121111121,
2000211220203000,
1110201110112010,
33040030,

#GOGOSTART
1212121212121212,
1212121212121212,
1211111211111211,
1112111121112000, //32
#GOGOEND

3030303000001220,
1022101011221220,
33330022,
1020101120122022,

1000201010202011,
11221221,
10211221,
1011201211102000, //40

3030303000001220,
1022102011221020,
33330022,
1020101120122022,

1000201010202011,
11221221,
4011104011104040,
0, //48

#SCROLL 0.75
300020100000200010000022,
201200202120,
100020100000200010000022,
200010200020200022100000,

300020100000200010000022,
201200202120,
5,
000000000000000000000008000000000000000000000000, //56

#SCROLL 1.5
#GOGOSTART
1112211121112112,
100000100200200000600008000000200200100100200200,
102020200200200000101020200200200000101010200200,
2011222211211210,

3001102110011020,
100211100122100111200100,
200200100200200100200200100200200100101010200200,
100200200100200100202020100100200200100100200200,
#GOGOEND
#SCROLL 1
300000000000700000000000000000000000000000080000,
0, //66

#GOGOSTART
1011112021111011,
1011122022111011,
1011222022211021,
1012222022221022,

1011212021211021,
1012112021121012,
1012122022121012,
1012212021221022, //74

1011201121012012,
1012201221012012,
1021202112021022,
1221221221112122,

1011201121012012,
1012201221012010,
400111100400111100400111,
2112121121121110, //82

3012201020122010,
1221201020122010,
1012201020122010,
1221201020122010,

1012201020122010,
1221201020122010,
100222200100100222200100,
1221221122122210, //90

1022112010211120,
1020112011211120,
1020112111211222,
1121112112212222,
#GOGOEND
#BPMCHANGE 80
#SCROLL 2.5
1, //95

#BPMCHANGE 190
#SCROLL 1.05
#GOGOSTART
3000
#BPMCHANGE 238
#SCROLL 0.84
2222
#BPMCHANGE 279
#SCROLL 0.71
2011
#BPMCHANGE 230
#SCROLL 0.86
1011,
#BARLINEOFF
200200100100200200100100200100100200100000000020,
#BPMCHANGE 180
#SCROLL 1.11
200020000010100000000000100000200200200000100000,
#BPMCHANGE 130
#SCROLL 1.53
12111117
#BPMCHANGE 74
#SCROLL 2.7
0000
#BPMCHANGE 60
#SCROLL 3.33
0008, //99

#BARLINEON
#BPMCHANGE 170
#SCROLL 1.17
300000002220100000000000,
#GOGOEND
0,
0,
0,
0, //104
#END


COURSE:Oni
LEVEL:8
BALLOON:80
SCOREINIT:1610
SCOREDIFF:0

#START
11000000,
11000000,
4,
#MEASURE 2/4
0, //4

#MEASURE 4/4
#BPMCHANGE 200
#GOGOSTART
1000111010201000,
1000111010201000,
1000111010020020,
1020111010201000,

1000111010201020,
1000111010201000,
6,
000000000000000000000000000000000008000000000000,
#GOGOEND
10110110,
1000111022201020, //14

10211020,
10211120,
10211020,
1000201010202022,

10211020,
10211120,
1020201110202010,
1020201110202022, //22

10211020,
10211120,
2000111020001110,
2000222020203000,
10211020,
33040030,

#GOGOSTART
1212101212101212,
1012121012121000,
3011103011103011,
1030111020002000, //32
#GOGOEND

33330022,
1020100011102000,
33330022,
1020100020102011,

10211020,
10211220,
10210120,
10211220, //40

33330022,
1020100011102000,
33330022,
1020100020102022,

10211020,
10211220,
30030033,
0, //48

#SCROLL 0.75
300100201002,
200200202020,
100100201001,
101102200100,

300100201002,
200200202020,
5,
000000000000000000000008000000000000000000000000, //56

#SCROLL 1
#GOGOSTART
1011101110111011,
100000200000200000600000000000000008000000000000,
1111101111101111,
1011111020222220,

3001102010011020,
1011101110201000,
5,
000000000000000000000000000000000008000000000000,
#GOGOEND
3,
0, //66

#GOGOSTART
1010111010101110,
1010111010111010,
2020222020202220,
2020222020222020,

1010111010101110,
1010111010111010,
2020222020202220,
2020222020202000, //74

1110201020102010,
1110201020102010,
1120201020102010,
500000000000000000000008000000000000200000200000,

1110201020102010,
1110201020102010,
30030030,
000000300000000000000000600000000008000000000000, //82

1000201020102011,
20212121,
1000201020102011,
20212121,

1000201020102011,
20212121,
1000201020102011,
2022112211111010, //90

1110201020102010,
1110201020102010,
1120201020102010,
1120201020102220,
#GOGOEND
#BPMCHANGE 80
#SCROLL 2.5
3, //95

#BPMCHANGE 190
#SCROLL 1.05
#GOGOSTART
7
#BPMCHANGE 238
#SCROLL 0.84
0
#BPMCHANGE 279
#SCROLL 0.71
0
#BPMCHANGE 230
#SCROLL 0.86
0,
#BARLINEOFF
0,
#BPMCHANGE 180
#SCROLL 1.11
0,
#BPMCHANGE 130
#SCROLL 1.53
00000000
#BPMCHANGE 74
#SCROLL 2.7
0000
#BPMCHANGE 60
#SCROLL 3.33
0008, //99

#BARLINEON
#BPMCHANGE 170
#SCROLL 1.17
31,
#GOGOEND
0,
0,
0,
0, //104
#END


COURSE:Hard
LEVEL:6
BALLOON:62
SCOREINIT:2650
SCOREDIFF:0

#START
11000000,
11000000,
4,
#MEASURE 2/4
0, //4

#MEASURE 4/4
#BPMCHANGE 200
#GOGOSTART
10101210,
10101210,
1000100010020020,
10121210,
10101212,
10121020,
6,
000000000000000000000000000000000008000000000000,
#GOGOEND
10110110,
10102220, //14

10111020,
10111020,
10111020,
10101120,

10111020,
10111020,
10102220,
10102022, //22

10111020,
10111020,
21102110,
20201110,
10111020,
33040030,

#GOGOSTART
12121212,
1020102011111000,
30030030,
03002020, //32
#GOGOEND

30300022,
1122,
30300022,
1122,

1012,
10011020,
10110020,
10102220, //40

30300022,
1122,
30300022,
1122,

10111020,
10010010,
30030033,
0, //48

#SCROLL 0.75
32,
2220,
12,
202202200000,

32,
2220,
3,
0, //56

#SCROLL 1
#GOGOSTART
5,
000000000000000000000000000008000000000000000000,
30230230,
23023020,

1001101010011010,
1212,
5,
000000000000000000000000000000000008000000000000,
#GOGOEND
3,
0, //66

#GOGOSTART
10101110,
10101110,
20202220,
20202220,

10101110,
10101110,
20202220,
20202220, //74

10202220,
10202220,
10202220,
500000000000000000000008000000000000000000000000,

10102120,
10102120,
30030030,
03003000, //82

10101120,
10101120,
10102120,
10102120,

10101120,
10101120,
10102120,
10102120, //90

10101210,
10101210,
10101210,
1000100010001110,
#GOGOEND
#BPMCHANGE 80
#SCROLL 2.5
3, //95

#BPMCHANGE 190
#SCROLL 1.05
#GOGOSTART
9
#BPMCHANGE 238
#SCROLL 0.84
0
#BPMCHANGE 279
#SCROLL 0.71
0
#BPMCHANGE 230
#SCROLL 0.86
0,
#BARLINEOFF
0,
#BPMCHANGE 180
#SCROLL 1.11
0,
#BPMCHANGE 130
#SCROLL 1.53
0009
#BPMCHANGE 74
#SCROLL 2.7
00
#BPMCHANGE 60
#SCROLL 3.33
00, //99

#BARLINEON
#BPMCHANGE 170
#SCROLL 1.17
08,
#GOGOEND
0,
0,
0,
0, //104
#END


COURSE:Normal
LEVEL:5
BALLOON:7,43
SCOREINIT:5060
SCOREDIFF:0

#START
11000000,
11000000,
4,
#MEASURE 2/4
0,

#MEASURE 4/4
#BPMCHANGE 200
#GOGOSTART
1110,
1110,
1110,
1,

1110,
1110,
6,
000000000000000000000000000000000008000000000000,
#GOGOEND
0,
0, //14

11,
1011,
11,
1,

11,
1011,
500000000000000000000000000000000000000008000000,
0, //22

11,
1011,
33,
3001,
1002,
30030000,

#GOGOSTART
1111,
10101110,
30030030,
03004000, //32
#GOGOEND

3300,
2220,
3300,
2220,

11,
1011,
700000000000000000000000000000000000000000000008,
0, //40

3300,
2220,
3300,
2220,

11,
10010010,
600000000000000000000000000000000000000008000000,
0, //48

#SCROLL 0.75
32,
2220,
12,
2,

32,
2220,
3,
0, //56

#SCROLL 1
#GOGOSTART
5,
000000000000000000000000000008000000000000000000,
30030030,
03003000,

11,
1111,
5,
000000000000000000000000000008000000000000000000,
#GOGOEND
3,
0, //66

#GOGOSTART
1110,
1110,
2220,
2220,

1110,
1110,
2220,
2220, //74

1220,
1220,
1220,
1,

1120,
1120,
6,
000000000000000000000008000000000000000000000000, //82

1110,
1110,
1122,
1110,

1110,
1110,
1122,
1110, //90

1120,
1120,
1122,
1,
#GOGOEND
#BPMCHANGE 80
#SCROLL 2.5
3, //95

#BPMCHANGE 190
#SCROLL 1.05
#GOGOSTART
9
#BPMCHANGE 238
#SCROLL 0.84
0
#BPMCHANGE 279
#SCROLL 0.71
0
#BPMCHANGE 230
#SCROLL 0.86
0,
#BARLINEOFF
0,
#BPMCHANGE 180
#SCROLL 1.11
0,
#BPMCHANGE 130
#SCROLL 1.53
0009
#BPMCHANGE 74
#SCROLL 2.7
00
#BPMCHANGE 60
#SCROLL 3.33
00, //99

#BARLINEON
#BPMCHANGE 170
#SCROLL 1.17
08,
#GOGOEND
0,
0,
0,
0, //104
#END


COURSE:Easy
LEVEL:3
BALLOON:33
SCOREINIT:7670
SCOREDIFF:0

#START
11000000,
11000000,
4,
#MEASURE 2/4
0, //4

#MEASURE 4/4
#BPMCHANGE 200
#GOGOSTART
11,
1,
11,
1,

11,
11,
6,
000000000000000000000000000000000008000000000000,
#GOGOEND
0,
0, //14

1,
1,
11,
1,

1,
11,
500000000000000000000000000000000000000008000000,
0, //22

1,
11,
33,
3,
1,
30030000,

#GOGOSTART
1110,
1110,
6,
000000000000000000000008000000000000000000000000, //32
#GOGOEND

3300,
0,
3300,
0,

1,
11,
1,
0, //40

3300,
0,
3300,
0,

1,
1,
600000000000000000000000000000000000000008000000,
0, //48

#SCROLL 0.75
3,
2,
1,
2,

3,
4,
3,
0, //56

#GOGOSTART
#SCROLL 1
5,
000000000000000000000000000008000000000000000000,
30030030,
03003000,

1,
1,
5,
000000000000000000000000000008000000000000000000,
#GOGOEND
0,
0, //66

#GOGOSTART
11,
1110,
11,
2,

11,
1110,
11,
2, //74

1110,
1110,
1110,
1,

1110,
1110,
6,
000000000000000000000008000000000000000000000000, //82

11,
1110,
11,
1110,

11,
1110,
11,
1110, //90

1110,
1110,
1110,
1,
#GOGOEND
#BPMCHANGE 80
#SCROLL 2.5
3, //95

#BPMCHANGE 190
#SCROLL 1.05
#GOGOSTART
9
#BPMCHANGE 238
#SCROLL 0.84
0
#BPMCHANGE 279
#SCROLL 0.71
0
#BPMCHANGE 230
#SCROLL 0.86
0,
#BARLINEOFF
0,
#BPMCHANGE 180
#SCROLL 1.11
0,
#BPMCHANGE 130
#SCROLL 1.53
0009
#BPMCHANGE 74
#SCROLL 2.7
00
#BPMCHANGE 60
#SCROLL 3.33
00, //99

#BARLINEON
#BPMCHANGE 170
#SCROLL 1.17
08,
#GOGOEND
0,
0,
0,
0, //104
#END