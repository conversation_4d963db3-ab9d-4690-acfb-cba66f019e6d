//TJADB Project
TITLE:<PERSON><PERSON> no <PERSON><PERSON><PERSON>
TITLEJA:黄泉のイザナミ
SUBTITLE:--<PERSON><PERSON> feat. <PERSON><PERSON> & <PERSON><PERSON><PERSON> Yabuki
BPM:172
WAVE:<PERSON><PERSON> no <PERSON><PERSON><PERSON>.ogg
OFFSET:-2.951
DEMOSTART:59.099

//Shinuchi: 3520/2550/1400/1110

COURSE:Oni
LEVEL:8
BALLOON:
SCOREINIT:370,1140
SCOREDIFF:85

#START
3000000010002220,
1000222010002220,
1010222010102220,
1010222010102220,

3000222010102120,
1010222010102120,
1010222010102120,
#BPMCHANGE 172.45
#SCROLL 0.99
1010222030000000, //8

#BPMCHANGE 172
#SCROLL 1
1000201110002000,
1000201110102020,
1000201210102020,
1000202210201022,

1000201110002000,
1000201110102020,
1000201110102020,
10210120, //16

1010201110102000,
1010201110102020,
1110201210102011,
1010202210201000,

1010202210102000,
1010202210102020,
1110201210102022,
1110202211201120, //24

#BPMCHANGE 172.75
#SCROLL 0.99
30002002,
#MEASURE 3/4
#BPMCHANGE 172
#SCROLL 1
000022022020,
#MEASURE 4/4
30002002,
#MEASURE 3/4
002022202000,

#MEASURE 4/4
30002002,
#MEASURE 3/4
000022022020,
#MEASURE 4/4
30002002,
#MEASURE 3/4
#BPMCHANGE 171
002022202000, //32

#MEASURE 4/4
#BPMCHANGE 172
10221222,
1022102010201000,
1000202010222020,
1022102010201000,

1020202220102020,
1022102010221000,
1000200011201020,
1120201122101000,
2000222020200020,
02203000,//42

#GOGOSTART
1110200011021020,
1000202010222020,
1110200011021020,
1000202010222020,

1120200011021020,
1120200012021120,
1111222211112222,
1010200011021020, //50

1110200011021020,
1120202211021020,
1110200011021020,
1120202211021020,

1110202011021020,
1110202212021120,
1111222211112222,
1111222211112000, //58
#GOGOEND

300221221221,
200211211211,
200221200211,
200221200300,

4012,
21101020,
10011020,
2010101012021020, //66

10112000,
10112000,
1000100020000011,
10012000,

1120,
10012000,
1000100020000011,
10201222, //74

1000100020001011,
10201120,
1000100020001011,
10211020,

1000111020002011,
10210120,
500000000000000000000000000000000008000000400000,
000000000400000000400020, //82

#BPMCHANGE 165
210210201022,
#BPMCHANGE 166
#SCROLL 0.99
210210201022,
210210201022,
210210202022, //86

#BPMCHANGE 169
#SCROLL 0.97
#GOGOSTART
210210101211,
210210201211,
#BPMCHANGE 169.62
210120101211,
#GOGOEND
#BPMCHANGE 167
#SCROLL 0.98
200020201110201000200111,
#BPMCHANGE 167.26
121212102020, //91

#BPMCHANGE 170
#SCROLL 1
#GOGOSTART
100020202000100011102000,
100020202000100022201000,
100020102000100011102000,
100020102000100022201000,

101120202212,
100010102000200022201000,
112121112121,
100010201000100022201000, //99

100010102020100011102000,
100020201010200022201000,
100010102020100011102000,
100020201010200022201000,

102112102112,
100010201000200011102020,
102112221120,
112121211121, //107
#GOGOEND

200221221222,
100211211212,
100222100222,
100000222000300000300000,

06,
0,
0,
000000000000000000000000000000000000000008000000,

0,
0,
0, //118
#END


COURSE:Hard
LEVEL:7
BALLOON:
SCOREINIT:380,1460
SCOREDIFF:90

#START
3000000010002220,
1000222010002220,
1000222010002220,
1000222010002220,

3000222010102220,
1010222010102220,
1010222010102220,
#BPMCHANGE 172.45
#SCROLL 0.99
1010222030000000, //8

#BPMCHANGE 172
#SCROLL 1
1000201110002000,
1000201110002000,
1000201110002000,
10221120,

1000201110102000,
1000201110102000,
1000201110102000,
500000000000000000000000000000000000000008000000, //16

1010201110002000,
1010201110002000,
1010201110002000,
10221210,

1010201110102000,
1010201110102000,
1010201110102000,
10221210, //24

#BPMCHANGE 172.75
#SCROLL 0.99
30002002,
#MEASURE 3/4
#BPMCHANGE 172
#SCROLL 1
000022022000,
#MEASURE 4/4
30002002,
#MEASURE 3/4
000022202000,

#MEASURE 4/4
30002002,
#MEASURE 3/4
000022022000,
#MEASURE 4/4
30002002,
#MEASURE 3/4
#BPMCHANGE 171
000022202000, //32

#MEASURE 4/4
#BPMCHANGE 172
10221121,
01122020,
10112212,
02211020,

10211121,
0011101020002000,
10201220,
1000201110002000,
3000222020200020,
02203000, //42

#GOGOSTART
1010200011102020,
10212020,
1010200011102020,
10212020,

1110200020002020,
1110200020111020,
1110222011102220,
30201212, //50

1010200011102020,
10212022,
1010200011102020,
10212022,

11202222,
1010200020111020,
1110222011102220,
1110222011104000, //58
#GOGOEND

100222200222,
200222200222,
200222200222,
200222200400,

3012,
11201020,
10012020,
1010202011011000, //66

1120,
10012000,
1000100020000011,
10012000,

1120,
10012000,
10102001,
10201222, //74

10102022,
10112020,
1000100020000011,
10012020,

10102022,
10210120,
500000000000000000000000000008000000000000300000,
00040040, //82

#BPMCHANGE 165
100222202022,
#BPMCHANGE 166
#SCROLL 0.99
220220202022,
220220202022,
220220202020,

#BPMCHANGE 169
#SCROLL 0.97
#GOGOSTART
110110101011,
110110101011,
#BPMCHANGE 169.62
110110101011,
#GOGOEND
#BPMCHANGE 167
#SCROLL 0.98
1,
#BPMCHANGE 167.26
000122, //91

#BPMCHANGE 170
#SCROLL 1
#GOGOSTART
102220102020,
102220201020,
102220102020,
102220201020,

102220102020,
102220101020,
110110220220,
100122, //99

102220102020,
102220201020,
102220102020,
102220102020,

102220102020,
102220101111,
500000000000000000000000000000000000000008000000,
3434, //107
#GOGOEND

300222200222,
200222200222,
200222200222,
200222200300,

06,
0,
0,
000000000000000000000000000000000000000008000000,

0,
0,
0, //118
#END


COURSE:Normal
LEVEL:6
BALLOON:7,6
SCOREINIT:520,3000
SCOREDIFF:138

#START
32,
12,
12,
12,

3022,
1022,
1022,
#BPMCHANGE 172.45
#SCROLL 0.99
13, //8

#BPMCHANGE 172
#SCROLL 1
1022,
1022,
1022,
500000000000000000000000000000000008000000000000,

1120,
1120,
1122,
500000000000000000000000000000000008000000000000, //16

1122,
1120,
1122,
1120,

1122,
1022,
1122,
1022, //24

#BPMCHANGE 172.75
#SCROLL 0.99
30002002,
#MEASURE 3/4
#BPMCHANGE 172
#SCROLL 1
0,
#MEASURE 4/4
30002002,
#MEASURE 3/4
0,

#MEASURE 4/4
30002002,
#MEASURE 3/4
0,
#MEASURE 4/4
30002002,
#MEASURE 3/4
#BPMCHANGE 171
0, //32

#MEASURE 4/4
#BPMCHANGE 172
12,
1112,
12,
1112,

12,
1112,
10002220,
1122,
3,
0122, //42

#GOGOSTART
10101110,
1120,
10101110,
1120,

11101020,
11101020,
3434,
3012, //50

10101110,
10112000,
10101110,
10112000,

11101020,
11101020,
3434,
3344, //58
#GOGOEND

1,
1,
11,
1012,

1012,
1022,
10011020,
1120, //66

1120,
10012000,
1120,
10012000,

1120,
10012020,
1120,
10201110, //74

1122,
10112020,
1122,
10112020,

1122,
30030030,
500000000000000000000000000008000000000000700000,
0008, //82

#BPMCHANGE 165
1220,
#BPMCHANGE 166
#SCROLL 0.99
1220,
1220,
1220, //86

#BPMCHANGE 169
#SCROLL 0.97
#GOGOSTART
3340,
3340,
#BPMCHANGE 169.62
3340,
#GOGOEND
#BPMCHANGE 167
#SCROLL 0.98
3,
#BPMCHANGE 167.26
0, //91

#BPMCHANGE 170
#SCROLL 1
#GOGOSTART
101122,
102200,
101122,
102200,

1110,
2220,
3434,
3, //99

101122,
102200,
101122,
102200,

1110,
2220,
7008,
3434, //107
#GOGOEND

3,
3,
43,
4033,

06,
0,
0,
000000000000000000000000000000000000000008000000,

0,
0,
0, //118
#END


COURSE:Easy
LEVEL:4
BALLOON:4,4
SCOREINIT:400,4470
SCOREDIFF:103

#START
32,
12,
12,
12,

32,
12,
12,
#BPMCHANGE 172.45
#SCROLL 0.99
13, //8

#BPMCHANGE 172
#SCROLL 1
12,
1022,
12,
500000000000000000000000000008000000000000000000,

12,
1022,
12,
500000000000000000000000000008000000000000000000, //16

1022,
1022,
1022,
1,

1022,
1022,
12,
1022, //24

#BPMCHANGE 172.75
#SCROLL 0.99
3,
#MEASURE 3/4
#BPMCHANGE 172
#SCROLL 1
0,
#MEASURE 4/4
4,
#MEASURE 3/4
0,

#MEASURE 4/4
3,
#MEASURE 3/4
0,
#MEASURE 4/4
4,
#MEASURE 3/4
#BPMCHANGE 171
0, //32

#MEASURE 4/4
#BPMCHANGE 172
12,
1120,
12,
1122,

12,
1122,
1022,
1111,
3,
0, //42

#GOGOSTART
1110,
1120,
1110,
1120,

1110,
2220,
3344,
3, //50

1111,
1120,
1111,
1120,

1110,
2220,
3344,
3333, //58
#GOGOEND

1,
1,
11,
1011,

1,
0,
0,
0, //66

1,
12,
1,
12,

1,
1022,
11,
1, //74

11,
1022,
11,
1022,

11,
30030030,
500000000000000000000000000008000000000000700000,
0008, //82

#BPMCHANGE 165
3,
#BPMCHANGE 166
#SCROLL 0.99
3,
3,
3, //86

#BPMCHANGE 169
#SCROLL 0.97
#GOGOSTART
3330,
3330,
#BPMCHANGE 169.62
3330,
#GOGOEND
#BPMCHANGE 167
#SCROLL 0.98
0,
#BPMCHANGE 167.26
0, //91

#BPMCHANGE 170
#SCROLL 1
#GOGOSTART
100101,
102200,
100101,
102200,

500000000000000000000000000008000000000000000000,
500000000000000000000000000008000000000000000000,
3333,
3, //99

101101,
102200,
101101,
102200,

500000000000000000000000000008000000000000000000,
500000000000000000000000000008000000000000000000,
7008,
3333, //107
#GOGOEND

3,
3,
43,
4033,

06,
0,
0,
000000000000000000000000000000000000000008000000,

0,
0,
0, //118
#END