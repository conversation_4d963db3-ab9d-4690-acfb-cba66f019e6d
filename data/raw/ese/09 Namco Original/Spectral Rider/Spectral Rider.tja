//TJADB Project
TITLE:Spectral Rider
TITLEJA:Spectral Rider
SUBTITLE:--Rio Hamamoto
SUBTITLEJA:濱本理央(BNSI)
BPM:258
WAVE:Spectral Rider.ogg
OFFSET:-1.723
DEMOSTART:49.167

COURSE:Oni
LEVEL:10
BALLOON:18,23
SCOREINIT:460,1310
SCOREDIFF:113

#START
#MEASURE 12/4
#SCROLL 0.5
321121,
321212,
321121,
321211,

#MEASURE 3/4
#SCROLL 1
333,
337,
#MEASURE 6/4
000000000008, //7

#MEASURE 3/4
300011101000,
111020,
100011101000,
101010002220,

100011101000,
111020,
101011101000,
111022201110, //15

300011101000,
111020,
100011101000,
111020,

100011101000,
111020,
111110102000,
111022201110, //23

101022201000,
5,
000000000000000000000000000008000000,
101011101000,

5,
0,
000000000000000000000000000008000000,
101011101000, //31

101022201000,
500000000000000000000000000008000000,
500000000000000000000000000008000000,
101011101000,

5,
000000000000000000000000000008000000,
300040002220,
111010202020, //39

101201,
101142,
111201,
104122,

100010202220,
101140,
101221,
104122, //47

101201,
101110004000,
101201,
104011,

7,
0,
000008,
102011, //55

#GOGOSTART
302121,
111020101020,
101020101110,
100010101110,

302122,
111020101020,
101020101110,
100010101110, //63

302121,
111020101010,
102121,
111020101010,

102121,
111020101022,
102211101011,
101011101010, //71
#GOGOEND

3,
#SCROLL 0.5
1,
1,
1,

102,
102,
102,
102, //79

302210,
102210,
102220,
102210,

212212,
112212,
212210,
122122, //87

#SCROLL 1
3,
000003,
300012,
122122,

3,
000001,
#MEASURE 2/4
#BPMCHANGE 172
#SCROLL 1.5
21101011,
112122, //95

#MEASURE 3/4
#BPMCHANGE 258
#SCROLL 1
3,
400003,
300340,
112122,

212102,
011212,
#MEASURE 2/4
#BPMCHANGE 172
#SCROLL 1.5
200010001000100200100200,
10121210, //103

#MEASURE 3/4
#BPMCHANGE 258
#SCROLL 1
3,
#SCROLL 0.5
2,
1,
2,

101,
202,
101,
202, //111

121,
221,
121,
221,

102210,
202210,
102210,
203022, //119

#SCROLL 0.75
102212,
102120,
102212,
100020222220,

102212,
102120,
102212,
102022202222, //127

102122,
102122,
102122,
100020102222,

102122,
102122,
305,
000000000000000000000000000008000000, //135

#SCROLL 1
#GOGOSTART
111011102020,
111212,
112121,
202011101020,

111011102010,
111212,
112121,
202011101020, //143

111011102010,
111212,
112121,
112121,

6,
0,
0,
000000000000000000000000000008000000, //151

302121,
111020101020,
101020101110,
100010101110,

302121,
111020101020,
101020101110,
100010101110, //159

302121,
111020101010,
111010111010,
222020222020,

6,
000000000000000000000000000008000000,
101110221120,
102211201010, //167
#GOGOEND

221120002222,
111120001110,
102220202030,

0,
0,
0,
0,
0,
0, //176
#END


COURSE:Hard
LEVEL:7
BALLOON:12,18,20,10
SCOREINIT:520,2050
SCOREDIFF:123

#START
#MEASURE 12/4
#SCROLL 0.5
322122,
322122,
322122,
322122,

#MEASURE 3/4
#SCROLL 1
333,
337,
#MEASURE 6/4
000000000008, //7

#MEASURE 3/4
301110,
111,
101110,
112,

101110,
111,
111020,
111020,

301110,
111,
101110,
112,

101110,
111,
222020,
111010, //23

101110,
5,
000000000000000000000000000008000000,
101210,

5,
0,
000000000000000000000000000008000000,
102220,

101110,
500000000000000000000000000008000000,
500000000000000000000000000008000000,
101210,

5,
000000000000000000000000000008000000,
341,
111111, //39

101100,
112,
101200,
102011,

101200,
112,
101200,
102011,

101100,
112,
5,
000000000000000008000000000000000000,

9,
0,
0900,
8, //55

#GOGOSTART
302121,
112,
102121,
111,

302121,
112,
102121,
111,

302121,
101012,
102121,
111,

7,
0,
0,
000008, //71
#GOGOEND

#SCROLL 0.5
3,
1,
1,
1,

102,
102,
102,
102,

302,
111,
102,
111,

122,
101110,
122,
101111, //87

#SCROLL 1
301,
211,
211,
111111,

301,
211,
#MEASURE 2/4
#BPMCHANGE 172
#SCROLL 1.5
201011,
101011,

#MEASURE 3/4
#BPMCHANGE 258
#SCROLL 1
301,
211,
101110,
111111,

6,
000000000000000000000000000008000000,
#MEASURE 2/4
#BPMCHANGE 172
#SCROLL 1.5
7,
000008, //103

#MEASURE 3/4
#BPMCHANGE 258
#SCROLL 0.5
3,
2,
1,
2,

101,
202,
101,
202,

121,
202,
121,
202,

121,
202220,
101110,
101011, //119

#SCROLL 0.75
100212,
111,
100212,
111,

100212,
111,
100212,
101110,

122,
101110,
122,
101110,

102011,
111,
405,
000000000000000000000000000008000000, //135

#SCROLL 1
#GOGOSTART
111110,
101210,
101110,
111110,

111110,
101210,
101110,
111110,

111110,
101210,
101110,
202220,

6,
0,
0,
000000000000000000000000000008000000, //151

302011,
101112,
101112,
101110,

302011,
101112,
101112,
101110,

302011,
101112,
101112,
101110,

6,
0,
0,
000000000000000000000000000008000000, //167
#GOGOEND

101011,
215000,
000000000000000000000008000000300000,

0,
0,
0,
0,
0,
0, //176
#END


COURSE:Normal
LEVEL:6
BALLOON:8,11,14,7
SCOREINIT:730,3860
SCOREDIFF:195

#START
#MEASURE 12/4
#SCROLL 0.5
302202,
302202,
302202,
302202,

#MEASURE 3/4
#SCROLL 1
6,
000000000008000000000000700000000000,
#MEASURE 6/4
000000000008, //7

#MEASURE 3/4
310,
111,
1,
111,

110,
111,
120,
111,

310,
111,
1,
111,

110,
111,
120,
111, //23

101,
5,
000000000000000000000000000008000000,
1,

5,
0,
000000000000000000000000000008000000,
1,

101,
500000000000000000000000000008000000,
500000000000000000000000000008000000,
1,

5,
000000000000000000000000000008000000,
3,
1, //39

5,
000000000000000008000000100000000000,
1,
110,

1,
110,
1,
1,

1,
101,
5,
000000000000000008000000000000000000,

9,
0,
0900,
8, //55

#GOGOSTART
301,
1,
1,
111,

301,
1,
1,
111,

301,
1,
1,
111,

7,
0,
0,
000008, //71
#GOGOEND

#SCROLL 0.5
3,
1,
1,
1,

102,
102,
102,
102,

302,
111,
102,
111,

102,
111,
102,
330, //87

#SCROLL 1
3,
2,
111,
1,

3,
2,
#MEASURE 2/4
#BPMCHANGE 172
#SCROLL 1.5
111,
1,

#MEASURE 3/4
#BPMCHANGE 258
#SCROLL 1
3,
2,
111,
101110,

6,
000000000000000000000000000008000000,
#MEASURE 2/4
#BPMCHANGE 172
#SCROLL 1.5
7,
000008, //103

#MEASURE 3/4
#BPMCHANGE 258
#SCROLL 0.5
3,
2,
2,
2,

1,
2,
2,
2,

102,
2,
102,
2,

102,
201,
102,
2, //119

#SCROLL 0.75
101,
102,
101,
102,

101,
102,
101,
102,

102,
111,
102,
111,

102,
111,
4,
0, //135

#SCROLL 1
#GOGOSTART
110,
111,
1,
1,

110,
111,
1,
1,

110,
111,
101,
111,

6,
0,
0,
000000000000000000000000000008000000, //151

301,
1,
110,
111,

301,
1,
110,
111,

301,
1,
110,
111,

6,
0,
0,
000000000000000000000000000008000000, //167
#GOGOEND

101011,
150,
000000000000000000000008000000300000,

0,
0,
0,
0,
0,
0, //176
#END


COURSE:Easy
LEVEL:5
BALLOON:6,9,11,5
SCOREINIT:700,6630
SCOREDIFF:210

#START
#MEASURE 12/4
#SCROLL 0.5
32,
32,
32,
32,

#MEASURE 3/4
#SCROLL 1
6,
000008000000000000000000700000000000,
#MEASURE 6/4
000000080, //7

#MEASURE 3/4
3,
110,
1,
110,

1,
110,
1,
1,

3,
110,
1,
110,

1,
110,
1,
2, //23

101,
5,
000000000000000008000000000000000000,
1,

5,
0,
000000000000000008000000000000000000,
1,

101,
5,
000000000000000008000000000000000000,
1,

5,
000000000000000008000000000000000000,
3,
1, //39

5,
000008000000000000000000100000000000,
1,
1,

1,
1,
1,
1,

1,
101,
5,
000000000000000008000000000000000000,

9,
0,
0900,
8, //55

#GOGOSTART
301,
1,
1,
1,

301,
1,
1,
1,

301,
1,
1,
1,

7,
0,
0,
000000000000000000080000000000000000, //71
#GOGOEND

#SCROLL 0.5
3,
0,
1,
0,

2,
0,
2,
0,

3,
1,
2,
2,

1,
1,
2,
3, //87

#SCROLL 1
3,
0,
2,
2,

3,
0,
#MEASURE 2/4
#BPMCHANGE 172
#SCROLL 1.5
2,
2,

#MEASURE 3/4
#BPMCHANGE 258
#SCROLL 1
3,
2,
1,
2,

6,
000000000000000008000000000000000000,
#MEASURE 2/4
#BPMCHANGE 172
#SCROLL 1.5
7,
08, //103

#MEASURE 3/4
#BPMCHANGE 258
#SCROLL 0.5
3,
0,
2,
0,

1,
0,
2,
0,

1,
0,
2,
0,

1,
0,
2,
0, //1199

#SCROLL 0.75
1,
2,
1,
2,

1,
2,
1,
2,

101,
1,
101,
1,

1,
2,
4,
0, //135

#SCROLL 1
#GOGOSTART
110,
1,
1,
1,

110,
1,
1,
1,

110,
1,
1,
111,

6,
0,
0,
000000000000000008000000000000000000, //151

301,
1,
1,
1,

301,
1,
1,
1,

301,
1,
1,
111,

6,
0,
0,
000000000000000008000000000000000000, //167
#GOGOEND

111,
050,
000000000008000000000000000000300000,

0,
0,
0,
0,
0,
0, //176
#END