//TJADB Project
TITLE:<PERSON><PERSON><PERSON><PERSON> to Linear to Boku
TITLEJA:星屑とリニアと僕
SUBTITLE:--Rio Hamamoto feat. Yuu (pLumsonic!)
SUBTITLEJA:feat.結羽(プラムソニック)
BPM:176
WAVE:<PERSON><PERSON><PERSON><PERSON> to Linear to Boku.ogg
OFFSET:-1.609
DEMOSTART:43.536

//Shinuchi: 4320/3140/2310/1390

COURSE:Oni
LEVEL:8
BALLOON:10,4
SCOREINIT:460,1410
SCOREDIFF:113

#START
0220,
20200220,
0220,
#BPMCHANGE 177.8
#SCROLL 0.98
0222,

#BPMCHANGE 176
#SCROLL 1
11210121,
0010201022111022, //6

1120201000102010,
0010201120102022,
1120201020100012,
2010201120201022,

1120201000102010,
0040002010102211,
1010201020201021,
2020101022112022, //14

1120201000102010,
0010201120102022,
1120201020100012,
2010201120201022,

1120201000101020,
0012202022101022,
2020111020112020,
1021201022112211, //22

02120121,
02021220,
211221,
1022111022111000,

1020202210100010,
2020102010201220,
1020202210100022,
202010101020202010101111,
000000700000000000000000000000000000080000000000, //31

#GOGOSTART
1021012020101020,
1021021010201000,
1021012020101020,
500000000008000000200000500000000008000000200000,

1010201010212020,
1010201010212022,
1020102210201022,
1120102011202212, //39
#GOGOEND

#BPMCHANGE 174.55
2220,
#BPMCHANGE 176
20200220,
0220,
#BPMCHANGE 178.97
#SCROLL 3
500000000008000000000000
#BPMCHANGE 176
#SCROLL 1
100000200200100100200200,
100000300000000000000000202020200000000000000100, //44

1220201000102012,
2010201120102022,
1120201020100012,
2010201120201022,

1120201020100012,
2010201000102211,
1010201020201021,
2020101022112022, //52

10000002,
22021222,
1,
12021221,

1,
3020202020201011,
2020111020112020,
1021201022112211, //60

01210121,
02021220,
211221,
1022111022111000,

1020202210100010,
2020102010201220,
1020202210100022,
202010101020202010101111,
0, //69

2002002020002000,
2002002020002000,
2002002020002000,
2002002020200000,

#GOGOSTART
1010201010212020,
1010201010212022,
1020102210201012,
#GOGOEND
20070080, //77

#GOGOSTART
1021012020101020,
1021021010201000,
1021012020101020,
500000000008000000200000500000000008000000200000,

1010201010212020,
1010201010112220,
1020102210201022,
100100200000100000200000100010001000100000000000, //85
#GOGOEND

#BPMCHANGE 174.55
4220,
#BPMCHANGE 176
20200220,
0220,
#BPMCHANGE 177.73
#SCROLL 0.99
000000000000200000000000
#BPMCHANGE 176
#SCROLL 3
500000000008000000000000,

#SCROLL 1
#GOGOSTART
1022102010212022,
1022102010112220,
1022102010221022,
100010001000200200200200100010001000202020202020,
#GOGOEND

2,
0,
0, //96
#END


COURSE:Hard
LEVEL:5
BALLOON:3,3,14,3,3,12
SCOREINIT:560,2470
SCOREDIFF:140

#START
0220,
20200220,
0220,
#BPMCHANGE 177.8
#SCROLL 0.98
0222,

#BPMCHANGE 176
#SCROLL 1
11010101,
01013000, //6

12010121,
01212020,
12012101,
01202120,

12010121,
04021120,
10121012,
10127080, //14

12010121,
01212020,
12010121,
01212210,

12010112,
01202120,
10121012,
10127080, //22

01010202,
01012220,
011111,
2220,

12210101,
22102020,
12210107,
00000008,
0, //31

#GOGOSTART
1001002020201000,
1001001020201000,
1001002020201000,
500000000008000000000000500000000008000000000000,

11211022,
11211000,
12211221,
10121043, //39
#GOGOEND

#BPMCHANGE 174.55
4220,
#BPMCHANGE 176
20200220,
0220,
#BPMCHANGE 178.97
#SCROLL 3
500000000008000000000000
#BPMCHANGE 176
#SCROLL 1
000000000000000000000000,
33000000, //44

12010101,
21202120,
12012101,
21012120,

12012101,
21210122,
10121012,
10127080, //52

1,
2022,
1,
2022,

1,
30000011,
10121012,
10127080, //60

01010202,
01012220,
011111,
2220,

12210101,
22101010,
12210107,
00000008,
0, //69

2,
22,
2,
22,

#GOGOSTART
11211022,
11211000,
12211221,
#GOGOEND
2, //77

#GOGOSTART
1001002020201000,
1001001020201000,
1001002020201000,
500000000008000000000000500000000008000000000000,

11211022,
11211000,
12211221,
100000100200101010100000, //85
#GOGOEND

#BPMCHANGE 174.55
4220,
#BPMCHANGE 176
20200220,
0220,
#BPMCHANGE 177.73
#SCROLL 0.99
000000000000200000000000
#BPMCHANGE 176
#SCROLL 3
500000000008000000000000,

#SCROLL 1
#GOGOSTART
11211022,
11211022,
12121221,
12121143,
#GOGOEND

4,
0,
0, //96
#END


COURSE:Normal
LEVEL:5
BALLOON:8,3,8
SCOREINIT:610,3430
SCOREDIFF:168

#START
0,
0,
0,
#BPMCHANGE 177.8
#SCROLL 0.98
0,

#BPMCHANGE 176
#SCROLL 1
11010101,
01013000, //6

11101010,
11102020,
11101010,
11102020,

11101010,
22202020,
1111,
1130, //14

11101010,
11102020,
11101010,
11102020,

11101010,
22202020,
1111,
1130, //22

01010202,
01011010,
05000000,
000000000000000000000000000000000008000000000000,

10010010,
01002020,
10010017,
00000008,
0, //31

#GOGOSTART
10102220,
1122,
10102220,
500000000008000000000000500000000008000000000000,

10102220,
1120,
10102220,
30303044, //39
#GOGOEND

#BPMCHANGE 174.55
4220,
#BPMCHANGE 176
20200220,
0220,
#BPMCHANGE 178.97
#SCROLL 2
500000000008000000000000
#BPMCHANGE 176
#SCROLL 1
000000000000000000000000,
33000000, //44

11101010,
11102020,
11101010,
11102020,

11101010,
11102020,
1111,
1130, //52

1,
2,
1,
2022,

1,
3,
1111,
10107080, //60

01010202,
01011010,
05000000,
000000000000000000000000000000000008000000000000,

10010010,
01002020,
10010017,
00000008,
0, //69

2,
22,
2,
22,

#GOGOSTART
10102220,
1120,
10102220,
#GOGOEND
3, //77

#GOGOSTART
10102220,
1122,
10102220,
500000000008000000000000500000000008000000000000,

10102220,
1120,
10102220,
300000000000300000000000600000000000000000000008, //85
#GOGOEND

#BPMCHANGE 174.55
0220,
#BPMCHANGE 176
20200220,
0220,
#BPMCHANGE 177.73
#SCROLL 0.99
000000000000200000000000
#BPMCHANGE 176
#SCROLL 2
500000000008000000000000,

#SCROLL 1
#GOGOSTART
10102220,
1120,
12101210,
12121044,
#GOGOEND

4,
0,
0, //96
#END


COURSE:Easy
LEVEL:4
BALLOON:6,8
SCOREINIT:500,4830
SCOREDIFF:145

#START
0,
0,
0,
#BPMCHANGE 177.8
#SCROLL 0.98
0,

#BPMCHANGE 176
#SCROLL 1
1111,
1130, //6

11,
1122,
11,
1122,

11,
1122,
1111,
1130, //14

11,
1122,
11,
1122,

11,
1122,
1111,
1130, //22

12,
1022,
05000000,
000000000000000000000000000008000000000000000000,

10010010,
01002000,
10010017,
00000008,
0, //31

#GOGOSTART
1110,
1122,
1110,
500000000008000000000000500000000008000000000000,

1122,
1120,
1122,
3333, //39
#GOGOEND

#BPMCHANGE 174.55
4,
#BPMCHANGE 176
0,
0,
#BPMCHANGE 178.97
#SCROLL 2
500000000008000000000000
#BPMCHANGE 176
#SCROLL 1
000000000000000000000000,
3, //44

11,
1122,
11,
1122,

11,
1122,
1111,
1130, //52

1,
2,
1,
22,

1,
3,
1111,
1130, //60

12,
1022,
05000000,
000000000000000000000000000008000000000000000000,

10010010,
01002000,
10010017,
00000008,
0, //69

2,
22,
2,
22,

#GOGOSTART
1122,
1120,
1122,
#GOGOEND
3, //77

#GOGOSTART
1110,
1122,
1110,
500000000008000000000000500000000008000000000000,

1122,
1120,
1122,
300000000000300000000000600000000000000000000008, //85
#GOGOEND

#BPMCHANGE 174.55
0,
#BPMCHANGE 176
0,
0,
#BPMCHANGE 177.73
#SCROLL 0.99
000000000000000000000000
#BPMCHANGE 176
#SCROLL 2
500000000008000000000000,

#SCROLL 1
#GOGOSTART
1122,
1120,
1122,
3333,
#GOGOEND

4,
0,
0, //96
#END