//TJADB Project
TITLE:Kill My Fortune
TITLEJA:Kill My Fortune
SUBTITLE:--<PERSON> and the Magic Orchestra
SUBTITLEJA:アリスシャッハと魔法の楽団
BPM:180
WAVE:Kill my Fortune.ogg
OFFSET:-0.340
DEMOSTART:5.673

COURSE:Edit
LEVEL:10
BALLOON:
SCOREINIT:910
SCOREDIFF:0

#START
1000201000112022,
1000201102112012,
1000201212102012,
200000200000200000200000100200100200101010101010, //4

#GOGOSTART
1110201000111022,
1001200212102210,
1110201200102011,
1210201122112222, 

1110201000111022,
1210202210102222,
2011212021212011,
2120212120222222, //12

1110201000111022,
1001200212102210,
1110201200102011,
1210202211221122,

1000201100112011,
1010201100112011,
100000000000200000100100100200100100200200202020,
100000000000000000100100202020100100200200100000, //20
#GOGOEND

3000201110002011,
1010202012102011,
1202201201102222,
1001001020111111,

1010201110002011,
1010202212102211,
100000201010100200202020,
1020201010202222, //28

1010201110002011,
1010201010112222,
100200200000100000000000202000000000200020002000,
101000100000200000200000100100100100200000000000,

3000201110002011,
1010200210102012,
1210201202112022,
2212201212122222, //36

1000201100112011,
1010201212102011,
1000201200112011,
1010201112102211,

1000201100112011,
1010201212102011,
1000201200112010,
1210201202102211, //44

1100201102102101,
1010210212102210,
1100201102102101,
1010210212102210, 

1100201102102101,
1010210222122122,
1022201120122011,
200000100100200100100100200100100100202020100100,
100000000000100000202020200100100000202020200000,
100000202020100100000000200000200100200100200000, //54

3000
#SCROLL 0.75
001110001210,
1012121220101020,
1000201220001111,
1010201022221111,

1022201020001210,
0012201120202222,
1020201120001022,
1022221112121212, //62

2000202030002220,
1001001012221222,
1000101020111010,
1002121120102010,

4010101120000011,
#SCROLL 0.86
1001001020102022,
#SCROLL 0.92
1000201000112011,
#SCROLL 1
100000200200200200200200101010101010101010100000, //70

40020020,
000000200000000000000000200200200200101010101010, 
#GOGOSTART
1110201200102011,
1210201210101111,

1110201000111022,
1210202210102222,
2011212021212011,
2120212122222222, //78

1110201000111022,
1001200212102210, 
1110201200102011,
1020202220102222,

1000201100112011,
1010201100112011,
1000201212102220,
100000200200100100100100
#GOGOEND
202020100100200200100000, //86

#GOGOSTART
4000201020102011,
2010201220102212,
2000201020102011,
2010201220101222,

2000201020102011,
200000100000200000100200200000100000202020100100,
2000201020102011,
200000100000200000200200101010200200101010200200, //94

1020112012201020,
1122112112211222,
100200200200100100200100101010202020101010202020,
1021112211211212,

100000200000100100200000100200200100100000202020,
1020112212211120,
101010200200100000200200101010200200101010200200,
122122300000, //102

0,
0,
#GOGOEND
0,
0,
0, //107
#END


COURSE:Oni
LEVEL:7
BALLOON:
SCOREINIT:1590
SCOREDIFF:0

#START
0,
0,
0,
0,

#GOGOSTART
1000201000111000,
12021020,
1000201000111000,
0020002000201110,

1000201000111000,
1020002010002220,
21121121,
1020101020202220, //12

1000201000111000,
12021020,
1000201000111000,
0020100022222000,

1000201000111000,
11210021,
1000201000102022,
1000001122222000, //20
#GOGOEND

30011011,
01101101,
10110110,
1001001010111000,

30011011,
0010100010102220,
100111100100,
11211020, //28

11011011,
0010100010102220,
100100200222,
20221020,

30011011,
01101101,
1000101000101110,
1010101010111110, //36

1000201000112000,
1000201110102000,
1000201000112000,
1000201110102000,

1000201000112000,
1000201110102000,
10210120,
1110201110201110, //44

1000201000112000,
1000201110102000,
1000201000112000,
1000201110102000,

1000201000112000,
1000201110102220,
2010201020102011,
2011201120112000,

10011011,
01102020, //54

30
#SCROLL 0.75
011011,
11012101,
10202011,
11012010,

10012011,
0010100020202220,
10102001,
100000000000000000500000000000000000000000000008, //62

03,
1001001022202220,
10112010,
1000111020102010,

1000101120000000,
#SCROLL 0.86
1001001020102000,
#SCROLL 0.92
10210120,
#SCROLL 1
500000000000000000000000000000000000000008000000, //70

40020020,
02002000,
#GOGOSTART
1000201000111000,
02002002,

1000201000111000,
1020002010002220,
21121121,
1020100011111110, //78

1000201000111000,
12021020,
1000201000111000,
0020202220002220,

1000201000111000,
11210021,
1000201000102022,
10000011
#GOGOEND
22222000, //86

#GOGOSTART
40212121,
21212120,
20212121,
2010201020102220,

20212121,
21212120,
20212121,
2010200011101110, //94

1020102011201020,
1000112010201020,
1020102011201020,
1000112010202220,

1020102011201020,
1000112010201020,
1020102011201020,
111111300000, //102

0,
0,
#GOGOEND
0,
0,
0, //107
#END


COURSE:Hard
LEVEL:5
BALLOON:9
SCOREINIT:2240
SCOREDIFF:0

#START
0,
0,
0,
0,

#GOGOSTART
10210010,
12001020,
10210010,
02020210,

10210010,
12001000,
20220220,
22222020, //12

10210010,
12001020,
10210010,
000000200000100000000000500000000000000008000000,

10210010,
10210010,
10210010,
10012020, //20
#GOGOEND

30011011,
01101101,
10110110,
1001001010000000,

30011011,
01101100,
1011,
11211000, //28

11011011,
01101100,
12,
20221000,

30011011,
01101101,
10110110,
500000000000000000000000000000000000000008000000, //36

10210110,
10210120,
10210110,
10210120,

10210110,
10211120,
10210110,
7008, //44

1000201000111000,
10211120,
1000201000111000,
10211120,

1000201000111000,
10212120,
21212121,
21212120,
10011011,
01102020, //54

30
#SCROLL 0.75
011010,
11012000,
1221,
11012000,

10012011,
0120,
1120,
100000000000000000500000000000000000000000000008, //62

03,
1001001020000000,
1121,
1120,

1120,
#SCROLL 0.86
1001001020000000,
#SCROLL 0.92
10210120,
#SCROLL 1
500000000000000000000000000000000000000080000000, //70

40020020,
02002000,
#GOGOSTART
10210010,
02002002,

10210010,
12021000,
20220220,
22222020, //78

10210010,
12001000,
10210010,
02202020,

10210010,
10210010,
10210010,
100000000000000000500000
#GOGOEND
000000000008000000000000, //86

#GOGOSTART
40212120,
20212120,
20212121,
21212120,

20212120,
20212120,
20212121,
2010200011101000, //94

1020102011101000,
10121210,
1020102011101000,
10121210,

1020102011101000,
10121210,
1020102011101000,
111100300000, //102

0,
0,
#GOGOEND
0,
0,
0, //107
#END


COURSE:Normal
LEVEL:5
BALLOON:6,6
SCOREINIT:3310
SCOREDIFF:0

#START
0,
0,
0,
0,

#GOGOSTART
10010010,
01001000,
10010010,
0,

10010010,
01001000,
20020020,
02002000, //12

10010010,
01001000,
10010010,
0,

10110010,
10110020,
10110010,
100000000000000000500000000000000008000000000000, //20
#GOGOEND

30011011,
01101101,
10110110,
11,

30011011,
01101100,
11,
11101000, //28

11011011,
01101100,
1,
2,

30011011,
01101101,
10110110,
500000000000000000000000000000000000000008000000, //36

10010010,
1002,
10010010,
1002,

10010020,
1002,
10010010,
7008, //44

10010010,
10220110,
10010010,
10220110,

10010010,
1022,
1111,
11111110,
10011011,
01102020, //54

3
#SCROLL 0.75
010,
12,
1110,
12,

1,
0110,
12,
100000000000000000500000000000000000000000000008, //62

03,
12,
1110,
12,

12,
#SCROLL 0.86
12,
#SCROLL 0.92
1,
#SCROLL 1
500000000000000000000000000000000000000008000000, //70

4,
0,
#GOGOSTART
10010010,
0,

10010010,
01001000,
20020020,
02002020, //78

10010010,
01001000,
10010010,
0,

10110010,
10110020,
10110010,
100000000000000000500000
#GOGOEND
000000000008000000000000, //86

#GOGOSTART
40101110,
10101110,
10101110,
10111110,

10101110,
10101110,
10101110,
7008, //94

10121210,
10121210,
10121210,
10101210,

10121210,
10121210,
12101210,
13, //102

0,
0,
#GOGOEND
0,
0,
0, //107
#END


COURSE:Easy
LEVEL:4
BALLOON:4,12,4
SCOREINIT:5710
SCOREDIFF:0

#START
0,
0,
0,
0,

#GOGOSTART
10010010,
01001000,
10010010,
0,

10010010,
01001000,
5,
000000000000000000000008000000000000000000000000,

10010010,
01001000,
10010010,
0,

10010000,
10010000,
10010000,
1, //20
#GOGOEND

30010010,
01001000,
10010010,
11,

30010010,
01001000,
2,
2, //28

10010010,
01001000,
1,
0,

30010010,
01001000,
10010010,
500000000000000000000000000008000000000000000000, //36

10010000,
1,
10010000,
1,

10010000,
1,
10010000,
70000800, //44

10010010,
1,
10010010,
1,

10010010,
1,
11,
1110,
7,
08, //54

3
#SCROLL 0.75
000,
1,
11,
1,

1,
01,
1,
100000000000000000500000000000000000000000000008, //62

03,
1,
11,
1,

1,
#SCROLL 0.86
1,
#SCROLL 0.92
1,
#SCROLL 1
500000000000000000000000000000000000000008000000, //70

0,
0,
#GOGOSTART
10010010,
0,

10010010,
01001000,
5,
000000000000000000000008000000000000000000000000, //78

10010010,
01001000,
10010010,
0,

10010000,
10010000,
10010000,
1
#GOGOEND
0, //86

#GOGOSTART
42,
1110,
22,
1111,

11,
2220,
1111,
700000000000000000000000000000080000000000000000, //94

1111,
1110,
2222,
2220,

1111,
1110,
1122,
13, //102

0,
0,
#GOGOEND
0,
0,
0, //107
#END