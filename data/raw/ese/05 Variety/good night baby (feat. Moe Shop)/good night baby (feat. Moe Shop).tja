//TJADB Project
TITLE:good night baby (feat. <PERSON>)
TITLEJA:good night baby (feat. <PERSON>)
SUBTITLE:--<PERSON><PERSON><PERSON><PERSON> (CV:<PERSON><PERSON><PERSON>) / Den'onbu
SUBTITLEJA:犬吠埼紫杏(CV:長谷川玲奈) 「電音部」より
BPM:125
WAVE:good night baby (feat. <PERSON>).ogg
OFFSET:-2.096
DEMOSTART:15.288

//Shinuchi: 8450/5270/4500/2360

COURSE:Oni
LEVEL:7
BALLOON:
SCOREINIT:710,2450
SCOREDIFF:200

#START
0,
0,
0,

1010210010012010,
0010210010012010,
0010210010012010,
0010210012112022, //7

#GOGOSTART
1011102210111022,
1011101110021010,
0010002010210020,
1022104010401022,

1011102210111022,
1020102010021030,
0000203000112060,
000000000000000000000008000000000000300000000000, //15
#GOGOEND

1000222210210010,
2012001020121020,
1000222210201020,
1022102010002000,

1000222210210120,
0020002000221010,
0022101000221050,
000000000000000008000000200200200200200000000000, //23

1111201010012010,
1110221102211020,
1111201101012010,
1111102102021020,

1111201101012010,
2211102102021020,
1110201102011110,
22221010, //31

10022022,
00021010,
10022022,
00021010,

10022022,
00000002,
2002002000202050,
000000000008000000600000000000000008000000200200, //39

#GOGOSTART
1011102210111022,
1011101110021010,
0010002010210020,
1022104010401022,

1011102210111022,
1020102010021030,
0000203000112060,
000000000000000000000008000000000000400000200200, //47

1012102210121022,
1011101110021010,
0010002010210020,
1022104010401022,

1012102210121022,
1020102010021030,
0000203000112050,
000000000000000008000000200200200200100000000000,
#GOGOEND
4,
0, //57
#END


COURSE:Hard
LEVEL:5
BALLOON:27
SCOREINIT:1020,4940
SCOREDIFF:345

#START
0,
0,
0,

0202,
0202,
0202,
0204, //7

#GOGOSTART
1011100010111000,
1011100010000020,
01020102,
01040400,

1011100010111000,
12121023,
00230026,
000000000000000000000008000000000000300000000000, //15
#GOGOEND

1000200010010010,
00011020,
10201001,
00011020,

1000200010010010,
02020211,
02110215,
000000000000000000000008000000000000200000000000, //23

10201021,
0212,
10201021,
0212,

10201021,
0212,
10201021,
00021010, //31

1,
00021010,
1,
00021010,

1,
0,
7,
00000800, //39

#GOGOSTART
1011100010111000,
1011100010000020,
01020102,
01040400,

1011100010111000,
12121023,
00230026,
000000000000000000000008000000000000400000000000, //47

1011100020111000,
1011100010000020,
01020102,
01040400,

1011100020111000,
12121023,
00230026,
000000000000000000000008000000000000100000000000,
#GOGOEND
4,
0, //57
#END


COURSE:Normal
LEVEL:3
BALLOON:18
SCOREINIT:800,5780
SCOREDIFF:285

#START
0,
0,
0,

0202,
0202,
0202,
0204, //7

#GOGOSTART
11101110,
11101002,
01020102,
01040400,

11101110,
11111013,
00130016,
000000000000000000000008000000000000000000000000, //15
#GOGOEND

10201001,
0012,
10201001,
0012,

10201002,
02020011,
00110015,
000000000000000000000008000000000000000000000000, //23

20202022,
0202,
20202022,
0202,

20202022,
0202,
20202022,
00022020, //31

1,
00022020,
1,
00022020,

1,
0,
7,
00000800, //39

#GOGOSTART
11101110,
11101002,
01020102,
01040400,

11101110,
11111013,
00130016,
000000000000000000000008000000000000000000000000, //47

11101110,
11101002,
01020102,
01040400,

11101110,
11111013,
00130016,
000000000000000000000008000000000000000000000000,
#GOGOEND
4,
0, //57
#END


COURSE:Easy
LEVEL:3
BALLOON:15
SCOREINIT:860,9760
SCOREDIFF:505

#START
0,
0,
0,

0202,
0202,
0202,
0204, //7

#GOGOSTART
1111,
10101001,
00010001,
00040400,

1111,
10101003,
00030006,
000000000000000000000008000000000000000000000000, //15
#GOGOEND

10001001,
0,
10001001,
0,

10001001,
00000001,
00010005,
000000000000000000000008000000000000000000000000, //23

20002002,
0,
20002002,
0,

20002002,
0,
20002002,
0, //31

1,
0022,
1,
0022,

1,
0,
7,
00000800, //39

#GOGOSTART
1111,
10101001,
00010001,
00040400,

1111,
10101003,
00030006,
000000000000000000000008000000000000000000000000, //47

1112,
10101001,
00010001,
00040400,

1112,
10101003,
00030006,
000000000000000000000008000000000000000000000000,
#GOGOEND
4,
0, //57
#END
