//TJADB Project
TITLE:<PERSON><PERSON><PERSON><PERSON>/Gyouan
TITLEJA:郢曲／暁闇
SUBTITLE:--<PERSON> AI-CUE feat. <PERSON><PERSON>☆<PERSON>ajiro/Touhou Project
SUBTITLEJA:東方Project×NAMCO SOUNDS LindaAI-CUE
BPM:140
WAVE:<PERSON><PERSON><PERSON><PERSON> Gyouan.ogg
OFFSET:-3.980
DEMOSTART:46.829

//Shinuchi: 4900/3960/1830/1000

COURSE:Oni
LEVEL:10
BALLOON:
SCOREINIT:370,1010
SCOREDIFF:93

#START
200200200200200200200200201010200000200200100000,

221221211211,
200010001000200020002000100000000200101010100000,
211211221221,
200010001000200020001000200000000200101010100000, //5

221221211211,
200010001000200020002000100000000200101010100000,
211211221221,
2221222110202211, //9

200200201010100000101010200000200200000000201010,
200200000200100000202020100000200200101010100000,
100100202020100100202020100100200200100000202020,
100000101010200000202020100000200200101010101000, //13

200200201010100000101010200000200200000000201010,
200200000200100000202020100000200200101010100000,
100100202020100100202020100100202020100000000000,
100200200200100200200200102020200200100200200200, //17

100100100000202020202020100000101010200000202020,
100100101010200000200200100100101010200000200200,
100100100000202020202020100000101010200000202020,
100100101010200000200200102020200200100200200200, //21

1022102211221022,
100200200200100000202020100100200200101010100000,
1022102211221022,
101010100100200200202020100200202020100100100000, //25

#GOGOSTART
1101201011012020,
1101201021112020,
1111202010221020,
1022112210012210, //29

1101201011012020,
1101201021112020,
1111202010221020,
100200202020100200202020101010100000200200100000, //33
#GOGOEND

#MEASURE 3/4
301120112200,
301120112200,
301120112200,
#MEASURE 4/4
100200200200100200200200100010001000200200100100, //37

200200200000400000200200100000002000000010000000,
202020202020200000200200100100100200000200100000,
2111221020121122,
1012122210121122, //41

202020201010100100100200100100200100100200200100,
202020200200101010100100200000202020200200100000,
200010001000200010001000200000101010202020200100,
500000000000000000000000000008000000200200100000, //45

#GOGOSTART
1101221011012020,
1101221021112020,
1111222210221020,
1022112210012210, //49

1101221011012020,
1101221021112020,
100100100100200200200200100000200200101010200000,
202020200200100100202020200200100100200200100000, //53
#GOGOEND

1021202211122222,
100100100000000200100100100000000200100000202020,
1120021110021122,
1110112212221022, //57

100100100000000200100100100000000200100000202020,
100100200000000200100100200000000200100002020100,
1110112212221022,
1222122210102211, //61

#MEASURE 5/4
202020100100200100201010200200200000
#SCROLL 1.5
400000000300000000000000,
#SCROLL 1
202020100100200100201010200200200000
#SCROLL 2
300000000400000000000000, //63

#SCROLL 1
202020100100200100201010200200200000
#SCROLL 3
400000000300000000000000,
#MEASURE 3/4
#SCROLL 1
202020100100200100201010200200200000,
#MEASURE 4/4
#SCROLL 3.5
600000000000000000000008000000000000000000000000, //66

#SCROLL 1
100000202020100200200200101010200100100000200200,
100000202020100100200200202020200100100000200100,
100000202020100200200200101010200100100000200200,
202020200200100000202020100020201000101010200200, //70

1022102211221022,
100200200200100000202020100100200200101010100000,
1022102211221022,
100000101010200100200200202020201010100100200200,
0,
0, //76
#END


COURSE:Hard
LEVEL:8
BALLOON:
SCOREINIT:530,1910
SCOREDIFF:133

#START
2220222020001110,

222200111100,
222222200000,
111100222200,
111111100000,

222200111100,
222222200000,
111100222200,
111111100000, //9

2220100020220020,
2220100020201110,
1110110110101000,
500000000000000000000000000000000008000000000000,

2220100020220020,
2220100020201110,
2220100022201000,
500000000000000000000000000000000008000000000000, //17

1110202011102020,
1110222011102220,
1110202011102020,
1110222022022220,

1020102011111000,
1020102022222000,
1020102011111000,
500000000000000000000000000000000008000000000000, //25

#GOGOSTART
1101101010001000,
2202202040004000,
1111102011111020,
1011101110002000,

1101101010001000,
2202202040004000,
1111102011111020,
2222202220003000, //33
#GOGOEND

#MEASURE 3/4
3,
3,
3,
#MEASURE 4/4
600000000000000000000000000000000008000000000000, //37

1000401110010010,
2000202220020020,
1000101110010010,
2000202220020020,

1011101110010010,
2022202220020020,
1011101110010010,
500000000000000000000000000000000008000000000000, //45

#GOGOSTART
1101101010001000,
2202202040004000,
1111102011111020,
1011101110002000,

1101101010001000,
2202202040004000,
1111102011111020,
2222202220003000, //53
#GOGOEND

1110111010002000,
1110001110002000,
1110001110022020,
1110222010022020,

1110111010002000,
1110001110002000,
1110001110022020,
1110222010022020, //61

#MEASURE 5/4
500000000000000000000008000000000000
#SCROLL 1.5
400000000300000000000000,
#SCROLL 1
500000000000000000000008000000000000
#SCROLL 2
300000000400000000000000,
#SCROLL 1
500000000000000000000008000000000000
#SCROLL 3
400000000300000000000000,
#MEASURE 3/4
#SCROLL 1
500000000000000000000008000000000000,
#MEASURE 4/4
#SCROLL 3.5
600000000000000000000008000000000000000000000000, //66

#SCROLL 1
1020102011102000,
1020102022202000,
1020102011102000,
500000000000000000000000000000000008000000000000,

1020102011201120,
1020102022102210,
1020102011201120,
500000000000000000000000000000000000000000000008,
0,
0, //76
#END


COURSE:Normal
LEVEL:5
BALLOON:
SCOREINIT:780,4540
SCOREDIFF:233

#START
2223,
1111,
101101100000,
2222,
202202200000,

1111,
101101100000,
2222,
202202200000, //9

1120,
1122,
10011000,
10011000,

1120,
1122,
11101110,
1122, //17

1210,
1122,
1210,
1144,

1212,
1110,
1212,
500000000000000000000000000000000008000000000000, //25

#GOGOSTART
1122,
1144,
33,
500000000000000000000000000000000008000000000000,

1122,
1144,
33,
500000000000000000000000000000000008000000000000, //33
#GOGOEND

#MEASURE 3/4
3,
3,
3,
#MEASURE 4/4
500000000000000000000000000000000008000000000000, //37

1400,
22,
10011010,
1110,

500000000000000000000000000000000008000000000000,
500000000000000000000000000000000008000000000000,
1111,
500000000000000000000000000000000008000000000000, //45

#GOGOSTART
1122,
1144,
33,
500000000000000000000000000000000008000000000000,

1122,
1144,
33,
500000000000000000000000000000000008000000000000, //53
#GOGOEND

1110,
1112,
1122,
1110,

1110,
1112,
1122,
500000000000000000000000000000000008000000000000, //61

#MEASURE 5/4
500000000000000000000008000000000000400000000400000000000000,
500000000000000000000008000000000000300000000300000000000000,
500000000000000000000008000000000000400000000400000000000000,
#MEASURE 3/4
500000000000000000000008000000000000,
#MEASURE 4/4
600000000000000000000000000000000008000000000000, //66

10111000,
20222000,
10111000,
20222020,

10201120,
2121,
10201120,
200000000000100000000000500000000000000000000008,
0,
0, //76
#END


COURSE:Easy
LEVEL:3
BALLOON:
SCOREINIT:540,5560
SCOREDIFF:180

#START
2023,
1111,
1110,
2222,
2220,

1111,
1110,
2222,
2220, //9

1110,
1110,
10011000,
10011000,

2220,
2220,
1120,
1122, //17

1110,
1110,
1110,
1144,

1110,
1110,
1111,
500000000000000000000000000008000000000000000000, //25

#GOGOSTART
1111,
14,
33,
500000000000000000000000000008000000000000000000,
1111,
14,
33,
500000000000000000000000000008000000000000000000, //33
#GOGOEND

#MEASURE 3/4
3,
3,
3,
#MEASURE 4/4
500000000000000000000000000008000000000000000000, //37

22,
2022,
22,
2220,

500000000000000000000000000000000008000000000000,
500000000000000000000000000008000000000000000000,
2220,
500000000000000000000000000008000000000000000000, //45

#GOGOSTART
1111,
14,
33,
500000000000000000000000000008000000000000000000,

1111,
14,
33,
500000000000000000000000000008000000000000000000, //53
#GOGOEND

1110,
1110,
1122,
1110,

1110,
1110,
1122,
500000000000000000000000000008000000000000000000, //61

#MEASURE 5/4
500000000000000000000008000000000000400000000000000000000000,
500000000000000000000008000000000000300000000000000000000000,
500000000000000000000008000000000000400000000000000000000000,
#MEASURE 3/4
500000000000000000000008000000000000,
#MEASURE 4/4
600000000000000000000000000000000008000000000000, //66

1110,
2220,
1110,
2220,

1111,
2220,
1111,
200000000000200000000000500000000000000000000008,
0,
0, //76
#END