//TJADB Project
TITLE:Ghost Rule
TITLEJA:ゴーストルール
SUBTITLE:--DECO*27 feat. <PERSON>sune Miku
SUBTITLEJA:DECO*27 feat.初音ミク
BPM:105
WAVE:Ghost Rule.ogg
OFFSET:-2.414
DEMOSTART:50.414

//Shinuchi: 5900/4040/2090/1700/1240

COURSE:Edit
LEVEL:9
BALLOON:
SCOREINIT:410,1250
SCOREDIFF:100

#START
2000200202202000,
2000200202202000,
2000200202202000,

#MEASURE 2/4
20002002,
#BARLINEOFF
#BPMCHANGE 210
#SCROLL 0.5
0220,
#SCROLL 1
2140, //6

#BARLINEON
#MEASURE 4/4
11012112,
01102102,
1110101020101020,
01102102,

11012112,
01102102,
1110101020101020,
1022201020202020, //14

10201121,
01210121,
10210121,
1010201110102000,

11201121,
01210121,
1000201010202011,
1010202211104000, //22

1020102210201022,
1020112010201022,
1020102210201022,
1020112010201002,

1020102210201022,
1020112010201022,
1020102211201022,
2022102220101010, //30

1010001020111020,
0010102022101020,
1111201020101020,
0020101022100020,

1010001020111020,
0010102022101020,
1111201020101020,
1122101020202020,
10210210,
200000100000000000200000500000000008000000000000, //40

#GOGOSTART
1000200210102011,
2010201000102022,
1010202210102022,
1010201000222020,

1010200210102011,
2010201000102022,
1010202210102022,
1011201020102020, //48

1010222210102222,
1010222210221020,
1011202010021020,
1011202010222020,

1010222210102222,
1010222210221020,
1011202010021020,
100222100200100000000000, //56
#GOGOEND

20202222,
20220020,
200200200200200020200200,
20220020,

20202222,
20220020,
100100100100100000100010,
100010100100000000400000, //64

#GOGOSTART
1000200210102011,
2010201000102022,
1010202210102022,
1010201000222020,

1010200210102011,
2010201000102022,
1010202210102022,
1011201012222222, //72

1020102210201022,
1011201000102022,
1011202210112022,
1011201000222020,

1020102210201022,
1011201000102022,
1011202210112022,
1011221020102020, //80

1010222210102222,
1010222210221020,
1011202010021020,
1011202010222020,

1010222210102222,
1010222210221020,
1011202010021020,
100222100200
#GOGOEND
100000
#SCROLL 1.25
400000, //88

#GOGOSTART
1111222211112222,
1111222211222222,
5,
000000000000000008000000100000200200100000200200,

1111222211112222,
1111222211222222,
5,
000000000000000008000000400000000000000000000000, //96
#GOGOEND

#BARLINEOFF
0,
0, //98
#END


COURSE:Oni
LEVEL:8
BALLOON:23,24
SCOREINIT:550,1960
SCOREDIFF:135

#START
2000200202202000,
2000200202202000,
2000200202202000,

#MEASURE 2/4
20002002,
#BARLINEOFF
#BPMCHANGE 210
#SCROLL 0.5
0220,
#SCROLL 1
44, //6

#BARLINEON
#MEASURE 4/4
11011011,
01102002,
11011011,
01102002,

11011011,
01102002,
11011011,
1022201020202020, //14

10201121,
01210121,
10210121,
11210120,

11201121,
01210121,
10210121,
11221140, //22

12121212,
12121212,
11211212,
1020102010201011,

12121212,
12121212,
1010201110200020,
2022202020101010, //30

5,
000008000000100000000000100000000000100000000000,
7,
00000804,

6,
000008000000300000000000300000000000300000000000,
7,
00000800,
20020020,
02002000, //40

#GOGOSTART
1000200010102011,
20210120,
1010202010102020,
1010201000222020,

1010200010102011,
2000201000102000,
11221221,
1022202010104000, //48

3434,
30403400,
1011202010002020,
1011202010104000,

3434,
30403400,
1011202010002020,
1022102010000000, //56
#GOGOEND

2,
2,
22,
20020000,

2,
2,
22222020,
20220030, //64

#GOGOSTART
1000200010102011,
20210120,
1010202010102020,
1010201000222020,

1010200010102011,
20210120,
11221221,
1022202010222220, //72

1000200010102011,
21210122,
11221122,
1010201000222020,

1010200010102011,
21210122,
11221221,
1022202010104000, //80

3434,
30403400,
1011202010002020,
1011202010104000,

3434,
30403400,
1011202010002020,
10221020
#GOGOEND
1000
#SCROLL 1.25
4000, //88

#GOGOSTART
3434,
30403400,
6,
000000000000000008000000000000000000400000000000,

3434,
30403400,
6,
000000000000000008000000300000000000000000000000,
#GOGOEND

#BARLINEOFF
0,
0, //98
#END


COURSE:Hard
LEVEL:7
BALLOON:18,19
SCOREINIT:530,2440
SCOREDIFF:138

#START
0202,
0202,
0202,

#MEASURE 2/4
02,
#BARLINEOFF
#BPMCHANGE 210
#SCROLL 0.5
0,
#SCROLL 1
44, //6

#BARLINEON
#MEASURE 4/4
11011011,
01101000,
11011011,
01101000,

11011011,
01101000,
11011011,
11212222, //14

10201021,
01210001,
10210201,
01110000,

11201021,
01210001,
10210201,
11110040, //22

10101212,
12121012,
12121102,
12121020,

12101212,
12121012,
10121102,
22220010, //30

5,
000008000000100000000000100000000000100000000000,
7,
00000806,

0,
000008000000300000000000300000000000300000000000,
7,
00000800,
20020020,
02002000, //40

#GOGOSTART
10201122,
10210020,
11221122,
10210120,

10202211,
20210020,
22112211,
20210120, //48

3434,
30403400,
22221022,
10210120,

3434,
30403400,
11112022,
10110000, //56
#GOGOEND

2,
2,
22,
20020000,

2,
2,
22222020,
20220030, //64

#GOGOSTART
10201122,
10210020,
11221122,
11210122,

10202211,
20210020,
22112211,
21210000, //72

10201122,
11210020,
11221122,
11210122,

10202211,
22210020,
22112211,
21211222, //80

3434,
30403400,
22221022,
10210120,

3434,
30403400,
11112022,
1011
#GOGOEND
00
#SCROLL 1.25
40, //88

#GOGOSTART
3434,
30403400,
6,
000000000000000008000000000000000000400000000000,

3434,
30403400,
6,
000000000000000008000000000000000000000000000000,
#GOGOEND

#BARLINEOFF
0,
0, //98
#END


COURSE:Normal
LEVEL:5
BALLOON:10,10,10,18,19
SCOREINIT:760,5250
SCOREDIFF:213

#START
#BARLINEOFF
0,
0,
0,

#MEASURE 2/4
0,
#BPMCHANGE 210
0,
0, //6

#BARLINEON
#MEASURE 4/4
3,
11,
3,
12,

3,
11,
3,
12, //14

11,
1100,
11,
1100,

11,
1100,
11,
2200, //22

1111,
10110000,
1212,
10110000,

1111,
10110000,
1212,
11110050, //30

000000000000000000000000000000000000000000000008,
0111,
7,
00000806,

000000000000000000000000000000000000000000000008,
0333,
9,
000009000000000800000000,
0,
0, //40

#GOGOSTART
1111,
10110020,
1212,
10110000,

1111,
10110020,
1212,
10110040, //48

3333,
30303300,
6,
000000000000000008000000000000000000400000000000,
3333,
30303300,
6,
000000000000000008000000000000000000000000000000, //56
#GOGOEND

0,
0,
0,
0,

0,
0,
7,
000000000000000000000008000000000000000000000000, //64

#GOGOSTART
1111,
10110020,
1212,
10110000,

1111,
10110020,
1212,
500000000000000000000008000000000000000000000000, //72

10101110,
10110020,
1212,
10220000,

10101110,
10110020,
1212,
10220040, //80

3333,
30303300,
6,
000000000000000008000000000000000000400000000000,

3333,
30303300,
6,
000000000000000008000000
#GOGOEND
000000000000
#SCROLL 1.25
000000000000, //88

#GOGOSTART
9,
0,
000000000009000000000000000000000000000000000008,
0,

9,
0,
000000000009000000000000000000000000000000000008,
0,
#GOGOEND

#BARLINEOFF
0,
0, //98
#END


COURSE:Easy
LEVEL:4
BALLOON:8,8,7,16,17
SCOREINIT:660,7970
SCOREDIFF:210

#START
#BARLINEOFF
#SCROLL 0.8
0,
0,
0,

#MEASURE 2/4
0,
#BPMCHANGE 210
0,
0, //6

#BARLINEON
#MEASURE 4/4
3,
11,
3,
11,

3,
11,
3,
11, //14

1,
0,
11,
1100,

1,
0,
11,
1100, //22

11,
1100,
11,
1100,

11,
1100,
11,
2205, //30

000000000000000000000000000000000000000008000000,
0111,
7,
00000806,

000000000000000000000000000000000000000008000000,
0333,
9,
000009000000000800000000,
0,
0, //40

#GOGOSTART
1110,
1100,
11,
1100,

1110,
1100,
11,
2200, //48

3333,
3330,
6,
000000000000000008000000000000000000000000000000,

3333,
3330,
6,
000000000000000008000000000000000000000000000000, //56
#GOGOEND

0,
0,
0,
0,

0,
0,
7,
000000000000000000000008000000000000000000000000, //64

#GOGOSTART
1110,
1100,
11,
1100,

1110,
1100,
11,
500000000000000000000008000000000000000000000000, //72

1110,
2200,
11,
2200,

1110,
2200,
11,
2200, //80

3333,
3330,
6,
000000000000000008000000000000000000000000000000,

3333,
3330,
6,
000000000000000008000000
#GOGOEND
000000000000
#SCROLL 1
000000000000, //88

#GOGOSTART
9,
0,
000000000009000000000000000000000000000000000008,
0,

9,
0,
000000000009000000000000000000000000000000000008,
0,
#GOGOEND

#BARLINEOFF
0,
0, //98
#END