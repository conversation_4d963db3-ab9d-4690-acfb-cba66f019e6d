//TJADB Project
TITLE:<PERSON><PERSON><PERSON>gänger
TITLEJA:拝啓ドッペルゲンガー
SUBTITLE:--kemu feat. <PERSON>sune Miku
SUBTITLEJA:kemu feat. 初音ミク
BPM:181
WAVE:<PERSON><PERSON><PERSON>ganger (<PERSON>ku).ogg
OFFSET:-2.052
DEMOSTART:46.467

//Shinuchi: 5890/3810/1970/1080

COURSE:Oni
LEVEL:10
BALLOON:
SCOREINIT:380,1110
SCOREDIFF:93

#START
1111111111112000,

#GOGOSTART
1020112010212012,
1020112012212011,
1111201122101222,
100000200000100200200100100000202020100000200000, //5
#GOGOEND

1111,
1000100010001011,
1110201110201110,
2011102011111111,

1001201011102012,
1011201002102010,
1001201011102012,
1011201002112022, //13

1001201011102012,
1011201122112211,
#GOGOSTART
1020112010212012,
1020112012212011,
#GOGOEND

1011201011102012,
1011201002102010,
1011221122102012,
1011201002112022, //21

1001201011102012,
1011201122112222,
#GOGOSTART
1020112010212012,
1020112012212010,
#GOGOEND

1110101111101011,
1110101111101011,
1110101111101011,
1110101111102011, //29

1021102110211021,
1021102110112011,
1202222210222222,
100000000300000000300000600000000000008000000000,
3003003030000000, //34

#GOGOSTART
1022102011201022,
1020112010221020,
1021201210201022,
1120102210201120,

1212102121201022,
1020112010221020,
1122105000000000,
000000000008000000000000200200200200100100100100, //42

2011201022102011,
2010221020112010,
2012102120102011,
2210201120102210,

2121201212102011,
2011221122111210,
3004003030040030,
#GOGOEND
32323020, //50

#GOGOSTART
1020112010212012,
1020112012212011,
1111201122101222,
100000200000100200200100100000202020100000200000,

1020112010212012,
1020112012212011,
1111201122101222,
100000200000100200200100100000202020100000200000, //58
#GOGOEND

30000003,
0000221110300300,
30000004,
0000122212221222,

1111111111111111,
1111111111111111,
0000000000300300,
3022122211112222, //66

#GOGOSTART
1022102011201022,
1020112010221020,
1021201210201022,
1120102210201120,

1212102121201022,
1022112211221211,
1021021010210210,
#GOGOEND
3020302030112210,
4010401040221000, //75

#GOGOSTART
1020112010212012,
1020112012212011,
1111201122101222,
100000200000100200200100100000202020100000200000,

1020112211212012,
1022112212212011,
1111201122101222,
100100200200100200200100100000202020100000200000, //83
#GOGOEND

0,
00000
#SCROLL 2
300,
#BARLINEOFF
0,
0, //87
#END


COURSE:Hard
LEVEL:7
BALLOON:21,11
SCOREINIT:500,2180
SCOREDIFF:130

#START
0,

#GOGOSTART
1010200010111000,
1010200010111000,
1010201011101000,
1010200020222020,//5
#GOGOEND

1,
0,
7,
0008,

10201122,
02010221,
10201122,
02010221, //13

10222011,
10221120,
#GOGOSTART
1010200010111000,
1010200010111000,
#GOGOEND

10201122,
02010221,
10201122,
02010221, //21

10222011,
10221120,
#GOGOSTART
1010200010111000,
1010200010111000,
#GOGOEND

11011101,
11011101,
11011101,
11011020, //29

12121212,
12121220,
3003003040040040,
300000000300000000300000600000000000008000000000,
3003003030000000,//34

#GOGOSTART
1011101010100010,
01122010,
1011101010100020,
02211020,

1010202011100010,
01212121,
2002005000000000,
000000000008000000000000200000000000200000000000, //42

1011101010100010,
01122010,
1011101010100020,
02211020,

1010202011100010,
21212120,
3003003040040040,
#GOGOEND
3332, //50

#GOGOSTART
1010200010111000,
1010200010111000,
1010201011101000,
1010200020222000,

1010200010111000,
1010200010111000,
1010201011101000,
1010200020222020, //58
#GOGOEND

30000003,
0000000000300300,
30000007,
0008,

5,
000000000000000000000000000000000000000000000008,
0000000000300300,
600000000000008000000000200000000000200000000000, //66

#GOGOSTART
1011101010100010,
01122010,
1011101010100020,
02211020,

1010202011100010,
21212120,
3003003040040040,
#GOGOEND
300000000000300000000000600000000008000000000000,
3332, //75

#GOGOSTART
1010200010111000,
1010200010111000,
1010201011101000,
1010200020222000,

1010200010111000,
1010200010111000,
1010201011101000,
1010200020222020, //83
#GOGOEND

0,
00000300,
#BARLINEOFF
0,
0, //87
#END


COURSE:Normal
LEVEL:5
BALLOON:14,15,7,6
SCOREINIT:700,4220
SCOREDIFF:198

#START
0,

#GOGOSTART
10101110,
10101110,
2222,
500000000000000000000000000000000000000000000008, //5
#GOGOEND

0,
0,
7,
0008,

10101001,
01010000,
10101001,
01010000, //13

1120,
1122,
#GOGOSTART
10101110,
10101110,
#GOGOEND

10101001,
01010000,
10101001,
01010000, //21

1120,
1122,
#GOGOSTART
10101110,
10101110,
#GOGOEND

1111,
1111,
1111,
1110, //29

2222,
2222,
9,
09,
8, //34

#GOGOSTART
10101001,
01101000,
10101001,
01101000,

10101001,
01101010,
5,
000000000008000000000000000000000000000000000000, //42

10102001,
01101000,
10102001,
01101000,

10102001,
01101010,
44,
#GOGOEND
3330, //50

#GOGOSTART
10101110,
10101110,
2222,
500000000000000000000000000000000008000000000000,

10101110,
10101110,
2222,
500000000000000000000000000000000008000000000000, //58
#GOGOEND

30000003,
0,
30000007,
0008,

5,
000000000000000000000000000000000000000000000008,
00000700,
08, //66

#GOGOSTART
10102001,
01101000,
10102001,
01101000,

10102001,
01101010,
44,
#GOGOEND
3330,
3330, //75

#GOGOSTART
10101110,
10101110,
2222,
500000000000000000000000000000000008000000000000,

10101110,
10101110,
2222,
500000000000000000000000000000000000000000000008,//83
#GOGOEND

0,
00000300,
#BARLINEOFF
0,
0, //87
#END


COURSE:Easy
LEVEL:3
BALLOON:9,12,5
SCOREINIT:610,6790
SCOREDIFF:210

#START
0,

#GOGOSTART
1110,
1110,
22,
500000000000000000000000000000000000000000000008,//5
#GOGOEND

0,
0,
7,
00000800,

11,
1,
11,
1, //13

11,
22,
#GOGOSTART
1110,
1110,
#GOGOEND

11,
1,
11,
1, //21

11,
22,
#GOGOSTART
1110,
1110,
#GOGOEND

11,
11,
11,
1, //29

22,
22,
9,
09,
8, //34

#GOGOSTART
11,
1110,
11,
1110,

11,
1110,
5,
000000000008000000000000000000000000000000000000, //42

12,
1110,
12,
1110,

12,
1110,
33,
#GOGOEND
3330, //50

#GOGOSTART
1110,
1110,
22,
500000000000000000000000000008000000000000000000,

1110,
1110,
22,
500000000000000000000000000008000000000000000000, //58
#GOGOEND

30000003,
0,
30000003,
0,

5,
000000000000000000000000000000000000000000000008,
00000700,
08, //66

#GOGOSTART
12,
1110,
12,
1110,

12,
1110,
33,
#GOGOEND
3330,
3330, //75

#GOGOSTART
1110,
1110,
22,
500000000000000000000000000008000000000000000000,

1110,
1110,
22,
500000000000000000000000000000000000000000000008, //83
#GOGOEND

0,
00000300,
#BARLINEOFF
0,
0, //87
#END