//TJADB Project
TITLE:Kurakura
TITLEJA:クラクラ
SUBTITLE:--SPY×FAMILY Season 2 Opening Theme Song
SUBTITLEJA:TVアニメ『SPY×FAMILY』Season 2 オープニング主題歌
BPM:188
WAVE:Kurakura.ogg
OFFSET:-2.268
DEMOSTART:50.289


COURSE:Oni
LEVEL:7
BALLOON:15
SCOREINIT:660,2100
SCOREDIFF:180

#START
0,
0,
0000000010111100,

1011102000101020,
0300,
1101101101101111, //6

1000100020111020,
0010201020222011,
1000200020111020,
0010201020112011,

1000100020111020,
0010201020222011,
1000200020111020,
0020112010200020, //14

3000100010002022,
11020121,
1020102011102010,
0010200010222022,

1000100010002022,
11020221,
1020102011102000,
2000201110222000, //22

10210210,
1011201110202000,
10210210,
1011101120202000,

10210210,
1011201110200030,
01210404,
0011101020001000, //30

20122022,
2010200220201020,
20122022,
2010200220201020,
20122103,
00201227,
000000000000000000000000000000000000000000000008,
00000000001010
#GOGOSTART
11, //38

1010202000102011,
1020102011200011,
1010202000102011,
1020102011200011,

1010201000112011,
1010201000112011,
1022102010102050,
000000000000000008000000000000100000100000100100, //46

1110202000102011,
1020102011200011,
1010201020102022,
1010202000111020,

0020102000100011,
1020102000102011,
1020201110200060,
000000000000000008000000000000200000200000
#GOGOEND
200000, //54

02220222,
00202020002010
#GOGOSTART
11,
1010201110200030,
0430400
#GOGOEND
2,
02202000, //59

3000100020111020,
0010201020222011,
1000200020111020,
0010201020112011,

1000100020111020,
0011201020222011,
1011101120222010,
0030030030030040,
0,
0, //69
#END


COURSE:Hard
LEVEL:4
BALLOON:11
SCOREINIT:830,3710
SCOREDIFF:250

#START
0,
0,
0,

10110111,
0300,
500000000000000000000000000000000000000008000000, //6

10102011,
02011020,
10102011,
02201020,

10102011,
02011020,
10102011,
02020202, //14

1112,
11010220,
10112011,
01200000,

1112,
11010220,
10112010,
22, //22

500000000000000000000000000000000000000008000000,
10111020,
500000000000000000000000000000000000000008000000,
10111020,

500000000000000000000000000000000000000008000000,
10111103,
01110303,
00011010, //30

20022020,
2220,
20022020,
2220,

20022003,
00101117,
000000000000000000000000000000000000000000000008,
0000011
#GOGOSTART
1, //38

01120111,
02022201,
01120111,
02022201,

01120111,
01120115,
0,
000000000000000008000000000000100000100000100000, //46

01120111,
02022201,
01102020,
11110001,

01120101,
01120115,
0,
000000000000000008000000000000200000200000
#GOGOEND
200000, //54

02220222,
0222010
#GOGOSTART
3,
01120103,
0330300
#GOGOEND
0,
0, //59

10102011,
02011020,
10102011,
02201020,

10102011,
02011020,
10102011,
0030030030030040,
0,
0, //69
#END


COURSE:Normal
LEVEL:4
BALLOON:8
SCOREINIT:1380,7820
SCOREDIFF:755

#START
0,
0,
0,

1,
0300,
0, //6

10001001,
0,
20002002,
0,

10001001,
0,
20002002,
0, //14

1011,
1,
1011,
0100,

1011,
1,
1011,
22, //22

500000000000000000000000000000000008000000000000,
11,
500000000000000000000000000000000008000000000000,
11,

500000000000000000000000000000000008000000000000,
10001003,
00030303,
0, //30

20022000,
2220,
20022000,
2220,

20022003,
00101007,
000000000000000000000000000000000000000000000008,
0000000
#GOGOSTART
3, //38

00010001,
01020001,
00010001,
01020001,

00010001,
01010006,
0,
000000000000000008000000000000000000000000300000, //46

00010001,
01020001,
00101000,
20020001,

00010001,
00010006,
0,
000000000000000008000000000000000000000000
#GOGOEND
200000, //54

00020002,
0002000
#GOGOSTART
3,
00030003,
0030300
#GOGOEND
0,
0, //59

10001001,
01,
20002002,
02,

10001001,
01,
20002002,
000000600000000000000000000000000000000008000000,
0,
0, //69
#END


COURSE:Easy
LEVEL:2
BALLOON:7
SCOREINIT:1420,13970
SCOREDIFF:1350

#START
0,
0,
0,

1,
0300,
0, //6

10000001,
0,
10000001,
0,

10000001,
0,
10000001,
0, //14

1,
1,
1,
0,

1,
1,
2,
0, //22

500000000000000000000000000008000000000000000000,
1,
500000000000000000000000000008000000000000000000,
1,

500000000000000000000000000008000000000000000000,
10000003,
00000003,
0, //30

2,
22,
2,
22,

20000003,
00000007,
000000000000000000000000000000000000000000000008,
0000000
#GOGOSTART
3, //38

00010001,
00000001,
00010001,
00000001,

00010001,
00000006,
0,
000000000000000008000000000000000000000000300000, //46

00010001,
00000001,
0,
20000001,

00010001,
00000006,
0,
000000000000000008000000000000000000000000
#GOGOEND
200000, //54

00000002,
0000000
#GOGOSTART
3,
00030003,
0000400
#GOGOEND
0,
0, //59

10000001,
0,
20000002,
0,

10000001,
0,
20000001,
000000600000000000000000000000000000000008000000,
0,
0, //69
#END