//TJADB Project
TITLE:Utsukushii Hire
TITLEJA:美しい鰭
SUBTITLE:--Detective Conan: Black Iron Submarine
SUBTITLEJA:「名探偵コナン 黒鉄の魚影」より
BPM:98
WAVE:Utsukushii Hire.ogg
OFFSET:-2.267
DEMOSTART:51.846


COURSE:Oni
LEVEL:6
BALLOON:42,40,40
SCOREINIT:970,3110
SCOREDIFF:268

#START
3112,
100001200100000100202200,
100001200100000100200100,
200002200200000100202200,

3112,
100001200100000100202200,
7,
000000000000000000000000000000000008000000000000, //8

#MEASURE 7/8
100101200102102002000,
100101200102102002000,
#MEASURE 4/4
21121102,
500000000000000000000000000008000000000000000000,

#MEASURE 7/8
100101200102102002000,
100101200102102002000,
#MEASURE 4/4
200100101200100100100200,
500000000000000000000008000000000000200000000000, //16

100100200202100100200200,
100100200101200100200000,
100100200202100101200200,
7,
000000000000800000202200, //21

#GOGOSTART
300001200100000200100200,
100102001100000100202200,
100001200100000200000202,
202001001200000100202200,

300001200100001200100200,
100102001100000102200200,
32230102,
600000000000000000000000000008000000000000000000, //29
#GOGOEND

100202100200100100200100,
100001100001202200100000,
100202100200100100200100,
200001200001202200101200,

100202100200100100200100,
100001100001202200101200,
7,
000000000000800000202200, //37

#GOGOSTART
300001200100000200100200,
100102001100000100202200,
100001200100000200000202,
202001001200000100202200,

300001200100001200100200,
100102001100000102200200,
32230102,
600000000000000000000000000008000000000000000000, //45
#GOGOEND

3,
0, //47
#END


COURSE:Hard
LEVEL:4
BALLOON:15,36,36
SCOREINIT:1140,4950
SCOREDIFF:370

#START
3112,
100001000100000000200000,
100001000100000000200000,
200002000200000000000000,

3112,
100001000100000000200000,
900000000000000000000000000000000009000000000008,
0, //8

#MEASURE 7/8
100000100002002002000,
100000100002002002000,
#MEASURE 4/4
21101002,
500000000000000000000000000008000000000000000000,

#MEASURE 7/8
100000100002002002000,
100000100002002002000,
#MEASURE 4/4
21101112,
500000000000000000000008000000000000000000000000, //16

11121120,
11102000,
11121120,
7,
00008022, //21

#GOGOSTART
300001000100000100000200,
100001000100000000200200,
100001000100000200000200,
600000000000000000000008000000000000200000200000,

300001000100000100000200,
100001000100000100200200,
31130102,
600000000000000000000000000008000000000000000000, //29
#GOGOEND

11121101,
10102200,
11121101,
20202200,

11121101,
10102200,
7,
00008022, //37

#GOGOSTART
300001000100000100000200,
100001000100000000200200,
100001000100000200000200,
600000000000000000000008000000000000200000200000,

300001000100000100000200,
100001000100000100200200,
31130102,
600000000000000000000000000008000000000000000000, //45
#GOGOEND

3,
0, //47
#END


COURSE:Normal
LEVEL:2
BALLOON:11,22,22
SCOREINIT:1220,8340
SCOREDIFF:628

#START
3112,
10010000,
10010010,
20020000,

3112,
10010000,
900000000000000000000000000000000009000000000008,
0, //8

#MEASURE 7/8
1010000,
1010000,
#MEASURE 4/4
2110,
500000000000000000000000000008000000000000000000,

#MEASURE 7/8
1010000,
1010000,
#MEASURE 4/4
2110,
500000000000000000000008000000000000000000000000, //16

1110,
11102000,
1110,
7,
0082, //21

#GOGOSTART
30010100,
10010020,
10010200,
600000000000000000000008000000000000200000000000,

30010100,
10010020,
30030100,
600000000000000000000000000008000000000000000000, //29
#GOGOEND

11101000,
1120,
11101000,
2220,

11101000,
1120,
7,
0082, //37

#GOGOSTART
30010100,
10010020,
10010200,
600000000000000000000008000000000000200000000000,

30010100,
10010020,
30030100,
600000000000000000000000000008000000000000000000, //45
#GOGOEND

3,
0, //47
#END


COURSE:Easy
LEVEL:2
BALLOON:9,18,18
SCOREINIT:1330,12880
SCOREDIFF:960

#START
11,
1,
10010000,
2,

11,
1,
900000000000000000000000000000000009000000000008,
0, //8

#MEASURE 7/8
1,
1,
#MEASURE 4/4
21,
500000000000000000000000000008000000000000000000,

#MEASURE 7/8
1,
1,
#MEASURE 4/4
21,
500000000000000000000008000000000000000000000000, //16

1110,
12,
1110,
7,
08, //21

#GOGOSTART
30010000,
10010000,
10010000,
600000000000000000000008000000000000000000000000,

30010000,
10010000,
30030000,
600000000000000000000000000008000000000000000000, //29
#GOGOEND

1110,
12,
1110,
22,

1110,
12,
7,
08, //37

#GOGOSTART
30010000,
10010000,
10010000,
600000000000000000000008000000000000000000000000,

30010000,
10010000,
30030000,
600000000000000000000000000008000000000000000000, //45
#GOGOEND

3,
0, //47
#END