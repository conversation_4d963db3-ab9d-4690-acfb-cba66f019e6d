//TJADB Project
TITLE:Haru
TITLEJA:晴る
SUBTITLE:--TV Anime "Frieren: Beyond the Journey's End" Season 2 Opening Theme
SUBTITLEJA:TVアニメ『葬送のフリーレン』第2クールオープニングテーマ
BPM:204
WAVE:Haru.ogg
OFFSET:-0.714
DEMOSTART:49.513


COURSE:Oni
LEVEL:7
BALLOON:13
SCOREINIT:810,2630
SCOREDIFF:228

#START
1202,
1202,
1202,
1202,

10200220,
10200220,
10200220,
10201120, //8

10201220,
10201122,
10200120,
10201120,

10201220,
10201122,
10200120,
10202120, //16

10201220,
10201122,
1201,
00200120,

10200220,
10201122,
10200220,
10201120, //24

10201220,
1000200011102020,
10200120,
10201120,

10201220,
1000200011102020,
10200120,
10202120, //32

10201220,
1000200011102020,
10200120,
1222,

1000111000201010,
2020111000201010,
2020111020100020,
1110201000201110,
7,
0820, //42

#GOGOSTART
1000201011102000,
1000201011102000,
#GOGOEND
3,
0
#GOGOSTART
333,

3000200011102010,
1000200011102000,
600000000000000000000000000000000000000000000008,
01, //50

1000201011102000,
1000201011102000,
1110200011102010,
0000200020102220,

1000200020102220,
1000200020102220,
500000000000000000000000000000000000000000000008,
01, //58

1000201011102000,
1000201011102000,
1110200011102020,
1110202011102020,

1000200011102010,
1000200011102000,
600000000000000000000000000000000000000000000008,
01, //66

1000201011102000,
1000201011102000,
3344,
0000200020102220,
1000200020102220,
1000200020102220, //72
#GOGOEND

500000000000000000000000000000000000000000000008,
0,
00200220,
1, //76
#END


COURSE:Hard
LEVEL:4
BALLOON:24
SCOREINIT:800,3670
SCOREDIFF:238

#START
1202,
1202,
1202,
1202,

10200220,
1202,
10200220,
1211, //8

10201110,
1211,
1102,
1211,

10201110,
10201011,
0202,
1211, //16

10201110,
10201011,
0001,
0,

10200220,
1211,
10200220,
1211, //24

10201110,
1211,
1202,
1211,

10201110,
10201011,
0202,
1211, //32

10201110,
10201011,
00200220,
0222,

10100110,
11010111,
0700,
0,
0,
080000000000000000000000200000000000000000000000, //42

#GOGOSTART
10201120,
1211,
#GOGOEND
3,
0
#GOGOSTART
333,

30201120,
10201110,
600000000000000000000000000000000000000000000008,
01, //50

10201120,
1211,
10201011,
00202120,

10202120,
10202120,
500000000000000000000000000000000000000000000008,
01, //58

10201120,
1211,
10201120,
1211,

10201120,
10201110,
600000000000000000000000000000000000000000000008,
01, //66

10201120,
1211,
3344,
00202120,
10202120,
10202120, //72
#GOGOEND

500000000000000000000000000000000000000000000008,
0,
00200220,
1, //76
#END


COURSE:Normal
LEVEL:3
BALLOON:16
SCOREINIT:1090,7160
SCOREDIFF:478

#START
0200,
0200,
0200,
0200,

0200,
0200,
0200,
0, //8

11,
1001,
0101,
0100,

11,
1011,
0001,
0100, //16

11,
1011,
0001,
0,

0200,
0200,
0200,
0, //24

11,
1001,
0101,
0100,

11,
1011,
0001,
0100, //32

11,
1011,
0,
0222,

0101,
0101,
0700,
0,
0,
080000000000000000000000200000000000000000000000, //42

#GOGOSTART
1012,
1011,
#GOGOEND
3,
0
#GOGOSTART
333,

3012,
1011,
600000000000000000000000000000000000000000000008,
01, //50

1012,
11,
1111,
0022,

1022,
12,
500000000000000000000000000000000000000000000008,
01, //58

1012,
1011,
1012,
1111,

1012,
1011,
600000000000000000000000000000000000000000000008,
01, //66

1012,
1011,
3333,
0022,
1022,
12, //72
#GOGOEND

500000000000000000000000000000000000000000000008,
0,
0,
0, //76
#END


COURSE:Easy
LEVEL:3
BALLOON:13
SCOREINIT:1140,11030
SCOREDIFF:815

#START
0,
0,
0,
0,

0200,
0200,
0200,
0, //8

1,
1001,
0,
0,

1,
1001,
0001,
0100, //16

1,
1001,
0001,
0,

0200,
0200,
0200,
0, //24

1,
1001,
0,
0,

1,
1001,
0001,
0100, //32

1,
1001,
0,
0202,

0,
0101,
0700,
0,
0,
080000000000000000000000000000000000000000000000, //42

#GOGOSTART
11,
1011,
#GOGOEND
3,
0
#GOGOSTART
000,

31,
1011,
600000000000000000000000000000000000000000000008,
0, //50

11,
11,
1111,
0,

12,
12,
500000000000000000000000000000000000000000000008,
0, //58

11,
1011,
11,
1110,

11,
1011,
600000000000000000000000000000000000000000000008,
0, //66

11,
11,
3333,
0,
12,
12, //72
#GOGOEND

500000000000000000000000000000000000000000000008,
0,
0,
0, //76
#END