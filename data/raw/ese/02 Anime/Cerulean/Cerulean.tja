//TJADB Project
TITLE:Cerulean
TITLEJA:セルリアン
SUBTITLE:--BACK-ON/Gundam Build Fighters Try
SUBTITLEJA:「ガンダムビルドファイターズトライ」より
BPM:196
WAVE:Cerulean.ogg
OFFSET:-1.607
DEMOSTART:59.725

COURSE:Oni
LEVEL:7
BALLOON:8
SCOREINIT:660,2200
SCOREDIFF:180

#START
20202022,
02202020,
20202027,
08,

#GOGOSTART
10201021,
01202020,
10201021,
0010200020102022, //8

10210102,
02101210,
10210210,
1010200022221000,

10201021,
0010200020102022,
10201021,
0010200020102022, //16

10210102,
0020100010102022,
10210210,
1010201022222000,

30201021,
0010202210002000,
10201021,
01212121, //24
#GOGOEND

01200020,
00200021,
01200021,
12020203,

01201021,
10201021,
01200020,
1010201022222030, //32

0010201020222010,
0010201022202010,
0010201020222010,
0010201022202010,

0010201020222010,
1010201020222010,
0010201011102220,
1022102011102030, //40

00022003,
00000003,
00201121,
11212120,

00300222,
11112020,
01120223,
03004040, //48

#GOGOSTART
1000200210102010,
0010200220102022,
1000200210102010,
0010202220102022,

1000201022102010,
0010200210102000,
10210210,
1010201022221000, //56

1000200210102010,
0010200220102022,
1000200210102010,
0010200020102022,

1000201022102010,
0010200210102022,
10210210,
1010201022222000, //64

30201121,
0010202210102000,
10201121,
0010201020221000,

30030060,
000000000008000000000000200000100100200000000000,
30030030,
0010201022102222,
#GOGOEND

0,
0, //74
#END


COURSE:Hard
LEVEL:5
BALLOON:4
SCOREINIT:700,3120
SCOREDIFF:208

#START
20202002,
0222,
20202005,
000000000000000000000008000000000000000000000000,

#GOGOSTART
10220110,
0222,
10220110,
0222, //8

10220101,
0211,
10210210,
0022,

10220110,
0222,
10220110,
0222, //16

10220101,
0222,
10210210,
11202220,

30201021,
01201010,
10201021,
01211011, //24
#GOGOEND

0202,
00200021,
01200020,
11210123,

01200020,
00200021,
01200020,
11212123, //32

01201121,
01201121,
01201121,
01211121,

01201121,
01201121,
01201022,
11020203, //40

00022003,
00000003,
0011,
11010120,

00300220,
11102020,
01110103,
03004040, //48

#GOGOSTART
10220110,
0222,
10220110,
0222,

10220101,
0222,
10210210,
12102020, //56

10220110,
0222,
10220110,
00222020,

10220101,
0222,
10210210,
11217080, //64

30201021,
01201010,
10201021,
01202020,

30201021,
01203030,
30030060,
000000000000000000000000000008000000000000300000,
#GOGOEND

0,
0, //74
#END


COURSE:Normal
LEVEL:5
BALLOON:
SCOREINIT:620,3470
SCOREDIFF:205

#START
20020020,
0022,
20020050,
000000000000000000000008000000000000000000000000,

#GOGOSTART
30030030,
0222,
30030030,
0222, //8

10110202,
0222,
30030030,
0011,

30030030,
0222,
30030030,
0222, //16

10110202,
0222,
30030030,
0444,

30001011,
0110,
10001011,
01101011, //24
#GOGOEND

0202,
00200021,
01200020,
00200021,

01200020,
00200021,
01200020,
10102203, //32

00201103,
00201103,
00201103,
00201103,

00201103,
00201103,
0212,
11010103, //40

00002003,
00002003,
0011,
30030030,

00300220,
1111,
02020204,
04000000, //48

#GOGOSTART
30030030,
0222,
30030030,
0222,

10110202,
0222,
30030030,
0011, //56

30030030,
0222,
30030030,
0222,

10110202,
0222,
30030030,
0444, //64

30001011,
0110,
10001011,
0110,

30001011,
0033,
30030060,
000000000000000000000000000000000000000008000000,
#GOGOEND

0,
0, //74
#END


COURSE:Easy
LEVEL:4
BALLOON:
SCOREINIT:520,4730
SCOREDIFF:203

#START
2,
0022,
2005,
000000000000000000000008000000000000000000000000,

#GOGOSTART
30030030,
0,
30030030,
0, //8

11,
11,
30030030,
0011,

30030030,
0,
30030030,
0, //16

11,
11,
30030030,
0111,

6,
000000000000000000000000000008000000000000000000,
30030030,
0110, //24
#GOGOEND

0202,
0202,
0202,
0202,

0202,
0202,
0202,
10101003, //32

00201003,
00201003,
00201003,
00201003,

00201003,
00201003,
0210,
30030003, //40

00000003,
00000003,
0011,
1011,

0302,
22,
20002003,
03004040, //48

#GOGOSTART
30030030,
0011,
30030030,
0011,

11,
11,
30030030,
0011, //56

30030030,
0011,
30030030,
0011,

11,
11,
30030030,
0111, //64

6,
000000000000000000000000000008000000000000000000,
10010010,
0,

30030030,
0011,
30030060,
000000000000000000000000000000000000000008000000,
#GOGOEND

0,
0, //74
#END