//TJADB Project
TITLE:Umapyoi Densetsu
TITLEJA:うまぴょい伝説
SUBTITLE:--<PERSON><PERSON> Derby
SUBTITLEJA:「ウマ娘 プリティーダービー」より
BPM:114
WAVE:Umapyoi Densetsu.ogg
OFFSET:-0.518
DEMOSTART:64.991


COURSE:Edit
LEVEL:9
BALLOON:8
SCOREINIT:1210
SCOREDIFF:0

#START
30022012,
1120100010101120,
1111, //3

122100
#BPMCHANGE 110.15
#SCROLL 1.03
122100,
#BPMCHANGE 110.14
122
#BPMCHANGE 99.71
#SCROLL 1.13
122
#BPMCHANGE 88.34
#SCROLL 1.29
100
#BPMCHANGE 44.65
#SCROLL 2.53
100,
#BPMCHANGE 116.14
#SCROLL 0.98
500000000000000000000000000000000008000000001010, //6

#BPMCHANGE 113.43
#SCROLL 1
1,
0,
#MEASURE 9/16
#BPMCHANGE 153
0,
#MEASURE 3/16
#BP<PERSON><PERSON><PERSON><PERSON> 166
#SCROLL 2
#GOGOSTART
0, //10
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 170
#SCROLL 1
1002102010221020,
1022102010221120,
1002102010221220,
1110
#SCROLL 1.5
4000
#SCROLL 1
1110
#SCROLL 1.5
4000,

#SCROLL 1
1111111011222020,
1111111011202000,
1122112211221122,
#SCROLL 1.5
3334, //18

#SCROLL 1
11221011222020
#SCROLL 1.5
40,
#SCROLL 1
11112211
#SCROLL 1.5
30403040,
#SCROLL 1
1021102211101111,
1010101011021120,

10221212101020
#SCROLL 1.5
40,
#SCROLL 1
100100100000100200100200101010200100200000
#SCROLL 1.5
400000,
#SCROLL 1
10221111201220
#SCROLL 1.5
40,
#SCROLL 1
200200100100500008000000100100200200500008000000, //26

1120
#SCROLL 1.5
4000
#SCROLL 1
1120
#SCROLL 1.5
4000,
#SCROLL 1
1122112212201102,
111010
#SCROLL 1.5
40
#SCROLL 1
110110
#SCROLL 1.5
40,
#SCROLL 1
11101022111110
#SCROLL 1.5
40, //30

#SCROLL 1
1010201011102010,
1010201011112022,
1010201011102010,
1011201011102222,
1010201011102010,
1110700000008000,

3022203000222030,
0022203000222030,
1122203011222030,
1122203011222030,
01212121,
2210221020122012,
100
#SCROLL 2
3, //43

#SCROLL 1
#GOGOSTART
1011201011201022,
1011212010
#SCROLL 1.5
404000,
#SCROLL 1
1011201011201022,
#SCROLL 1.5
30
#SCROLL 1
22
#SCROLL 1.5
30
#SCROLL 1
22
#SCROLL 1.5
30
#SCROLL 1
22
#SCROLL 1.5
3000,

#SCROLL 1
1022102011101020,
1022102011102010,
2222222222122020,
#SCROLL 1.5
4444, //51

#SCROLL 1
1011201011201022,
1011212010
#SCROLL 1.5
404000,
#SCROLL 1
1011201011201022,
#SCROLL 1.5
30
#SCROLL 1
22
#SCROLL 1.5
30
#SCROLL 1
22
#SCROLL 1.5
30
#SCROLL 1
22
#SCROLL 1.5
3000,

#SCROLL 1
1022102011101020,
1022102011102010, //57

1022102210201120,
10222210
#SCROLL 1.5
40004000,
#SCROLL 1
1022102210201120,
1022221011111111, //61
#GOGOEND

#SCROLL 1.5
4000
#SCROLL 1
102010221020,
1022102010221120,
1002102010221220,
1110
#SCROLL 1.5
4000
#SCROLL 1
1110
#SCROLL 1.5
4000,

#SCROLL 1
1111111111222020,
1111111111202000,
1122112211221122,
000
#SCROLL 3
3, //69

#SCROLL 1
#GOGOSTART
1011201011201022,
1011212010
#SCROLL 1.5
404000,
#SCROLL 1
1011201011201022,
#SCROLL 1.5
30
#SCROLL 1
22
#SCROLL 1.5
30
#SCROLL 1
22
#SCROLL 1.5
30
#SCROLL 1
22
#SCROLL 1.5
3000,

#SCROLL 1
1022102011101122,
1022102011112010,
2222222222122020,
#SCROLL 1.5
4444, //77

#SCROLL 1
1011201011201022,
1011212010
#SCROLL 1.5
404000,
#SCROLL 1
1011201011201022,
#BARLINEOFF
#SCROLL 1.5
30
#SCROLL 1
22
#SCROLL 1.5
30
#SCROLL 1
22
#SCROLL 1.5
30
#SCROLL 1
22
#SCROLL 1.5
3000, //81

#BARLINEON
#SCROLL 1
1022102011101122,
1022102011112010,

1022102210201120,
10222210
#SCROLL 1.5
40004000,
#SCROLL 1
1022102210201120,
1022221011111111, //87
#GOGOEND

#SCROLL 2
4,
#SCROLL 1
0,
0,
0, //91
#END


COURSE:Oni
LEVEL:7
BALLOON:8,11,13
SCOREINIT:1750
SCOREDIFF:0

#START
30022010,
1110100010101120,
1111, //3

122100
#BPMCHANGE 110.15
#SCROLL 1.03
122100,
#BPMCHANGE 110.14
122
#BPMCHANGE 99.71
#SCROLL 1.13
122
#BPMCHANGE 88.34
#SCROLL 1.29
100
#BPMCHANGE 44.65
#SCROLL 2.53
100,
#BPMCHANGE 116.14
#SCROLL 0.98
500000000000000000000000000000000008000000001010, //6

#BPMCHANGE 113.43
#SCROLL 1
1,
0,
#MEASURE 9/16
#BPMCHANGE 153
0,
#MEASURE 3/16
#BPMCHANGE 166
#SCROLL 2
#GOGOSTART
0, //10
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 170
#SCROLL 1
5,
000000000000000000000008000000200200200000200000,
500000000000000000000000000000000008000000000000,
1110200011102000,

500000000000000000000008000000200200200000200000,
500000000000000000000008000000000000400000000000,
12121212,
3334, //18

1111101020222000,
1110201010201020,
1011102210111110,
1010101011011020,

1020121210102000,
1110121212121000,
1011101020222020,
100100100000500008000000200200200000500008000000, //26

1110202011102020,
1110111012202000,
1110102011011020,
1110101122204000,

10110112,
000000200000200000500000000000000008000000000000,
1020202010221010,
0011101000202022,
1000202022202000,
1110700000008000, //36

1010201000112010,
0011201000112010,
1011201000112010,
0011201000112010,

02121212,
12121212,
7008, //43

#GOGOSTART
1011201011201020,
11221440,
1011201011201022,
1333,

1020002011102010,
1020002011102000,
500000000000000000000008000000100100100000100000,
4444, //51

1011201011201020,
11221440,
1011201011201022,
1333,
1020002011102010,
1020002011102000,

1020102011202020,
1011101020004000,
1020102011202020,
1011101010001000, //61
#GOGOEND

5,
000000000000000000000008000000200200200000200000,
500000000000000000000000000000000008000000000000,
1110200011102000,

500000000000000000000008000000200200200000200000,
500000000000000000000008000000000000400000000000,
12121212,
7008, //69

#GOGOSTART
1011201011201020,
11221440,
1011201011201022,
1333,

1020002011102010,
1020002011102000,
500000000000000000000008000000100100100000100000,
4444, //77

1011201011201020,
11221440,
1011201011201022,
#BARLINEOFF
1333,
#BARLINEON
1020002011102010,
1020002011102000,

1020102011202020,
1011101020004000,
1020102011202020,
100000100100100000100000500000000000000008000000, //87
#GOGOEND

4,
0,
0,
0, //91
#END


COURSE:Hard
LEVEL:6
BALLOON:7,8,8
SCOREINIT:2320
SCOREDIFF:0

#START
30022010,
10101100,
1111, //3

1
#BPMCHANGE 110.15
#SCROLL 1.03
1,
#BPMCHANGE 110.14
1
#BPMCHANGE 99.71
#SCROLL 1.13
1
#BPMCHANGE 88.34
#SCROLL 1.29
1
#BPMCHANGE 44.65
#SCROLL 2.53
1,
#BPMCHANGE 116.14
#SCROLL 0.98
500000000000000000000000000000000000000000000008, //6

#BPMCHANGE 113.43
#SCROLL 1
0,
0,
#MEASURE 9/16
#BPMCHANGE 153
0,
#MEASURE 3/16
#BPMCHANGE 166
#SCROLL 2
#GOGOSTART
0, //10
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 170
#SCROLL 1
5,
000000000000000000000008000000000000200000000000,
500000000000000000000000000000000008000000000000,
11201120,

500000000000000000000008000000000000200000000000,
500000000000000000000008000000000000200000000000,
12121212,
3334, //18

1011100020222000,
1110200010201020,
1010101010111000,
1010101011011010,

1000111010102000,
1110101010102000,
1011100020222000,
100000100000500008000000200000200000500008000000, //26

11221122,
1010101011102000,
1110100022022000,
1110101020204000,

10110111,
000000100000100000500000000000000008000000000000,
11111011,
01110222,
10222020,
11700080, //36

11210121,
01210121,
11210121,
01210121,

02121212,
12121212,
7008, //43

#GOGOSTART
11201202,
11220440,
1010200010200011,
1333,

11011021,
11011020,
500000000000000000000008000000100000100000000000,
4444, //51

11201202,
11220440,
1010200010200011,
1333,
11011021,
11011020,

1020102011102020,
0011101020004000,
1020102011102020,
0011101010001000, //61
#GOGOEND

5,
000000000000000000000008000000000000200000000000,
500000000000000000000000000000000008000000000000,
11201120,

500000000000000000000008000000000000200000000000,
500000000000000000000008000000000000200000000000,
12121212,
7008, //69

#GOGOSTART
11201202,
11220440,
1010200010200011,
1333,

11011021,
11011020,
500000000000000000000008000000100000100000000000,
4444, //77

11201202,
11220440,
1010200010200011,
#BARLINEOFF
1333,
#BARLINEON
11011021,
11011020,

1020102011102020,
0011101020004000,
1020102011102020,
000000100100100000100000500000000000000008000000, //87
#GOGOEND

4,
0,
0,
0, //91
#END


COURSE:Normal
LEVEL:5
BALLOON:12,5,6,7
SCOREINIT:3160
SCOREDIFF:0

#START
3011,
10001100,
1111, //3

1
#BPMCHANGE 110.15
#SCROLL 1.03
1,
#BPMCHANGE 110.14
7
#BPMCHANGE 99.71
#SCROLL 1.13
0
#BPMCHANGE 88.34
#SCROLL 1.29
0
#BPMCHANGE 44.65
#SCROLL 2.53
8,
#BPMCHANGE 116.14
#SCROLL 0.98
500000000000000000000000000000000000000000000008, //6

#BPMCHANGE 113.43
#SCROLL 1
0,
0,
#MEASURE 9/16
#BPMCHANGE 153
0,
#MEASURE 3/16
#BPMCHANGE 166
#SCROLL 2
#GOGOSTART
0, //10
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 170
#SCROLL 1
5,
000000000000000000000000000000000008000000000000,
500000000000000000000000000000000008000000000000,
1212,

500000000000000000000000000000000008000000000000,
500000000000000000000000000000000008000000000000,
1111,
3334, //18

1110,
11102020,
1111,
11111010,

10111010,
500008000000500008000000500000000008000000000000,
1122,
11102220, //26

1212,
11101020,
11102220,
11104000,

10110111,
000000100000100000500000000000000008000000000000,
10111011,
01110000,
10222020,
7008, //36

10110111,
01110111,
01110111,
01110111,

0111,
1111,
7008, //43

#GOGOSTART
11102201,
11110440,
11102201,
3333,

11011020,
11011020,
500000000000000000000000000008000000000000000000,
4444, //51

11102201,
11110440,
11102201,
3333,
11011020,
11011020,

10101101,
01104040,
10101101,
01101010, //61
#GOGOEND

5,
000000000000000000000000000000000008000000000000,
500000000000000000000000000000000008000000000000,
1212,

500000000000000000000000000000000008000000000000,
500000000000000000000000000000000008000000000000,
1111,
7008, //69

#GOGOSTART
11102201,
11110440,
11102201,
3333,

11011020,
11011020,
500000000000000000000000000008000000000000000000,
4444, //77

11102201,
11110440,
11102201,
#BARLINEOFF
3333,
#BARLINEON
11011020,
11011020,

11101101,
01104040,
11101101,
01101010, //87
#GOGOEND

4,
0,
0,
0, //91
#END


COURSE:Easy
LEVEL:3
BALLOON:10,3,12,5
SCOREINIT:6890
SCOREDIFF:0

#START
31,
1,
11, //3

1
#BPMCHANGE 110.15
#SCROLL 1.03
1,
#BPMCHANGE 110.14
7
#BPMCHANGE 99.71
#SCROLL 1.13
0
#BPMCHANGE 88.34
#SCROLL 1.29
0
#BPMCHANGE 44.65
#SCROLL 2.53
8,
#BPMCHANGE 116.14
#SCROLL 0.98
500000000000000000000000000000000000000000000008, //6

#BPMCHANGE 113.43
#SCROLL 1
0,
0,
#MEASURE 9/16
#BPMCHANGE 153
0,
#MEASURE 3/16
#BPMCHANGE 166
#SCROLL 2
#GOGOSTART
0, //10
#GOGOEND

#MEASURE 4/4
#BPMCHANGE 170
#SCROLL 1
5,
000000000000000000000000000000000008000000000000,
500000000000000000000000000008000000000000000000,
11,

500000000000000000000000000000000008000000000000,
500000000000000000000000000008000000000000000000,
11,
3330, //18

11,
1122,
11,
1110,

1110,
500008000000500008000000500008000000000000000000,
1122,
12, //26

1212,
11,
500000000008000000000000500000000008000000000000,
14,

10010001,
00010000,
10001001,
00010000,
11,
7008, //36

10010001,
00010001,
00010001,
00010009,

0,
000000000009000000000000,
8, //43

#GOGOSTART
11,
500000000000000000000000000008000000000000000000,
11,
3333,

11,
11,
500000000000000000000000000008000000000000000000,
4444, //51

11,
500000000000000000000000000008000000000000000000,
11,
3333,
11,
11,

1110,
1110,
1110,
1110, //61
#GOGOEND

5,
000000000000000000000000000000000008000000000000,
500000000000000000000000000008000000000000000000,
11,

500000000000000000000000000000000008000000000000,
500000000000000000000000000008000000000000000000,
9009,
8, //69

#GOGOSTART
11,
500000000000000000000000000008000000000000000000,
11,
3333,

11,
11,
500000000000000000000000000008000000000000000000,
4444, //77

11,
500000000000000000000000000008000000000000000000,
11,
#BARLINEOFF
3333,
#BARLINEON
11,
11,

1110,
1110,
1110,
1110, //87
#GOGOEND

4,
0,
0,
0, //91
#END