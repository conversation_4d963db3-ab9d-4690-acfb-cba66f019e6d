//TJADB Project
TITLE:Ichido Dake no Koi Nara
TITLEJA:一度だけの恋なら
SUBTITLE:--Walküre/Macross Δ
SUBTITLEJA:「マクロスΔ」より
BPM:172
WAVE:Ichido Dake no Koi Nara.ogg
OFFSET:-1.617
DEMOSTART:56.900


COURSE:Edit
LEVEL:9
BALLOON:4,4,4,4
SCOREINIT:590,1740
SCOREDIFF:155

#START
202020200000202020200000200200200200200000200000,
00022222,
202020200000202020200000200200200200200000200000,
00022222, //4

#GOGOSTART
1000201000102210,
#MEASURE 6/4
002010000020201010112030,
#MEASURE 4/4
0010202210201010,
200100222100200111200100,
200100100200111100222100,
2020222020000000, //10
#GOGOEND

11221221,
2020102010222010,
11221221,
2020102010222010,

11221221,
2020102010222010,
11221221,
2020102010222010, //18

1012202010222010,
0010221020102210,
1012202010222010,
0010221020102210,

1012202010222010,
0010221020102210,
1012202012202030,
03300000, //26

700000000800000000100000200000000000101010101010,
11102000,
700000000800000000100000200000000000101010101010,
11102000,

700000000800000000100000200000000000101010101010,
11102000,
700000000800000000100000200000000000101010101010,
11201301, //34

0011221122102010,
0022112211202010,
0011221122112210,
32232232,
#MEASURE 6/4
000000400000400000000000000000000000000000000000000001010100200000000000, //39

#MEASURE 4/4
#GOGOSTART
1120102012102010,
0011201022102010,
1120102012102010,
0011201022102220,

1021201010212010,
1021201010212010,
1222102022102010,
000000101010200000100000000000300000200100100000, //47

1022201000100010,
0010102120102110,
1011201011202010,
0011201022101020,

1021201012221010,
2210221012221022,
1022101022102010,
0010221212121110, //55

33230144,
100400000111100000000100,
000000100000200000200200100000200000101010100000,
200000100000202020100000200200100100200000100000,
#GOGOEND

202020200000202020200000200200200200200000300000,
002220300000000000000000,
0,
0, //63
#END


COURSE:Oni
LEVEL:7
BALLOON:3,3,3,3
SCOREINIT:700,2280
SCOREDIFF:193

#START
20220202,
02200222,
20220202,
02200222,

#GOGOSTART
30430303,
#MEASURE 6/4
000000300000600000000000000000000000000000000000000008000000200000100000,
#MEASURE 4/4
01221211,
2010221020112010,
2010102011102210,
22222020, //10
#GOGOEND

10121012,
1111,
10121212,
1111,

10121012,
1111,
10121212,
1000100010201022, //18

1000102010221020,
1000102210201000,
1000102010201022,
1000102210201000,

1000102010201022,
1000102210201000,
1000102010221030,
03302020, //26

70812201,
01202020,
70821102,
02102020,

70812201,
01202020,
70821102,
02220103, //34

0010202210100010,
0010202210100010,
0010202010102220,
30030060,
#MEASURE 6/4
000000000000000000000008000000000000000000000000000000100100200000200000, //39

#MEASURE 4/4
#GOGOSTART
1022102010202010,
0010102210202020,
1022102010202010,
0010102210202022,

1020102210201022,
1020101022102050,
000000000008000000100000200200100000100000100000,
0022101000102020, //47

1101201000100010,
0020102210202010,
1022102010202210,
0010102210102022,

1020102210201010,
2210201022101000,
1022101022102010,
0010221020102210, //55

33230033,
23001221,
0010202010201110,
2010201022112010,
#GOGOEND

2220222022222030,
0111200000000000,
0,
0, //63
#END


COURSE:Hard
LEVEL:5
BALLOON:
SCOREINIT:730,3180
SCOREDIFF:220

#START
20220202,
02200220,
20220202,
02200220,

#GOGOSTART
30030404,
#MEASURE 6/4
000000300000600000000000000000000000000000000000000000000008000000100000,
#MEASURE 4/4
01212121,
21212121,
21212121,
22222000, //10
#GOGOEND

10022022,
0111,
10220202,
0111,

10022022,
0111,
10220202,
00101222, //18

10022202,
0111,
10022202,
0111,

10022202,
0111,
10220203,
03300000, //26

10012101,
000000100000500000000000000000000000000008000000,
10012101,
000000100000500000000000000000000000000008000000,

10012101,
000000100000500000000000000000000000000008000000,
10012101,
000000100000200000500000000008000000000000300000, //34

01221101,
01221101,
01221122,
30030060,
#MEASURE 6/4
000000000000000000000008000000000000000000000000000000100100100000100000, //39

#MEASURE 4/4
#GOGOSTART
10210121,
01201120,
10210121,
01201120,

10210120,
10210120,
10210121,
0010101000111010, //47

30330303,
03300121,
10210121,
01201120,

10210121,
01201010,
10210121,
01212020, //55

33030044,
04000001,
01212121,
21212121,
#GOGOEND

2000200022202030,
0011100000000000,
0,
0, //63
#END


COURSE:Normal
LEVEL:5
BALLOON:
SCOREINIT:790,4350
SCOREDIFF:273

#START
20000202,
0200,
20000202,
0200,

#GOGOSTART
30030303,
#MEASURE 6/4
000000600000000000000000000000000000000000000000000000000008000000300000,
#MEASURE 4/4
0111,
1111,
1111,
2220, //10
#GOGOEND

10022020,
2220,
10220202,
0,

10022020,
2220,
10220202,
00000111, //18

10022202,
0220,
10022202,
0220,

10022202,
0220,
10220203,
03300000, //26

10002001,
0120,
10002001,
0120,

10002001,
0120,
10002001,
00100303, //34

00102203,
00102203,
0212,
30030060,
#MEASURE 6/4
000000000000000000000008000000000000000000000000000000000000000000000000, //39

#MEASURE 4/4
#GOGOSTART
10110101,
01100111,
10110101,
01100111,

10110101,
10110101,
00010115,
000000000000000008000000000000000000000000000000, //47

30330303,
03300111,
10110101,
01100111,

10110101,
01101010,
10110105,
000000000000000008000000000000000000000000000000, //55

33030033,
03000003,
0111,
1111,
#GOGOEND

00000003,
0300,
0,
0, //63
#END


COURSE:Easy
LEVEL:3
BALLOON:4,5,5,5,5
SCOREINIT:770,7220
SCOREDIFF:330

#START
2,
2200,
2,
2200,

#GOGOSTART
30030306,
#MEASURE 6/4
000000000000000000000000000000000000000000000008000000000000000000300000,
#MEASURE 4/4
0111,
0111,
0111,
0, //10
#GOGOEND

11,
1011,
11,
1011,

11,
1011,
11,
1, //18

11,
1110,
11,
1110,

11,
1110,
10001007,
08, //26

9009,
8,
9009,
8,

9009,
8,
9009,
80000003, //34

00202003,
00202003,
0222,
30030060,
#MEASURE 6/4
000000000000000000000008000000000000000000000000000000000000000000000000, //39

#MEASURE 4/4
#GOGOSTART
1011,
11,
1011,
11,

1120,
1120,
10001005,
000000000000000000000008000000000000000000000000, //47

30030003,
01,
1011,
11,

1120,
1120,
10001005,
000000000000000008000000000000000000000000000000, //55

30030030,
03000003,
0111,
0111,
#GOGOEND

00000003,
0300,
0,
0, //63
#END