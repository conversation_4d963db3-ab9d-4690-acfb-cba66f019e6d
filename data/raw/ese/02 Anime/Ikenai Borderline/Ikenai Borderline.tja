//TJADB Project
TITLE:Ikenai Borderline
TITLEJA:いけないボーダーライン
SUBTITLE:--Walkü<PERSON>/Macross Δ
SUBTITLEJA:「マクロスΔ」より
BPM:161
WAVE:Ikenai Borderline.ogg
OFFSET:-6.073
DEMOSTART:60.454

COURSE:Edit
LEVEL:8
BALLOON:
SCOREINIT:600,1840
SCOREDIFF:160

#START
02220222,
02220222,
02220222,
0020202000102220,

1000100010201120,
1000100010201120,
1000100011102010,
0010222020102220, //8

1000100011102010,
0010202210002010,
0010202011102000,
1111104000102000,

1020100010221020,
100222200200000100200200,
1020100010221020,
100000202020200000200000000000100000200200200000, //16

1020100010221020,
1000102011201020,
10121212,
1020202000102220,

1020102210201120,
100000202020200000200000000000100000200200200000,
1020102210201120,
100000202020200000200000000000100000200200200000, //24

1020112010221020,
1000102011201020,
1000102010201120,
1020202020202222,

12021202,
1020002010201220,
12021202,
1020002012102000,

12021202,
1020002010201220,
1120102011201020,
1122112211221212,
1000000010201120, //37

#GOGOSTART
1020112010201120,
1020122011212020,
1020112010201120,
1222101012201020,

1110400010201120,
1110202210201222,
1020112010201120,
1111100010201120, //45

1020112010201120,
1020122012221020,
1020112010201120,
1222101012212020,

1110400010201120,
1111202210201222,
1020112010221220,
#MEASURE 3/4
300030003003, //53
#GOGOEND

#MEASURE 4/4
06000000,
000000000000000008000000000000100000200200200000,
1020100010201120,
1020100010201120,

1020100011102010,
0010222020102220,
1020100011102010,
0010202210002010, //61

0010202011102000,
1111104010102220,
10050000,
000000000000000000000008000000000000000000000000,
0,
0, //67
#END


COURSE:Oni
LEVEL:7
BALLOON:20,11
SCOREINIT:770,2390
SCOREDIFF:210

#START
0,
0,
7,
00008111,

10102111,
10102111,
10101121,
20201222, //8

2000100020102022,
22112015,
000000000000000000000000000000000008000000000000,
30430030,

30201121,
1022202000102020,
10201121,
1022202000102010, //16

10201121,
1022202010111010,
5,
000000000008000000000000000000000000000000000000,

30201121,
2022202000102020,
10201121,
2022202000102010, //24

10201121,
2022202010111010,
500000000000000000000000000000000008000000700000,
000000000000000000000000000000080000000000000000,

11022101,
11022121,
21022101,
1010001010102011,

21022101,
11022121,
2022201020222010,
1110111011101110,
4, //37

#GOGOSTART
1110102211101022,
1020102011101022,
1110102211101022,
1020201011202010,

1000201110202010,
2220201110102211,
500000000000000000000000000000000000000000000008,
00001212, //45

1110102211101022,
1020102011101022,
1110102211101022,
1020201011202010,

1000201110202010,
2220201110102211,
500000000000000000000000000008000000000000000000,
#MEASURE 3/4
333, //53
#GOGOEND

#MEASURE 4/4
02222222,
22222111,
10102111,
10102111,

10101121,
20201222,
2000100020102022,
22112015, //61

000000000000000000000000000000000008000000000000,
32232244,
30030000,
0,
0,
0, //67
#END


COURSE:Hard
LEVEL:5
BALLOON:16,9,12
SCOREINIT:870,3530
SCOREDIFF:253

#START
0,
0,
7,
00008111,

10100111,
10100111,
10101121,
20200222, //8

20102102,
02112015,
000000000000000000000000000000000008000000000000,
30030030,

30201120,
1202,
10201120,
1202, //16

10201120,
10112211,
5,
000000000008000000000000000000000000000000000000,

30201120,
1202,
10201120,
1202, //24

10201120,
10112211,
500000000000000000000000000000000008000000700000,
000000000000000000000000000000080000000000000000,

10022001,
10022021,
10022001,
10011021,

10022001,
10022021,
12211221,
9009,
8, //37

#GOGOSTART
10101110,
11101011,
10101110,
11101011,

10201121,
11211121,
500000000000000000000000000000000000000000000008,
00001212, //45

10101110,
11101011,
10101110,
11101011,

10201121,
11211121,
500000000000000000000000000008000000000000000000,
#MEASURE 3/4
333, //53
#GOGOEND

#MEASURE 4/4
02222222,
22222111,
10100111,
10100111,

10101121,
20200222,
20102102,
02112015, //61

000000000000000000000000000000000008000000000000,
30032222,
30030000,
0,
0,
0, //67
#END


COURSE:Normal
LEVEL:4
BALLOON:14,6,9,9
SCOREINIT:1210,6780
SCOREDIFF:490

#START
0,
0,
7,
08,

1100,
1100,
1115,
000000000000000008000000000000000000000000000000, //8

2220,
1115,
000000000000000000000000000000000008000000000000,
30030000,

1011,
1,
1011,
1, //16

1011,
1222,
5,
000000000008000000000000000000000000000000000000,

1011,
1,
1011,
1, //24

1011,
1222,
500000000000000000000000000000000008000000700000,
000000000000000000000000000000080000000000000000,

10022000,
10022000,
10022000,
10022020,

10022000,
10022000,
1111,
9009,
8, //37

#GOGOSTART
1110,
10101011,
1110,
10101011,

1202,
1202,
500000000000000000000000000000000000000000000008,
0, //45

1110,
10101011,
1110,
10101011,

1202,
1202,
500000000000000000000000000008000000000000000000,
#MEASURE 3/4
333, //53
#GOGOEND

#MEASURE 4/4
9009,
8,
1100,
1100,

1115,
000000000000000008000000000000000000000000000000,
2220,
1115, //61

000000000000000000000000000000000008000000000000,
40040000,
30030000,
0,
0,
0, //67
#END


COURSE:Easy
LEVEL:3
BALLOON:5,6,6
SCOREINIT:910,8270
SCOREDIFF:445

#START
0,
0,
0,
0,

1100,
1100,
1115,
000000000000000008000000000000000000000000000000, //8

11,
1115,
000000000000000000000000000008000000000000000000,
30030000,

11,
1,
11,
1, //16

11,
1111,
5,
000000000008000000000000000000000000000000000000,

11,
1,
11,
1, //24

11,
1111,
500000000000000000000000000000000008000000700000,
000000000000000000000000000000080000000000000000,

12,
12,
12,
1011,

12,
12,
1111,
9009,
8, //37

#GOGOSTART
1110,
1110,
1110,
1111,

0202,
0202,
500000000000000000000000000000000000000000000008,
0, //45

1110,
1110,
1110,
1111,

0202,
0202,
500000000000000000000000000008000000000000000000,
#MEASURE 3/4
333, //53
#GOGOEND

#MEASURE 4/4
9009,
8,
1100,
1100,

1115,
000000000000000008000000000000000000000000000000,
11,
1115, //61

000000000000000000000000000008000000000000000000,
40040000,
30030000,
0,
0,
0, //67
#END