//TJADB Project
TITLE:Scar
TITLEJA:スカー
SUBTITLE:--
SUBTITLEJA:
BPM:193
WAVE:Scar.ogg
OFFSET:-1.553
DEMOSTART:54.346


COURSE:Oni
LEVEL:8
BALLOON:
SCOREINIT:680,2200
SCOREDIFF:193

#START
0,
2,
2,

3,
0,
2,
2000110
#GOGOSTART
3, //7

0010102210102000,
1010202110102010,
0010102210102010,
1010201022102030,

0010102210102000,
1010202110102010,
0010102210102010,
1010202210202010, //15
#GOGOEND

0010200110102000,
11201121,
1010200110102000,
1010200012202010,

2010200110102010,
2010200110102010,
0010200110102000,
1010200110202010, //23

0010200110102010,
1010200110102010,
1010200110102000,
1010200011202010,

0010200110201000,
1010200110201010,
1010200110201000,
3020102210202010, //31

20022000,
2222,
20022002,
2,

6,
000000000000000000000000000000000008000000000000,
11212212,
0011101020102000,

10002002,
2222,
1210,
22000000, //43

#GOGOSTART
1011202110102000,
1010201022102011,
1011202110102000,
1010201210202011,

1010201210202011,
1010201210202010,
500000000000000000000000000000000000000000000008,
0000200010222010, //51

1011202110102000,
1010201022102011,
1011202110102000,
1010201210202011,

1010201210202011,
1010201210202011,
11212150,
000000000000000000000000000000000008000000000000, //59

3000002010102011,
1010002010102022,
1000002010102011,
1010201120102010,

3000002010102011,
11021122,
#GOGOEND
500000000000000000000008000000000200000000200000,
40102011101000
#GOGOSTART
30, //67

0010102210102020,
1010202110102010,
0010102210102010,
102210
#GOGOEND
1000000000,
0, //72
#END


COURSE:Hard
LEVEL:5
BALLOON:
SCOREINIT:750,3320
SCOREDIFF:228

#START
0,
2,
2,

3,
0,
2,
2000110
#GOGOSTART
3, //7

01101120,
11101121,
01101120,
12120003,

01101120,
11101121,
01101120,
12120001, //15
#GOGOEND

01101020,
10201120,
10101120,
11201110,

10201011,
1211,
0112,
11102201, //23

01101020,
10201120,
10101120,
11201101,

01101210,
10101210,
10202220,
3232, //31

20022000,
22,
20022002,
2,

6,
000000000000000000000000000000000008000000000000,
11011011,
0121,

12,
2220,
12,
2, //43

#GOGOSTART
10121120,
11201022,
10121120,
11202210,

10201121,
10201121,
500000000000000000000000000000000000000000000008,
00001022, //51

10121120,
11201022,
10121120,
11202210,

10201121,
10201121,
10211050,
000000000000000000000000000000000008000000000000, //59

30021120,
11022020,
10021120,
11022110,

30021120,
11022010,
#GOGOEND
500000000000000000000000000000000000000008000000,
0000110
#GOGOSTART
3, //67

01101120,
11101121,
00101121,
121
#GOGOEND
10000,
0, //72
#END


COURSE:Normal
LEVEL:5
BALLOON:
SCOREINIT:890,5050
SCOREDIFF:328

#START
0,
2,
2,

3,
0,
2,
2000000
#GOGOSTART
3, //7

0111,
10201001,
0111,
10110003,

0111,
10201001,
0111,
10110001, //15
#GOGOEND

0110,
11,
1101,
0,

1210,
1211,
0112,
10100001, //23

0110,
11,
1101,
00000001,

0112,
1012,
12,
33, //31

2,
2,
22,
2,

6,
000000000000000000000000000000000008000000000000,
3021,
0121,

1,
2,
12,
2, //43

#GOGOSTART
10111010,
11102020,
10111010,
10002220,

10001110,
10002220,
500000000000000000000000000000000000000000000008,
01, //51

10111010,
11102020,
10111010,
10002220,

10001110,
10002220,
10011050,
000000000000000000000000000000000008000000000000, //59

30011020,
11022020,
10011020,
11022020,

30011020,
11022010,
#GOGOEND
500000000000000000000000000000000000000008000000,
0000000
#GOGOSTART
3, //67

0111,
10201001,
0111,
101
#GOGOEND
10000,
0, //72
#END


COURSE:Easy
LEVEL:3
BALLOON:
SCOREINIT:920,9000
SCOREDIFF:608

#START
0,
2,
2,

3,
0,
2,
2000000
#GOGOSTART
3, //7

01,
10000001,
01,
10100003,

01,
10000001,
01,
10100001, //15
#GOGOEND

01,
1,
1101,
0,

11,
1011,
01,
10100001, //23

01,
1,
1101,
00000001,

02,
12,
1,
33, //31

2,
0,
2,
0,

6,
000000000000000000000000000008000000000000000000,
3001,
01,

2,
0,
2,
2, //43

#GOGOSTART
1110,
11,
1110,
11,

21,
21,
500000000000000000000000000000000000000000000008,
01, //51

1110,
11,
1110,
11,

21,
21,
2015,
000000000000000000000000000008000000000000000000, //59

32,
02,
12,
02,

32,
02,
#GOGOEND
500000000000000000000000000000000000000008000000,
0000000
#GOGOSTART
3, //67

01,
10000001,
01,
101
#GOGOEND
00000,
0, //72
#END