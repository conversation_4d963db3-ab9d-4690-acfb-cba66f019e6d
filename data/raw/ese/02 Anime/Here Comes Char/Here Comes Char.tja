//TJADB Project
TITLE:Here Comes Char
TITLEJA:シャアが来る
SUBTITLE:--Mobile Suit Gundam
SUBTITLEJA:「機動戦士ガンダム」より
BPM:176
WAVE:Here Comes Char.ogg
OFFSET:-2.865
DEMOSTART:33.197

COURSE:Edit
LEVEL:8
BALLOON:14,20
SCOREINIT:660,1980
SCOREDIFF:180

#START
1,
1,
11,
11,

#SCROLL 3
508
#SCROLL 1
000100000,
11,
#SCROLL 3
608
#SCROLL 1
000100000,
11,
300000000000000000000111,
1430, //10

3113,
10301400,
3113,
10301400,

1010001120100020,
0010102220100010,
1011101020100070,
000000000000000000000000000000000000080000000000, //18

#SCROLL 3
3
#SCROLL 1
0012212,
10212010,
#SCROLL 3
3
#SCROLL 1
0012212,
1000201120300000,

#GOGOSTART
12012001,
1011121120000010,
1020001020001011,
1011121120000010,
11121222,
02221000, //28

#SCROLL 3
3
#SCROLL 1
22
#SCROLL 3
3
#SCROLL 1
22
#SCROLL 3
3
#SCROLL 1
2,
#GOGOEND
12212210,
#GOGOSTART
#SCROLL 3
3
#SCROLL 1
22
#SCROLL 3
3
#SCROLL 1
22
#SCROLL 3
3
#SCROLL 1
2,
1020200220300000,

12012001,
1011121120000010,
12212212,
1011121120000010, //36

12012001,
1022121120000010,
12212212,
1020001020111000,

#SCROLL 3
3
#SCROLL 1
22
#SCROLL 3
3
#SCROLL 1
22
#SCROLL 3
3
#SCROLL 1
2,
#GOGOEND
12212210,
#GOGOSTART
#SCROLL 3
3
#SCROLL 1
22
#SCROLL 3
3
#SCROLL 1
22
#SCROLL 3
3
#SCROLL 1
2,
01113333, //44
#GOGOEND

3000201120000010,
1002101020000010,
1002101020000010,
1011121120000010,

1022201020000010,
1002101020021010,
2011101020000010,
1011121120002010, //52

1002201020022010,
1002201020022010,
1001101020001022,
1010201010221010,

2010102210102010,
1011121120021020,
100000000000000000000111,
1430, //60

#SCROLL 3
000000000000500000000000000000000008000000000000,
#SCROLL 1
11012011,
1210001020001020,
0020201120201010,

1,
2007,
0,
000008000000300000000000, //68

1001101020111020,
1020201120101020,
1020201120201020,
1022201010112220,

1011201020021020,
1022201020021020,
1021102020011020,
1011121150000800,
#MEASURE 8/4
3300000000000000, //77
#END


COURSE:Oni
LEVEL:7
BALLOON:11,11
SCOREINIT:930,2880
SCOREDIFF:270

#START
#MEASURE 8/4
#SCROLL 0.5
22,
2222,
#SCROLL 1
3
#SCROLL 0.5
222,
#SCROLL 1
3
#SCROLL 0.5
222,
70081200, //5

1011102010112120,
1011102010112120,
2220120221121120,
500000000000000000000000000000000000000000000000000000000000000000000000000000000008000000000000, //9

#SCROLL 1
3
#SCROLL 0.5
002202212021020,
#SCROLL 1
3
#SCROLL 0.5
002202212201200,
#GOGOSTART
1011202011002020,
100000100000000000100000200000000000100000500000000000000000000000000000000000000008000000000000,
1111120201112000, //14

#SCROLL 1
30030030
#GOGOEND
#SCROLL 0.5
01102200,
#GOGOSTART
#SCROLL 1
30030030
#SCROLL 0.5
01102200,
1011202011002020,
1112112210002000, //18

1011202011002020,
1111112010220000,
#SCROLL 1.5
30030030
#GOGOEND
#SCROLL 0.5
01102200,
#GOGOSTART
#SCROLL 1.5
30030030
#SCROLL 0.5
01112222, //22
#GOGOEND

1001102010112020,
1001101020112020,
1001102010112020,
1021112011212120, //26

1021102010211020,
1011112111112120,
1011112111212200,
70081200, //30

1011202011002020,
1011201110102010,
2020202022002020,
500000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000, //34

1011011211210120,
1112112101221020,
1121201012212020,
1201221211212020,
1100000000000000, //39
#END


COURSE:Hard
LEVEL:5
BALLOON:9,9
SCOREINIT:940,3900
SCOREDIFF:305

#START
#MEASURE 8/4
#SCROLL 0.5
22,
2222,
#SCROLL 1
3
#SCROLL 0.5
0202020,
#SCROLL 1
3
#SCROLL 0.5
0202020,
70081200,

1000101010001110,
1000101010001110,
1010100020101110,
500000000000000000000000000000000000000000000000000000000000000000000000000000000008000000000000, //9

#SCROLL 1
3
#SCROLL 0.5
002200020022000,
#SCROLL 1
3
#SCROLL 0.5
002200020001100,
#GOGOSTART
1011101020002020,
100000000000000000100000100000000000500000000000000000000000000000000000000000000008000000000000,
1011100010102000,

#SCROLL 1
30030030
#GOGOEND
#SCROLL 0.5
01102200,
#GOGOSTART
#SCROLL 1
30030030
#SCROLL 0.5
01102200,
1011101010002020,
100000000000100000100000100000000000500000000000000000000000000000000008000000000000000000000000, //18

1011101010002020,
100000000000100000100000100000000000500000000000000000000000000000000008000000000000000000000000,
#SCROLL 1.5
30030030
#GOGOEND
#SCROLL 0.5
01102200,
#GOGOSTART
#SCROLL 1.5
30030030
#SCROLL 0.5
01102020, //22
#GOGOEND

1001101010011010,
1001101020011010,
1001101010011010,
1001101020011010,

1001101020022020,
1001101020022020,
1011101010101100,
70081200, //30

1011101010002020,
1011101010002000,
20222022,
500000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000,

1001101010111000,
1001101020222000,
1011100020222000,
1001101020022020,
3, //39
#END


COURSE:Normal
LEVEL:4
BALLOON:6,14,6,14
SCOREINIT:1180,6610
SCOREDIFF:545

#START
#MEASURE 8/4
#SCROLL 0.5
11,
1110,
#SCROLL 1
3
#SCROLL 0.5
0101010,
#SCROLL 1
3
#SCROLL 0.5
0101010,
70081100,

10101011,
10101011,
10200110,
500000000000000000000000000000000000000000000000000000000000000000000000000000000008000000000000, //9

#SCROLL 1
3
#SCROLL 0.5
000000020000000,
#SCROLL 1
3
#SCROLL 0.5
000000020000000,
#GOGOSTART
10111020,
70000008,
11100220,

#SCROLL 1
30030030
#GOGOEND
#SCROLL 0.5
00002000,
#GOGOSTART
#SCROLL 1
30030030
#SCROLL 0.5
00002000,
10111020,
500000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000, //18

10111020,
500000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000,
#SCROLL 1.5
30030030
#GOGOEND
#SCROLL 0.5
00002000,
#GOGOSTART
#SCROLL 1.5
30030030
#SCROLL 0.5
00002000, //22
#GOGOEND

1001000010010000,
1001000020020000,
1001000010010000,
1001000020020000,

1001000010020000,
1001000010020000,
1001000010020000,
70081100, //30

10111020,
70000008,
1110,
500000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000,

10201110,
10102220,
10201110,
10102220,
3, //39
#END


COURSE:Easy
LEVEL:2
BALLOON:8,8,8,8
SCOREINIT:1100,10580
SCOREDIFF:840

#START
#MEASURE 8/4
#SCROLL 0.5
1,
1110,
#SCROLL 1
3
#SCROLL 0.5
0001000,
#SCROLL 1
3
#SCROLL 0.5
0001000,
70000800,

1110,
1110,
2222,
500000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000, //9

#SCROLL 1
3
#SCROLL 0.5
000000020000000,
#SCROLL 1
3
#SCROLL 0.5
000000020000000,
#GOGOSTART
1110,
70000800,
1100,

#SCROLL 1
30000030
#GOGOEND
#SCROLL 0.5
00000000,
#GOGOSTART
#SCROLL 1
30000030
#SCROLL 0.5
00000000,
1111,
500000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000, //18

1111,
500000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000,
#SCROLL 1.5
30000030
#GOGOEND
#SCROLL 0.5
00000000,
#GOGOSTART
#SCROLL 1.5
30000030
#SCROLL 0.5
00000000, //22
#GOGOEND

1011,
12,
1011,
12,

1111,
11,
12,
70000800, //30

1111,
70000800,
11,
500000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000,

1110,
2220,
1100,
1100,
3, //39
#END
