//TJADB Project
TITLE:[Sou-uchi] Kurenai
TITLEJA:【双打】 紅
SUBTITLE:--X JAPAN
SUBTITLEJA:
BPM:156.3
WAVE:Ku<PERSON>i.ogg
OFFSET:-3.338
DEMOSTART:113.894

COURSE:Edit
LEVEL:10

STYLE:SINGLE
BALLOON:
SCOREINIT:1640
SCOREDIFF:0

#START
0111111111111111,
0020111100100100,
4,
4000000020020020,

02020202,
02020202,
02020202,
0020000022001111, //8

1000110010011101,
1000110010011101,
1000110010011101,
1000030040101040,

3000110010010100,
1000110010010100,
1001110010011100,
1000110011010100, //16

02020202,
02020202,
02020202,
0000002011112200,

3001110010011100,
1001110111004000,
4001110010011100,
41144000, //24

3001110010011100,
1001110111004000,
4001110010011100, //27

4,
4000000020020020,
0111111111111111,
0020111100100100,

02020202,
02020202,
02020202,
02020202, //35

3000110010010100,
1000110010010100,
1001110010011100,
3011110200000022, 

02020202,
02020204,
02020202,
0104010100001111, //43

02020202,
02020204,
02020200, //46

2211221122111120,
0,
2211221122111120,
0,

21212112,
0102010201020100,
21212112,
20221030, //54

0002021112020000,
30200204, //56

0121112111211121,
1121112111211121,
0111211121110121,
0111011211011210,

4,
0,
4002,
20200200, //64

3001110110001100,
1000110010011100,
1001110110001100,
3000000022111040,

02020202,
02020202,
02020202,
0022111000000040, //72

#GOGOSTART
3001110110010100,
1001110110010100,
1101110011011100,
1001110111001100,

1001110110010100,
1001110110010100,
1101110011011100,
1111222200000011, //80

02020202,
02020202,
02020202,
0020002000200022,

02020202,
02020202,
02020202, //87

0111111121121121,
0111111101101100,
4,
4000000020020020,

1110200000201110,
0002021112020000,
3004020110203010,
#GOGOEND
3, //95

0,
0,
0,
0, //99
#END


STYLE:DOUBLE
BALLOON:
SCOREINIT:1640
SCOREDIFF:0

#START P1
0111111111111111,
0020111100100100,
4,
4000000020020020,

02020202,
02020202,
02020202,
0020000022001111, //8

1000110010011101,
1000110010011101,
1000110010011101,
1000030040101040,

3000110010010100,
1000110010010100,
1001110010011100,
1000110011010100, //16

02020202,
02020202,
02020202,
0000002011112200,

3001110010011100,
1001110111004000,
4001110010011100,
41144000, //24

3001110010011100,
1001110111004000,
4001110010011100, //27

4,
4000000020020020,
0111111111111111,
0020111100100100,

02020202,
02020202,
02020202,
02020202, //35

3000110010010100,
1000110010010100,
1001110010011100,
3011110200000022, 

02020202,
02020204,
02020202,
0104010100001111, //43

02020202,
02020204,
02020200, //46

2211221122111120,
0,
2211221122111120,
0,

21212112,
0102010201020100,
21212112,
20221030, //54

0002021112020000,
30200204, //56

0121112111211121,
1121112111211121,
0111211121110121,
0111011211011210,

4,
0,
4002,
20200200, //64

3001110110001100,
1000110010011100,
1001110110001100,
3000000022111040,

02020202,
02020202,
02020202,
0022111000000040, //72

#GOGOSTART
3001110110010100,
1001110110010100,
1101110011011100,
1001110111001100,

1001110110010100,
1001110110010100,
1101110011011100,
1111222200000011, //80

02020202,
02020202,
02020202,
0020002000200022,

02020202,
02020202,
02020202, //87

0111111121121121,
0111111101101100,
4,
4000000020020020,

1110200000201110,
0002021112020000,
3004020110203010,
#GOGOEND
3, //95

0,
0,
0,
0, //99
#END


BALLOON:
SCOREINIT:1640
SCOREDIFF:0

#START P2
4,
4000000020020020,
0111111111111111,
0020111100100100,

3000110011010100,
1000110011010100,
1000110011010100,
1000111100220000, //8

02020202,
02020202,
02020202,
0040101000030040,

02020202,
02020202,
02020202,
02020202, //16

3000110010010100,
1000110010010100,
1001110010011100,
3011110200000022,

02020202,
02020204,
02020202,
0104010100001111, //24

02020202,
02020204,
02020202, //27

0111111111111111,
0020111100100100,
4,
4000000020020020,

3000110010010100,
1000110010010100,
1001110010011100,
1000110011010100, //35

02020202,
02020202,
02020202,
0000002011112200, 

3001110010011100,
1001110111004000,
4001110010011100,
41144000, //43

3001110010011100,
1001110111004000,
4001110111012040, //46

0,
1122112211221110,
0,
1122112211221110,

0201020102010100,
12121211,
0201020102010100,
20221030, //54

1110200000201110,
0011001111004000, //56

4,
0,
4002,
20200200, 

0121112111211121,
1121112111211121,
0111211121110121,
0111011211011211, //64

02020202,
02020202,
02020202,
0022111000000040,

3001110110001100,
1000110010011100,
1001110110001100,
3000000022111040, //72

#GOGOSTART
02020202,
02020202,
02020202,
0020002000200022,

02020202,
02020202,
02020202,
0000000022221100, //80

3001110110010100,
1001110110010100,
1101110011011100,
1001110111001100,

1001110110010100,
1001110110010100,
1101110011011100, //87

4,
4000000020020020,
0111111121121121,
0111111101101100,

0002021112020000,
1110200000201120,
0110203004020201,
#GOGOEND
1, //95

0,
0,
0,
0, //99
#END


COURSE:Oni
LEVEL:9

STYLE:SINGLE
SCOREINIT:2070
SCOREDIFF:0
BALLOON:6

#START
0010101011101110,
0010111000100100,
4,
4000000020020020,

02020202,
02020202,
02020202,
0020000000111110, //8

1000100010001100,
1000100010001100,
1000100010001100,
1000030040000040,

3000100010010100,
1000100010010100,
1000100010001100,
1000100010010100, //16

02020202,
02020202,
02020202,
0000000011111000,

3000110010001100,
1000110010004000,
4000110010001100,
40044000,

3000110010001100,
1000110010004000,
4000110010001100, //27

4,
4000000020020020,
0010101011101110,
0010111000100100,

02020202,
02020202,
02020202,
02020202, //35

3000100010010100,
1000100010010100,
1000100010001100,
3011111000000040,

02020202,
02020204,
02020202,
0004000040001000,

02020202,
02020204,
02020200, //46

2211221122111120,
0,
2211221122111120,
0,

2211221122111120,
0,
2211221122111120,
20221030,

0000002222201010,
70000804, //56

0020112010210120,
1020112010210120,
01010102,
0010000000202220,

4,
0,
4222,
2000202220000000, //64

3000100010001100,
1000100010001100,
1000100010001100,
3000000011101040,

02020202,
02020202,
02020202,
0011101000000040, //72

#GOGOSTART
3000110010010100,
1000110010010100,
1000110010001100,
1000110011010100,

1000110010010100,
1000110010010100,
1000110010001100,
2222111000000000, //80

02020202,
02020202,
02020202,
02020202,

02020202,
02020202,
02020202, //87

0010101020120120, 
0010111000100100,
4,
4000000020020020,

1112221112221110,
0,
11211211,
#GOGOEND
3,

0,
0,
0,
0, //99
#END

STYLE:DOUBLE
SCOREINIT:2070
SCOREDIFF:0
BALLOON:6

#START P1
0010101011101110,
0010111000100100,
4,
4000000020020020,

02020202,
02020202,
02020202,
0020000000111110, //8

1000100010001100,
1000100010001100,
1000100010001100,
1000030040000040,

3000100010010100,
1000100010010100,
1000100010001100,
1000100010010100, //16

02020202,
02020202,
02020202,
0000000011111000,

3000110010001100,
1000110010004000,
4000110010001100,
40044000,

3000110010001100,
1000110010004000,
4000110010001100, //27

4,
4000000020020020,
0010101011101110,
0010111000100100,

02020202,
02020202,
02020202,
02020202, //35

3000100010010100,
1000100010010100,
1000100010001100,
3011111000000040,

02020202,
02020204,
02020202,
0004000040001000,

02020202,
02020204,
02020200, //46

2211221122111120,
0,
2211221122111120,
0,

2211221122111120,
0,
2211221122111120,
20221030,

0000002222201010,
70000804, //56

0020112010210120,
1020112010210120,
01010102,
0010000000202220,

4,
0,
4222,
2000202220000000, //64

3000100010001100,
1000100010001100,
1000100010001100,
3000000011101040,

02020202,
02020202,
02020202,
0011101000000040, //72

#GOGOSTART
3000110010010100,
1000110010010100,
1000110010001100,
1000110011010100,

1000110010010100,
1000110010010100,
1000110010001100,
2222111000000000, //80

02020202,
02020202,
02020202,
02020202,

02020202,
02020202,
02020202, //87

0010101020120120, 
0010111000100100,
4,
4000000020020020,

1112221112221110,
0,
11211211,
#GOGOEND
3,

0,
0,
0,
0, //99
#END

SCOREINIT:2070
SCOREDIFF:0
BALLOON:6

#START P2
4,
4000000020020020,
0010101011101110,
0010111000100100,

3000100010010100,
1000100010010100,
1000100010010100,
1000111110000000, //8

02020202,
02020202,
02020202,
0040000000030040,

02020202,
02020202,
02020202,
02020202, //16

3000100010010100,
1000100010010100,
1000100010001100,
3011111000000040,

02020202,
02020204,
02020202,
0004000040001000,

02020202,
02020204,
02020202, //27

0010101011101110,
0010111000100100,
4,
4000000020020020,

3000100010010100,
1000100010010100,
1000100010001100,
1000100010010100, //35

02020202,
02020202,
02020202,
0000000011111000,

3000110010001100,
1000110010004000,
4000110010001100,
40044000,

3000110010001100,
1000110010004000,
4000110010001000, //46

0,
1122112211221110,
0,
1122112211221110,

0,
1122112211221110,
0,
20221030,

1111100000001010,
70000840, //56

4,
0,
4222,
2000202220000000,

0020112010210120,
1020112010210120,
01010102,
0010000000202220, //64

02020202,
02020202,
02020202,
0011101000000040,

3000100010001100,
1000100010001100,
1000100010001100,
3000000011101040, //72

#GOGOSTART
02020202,
02020202,
02020202,
02020202,

02020202,
02020202,
02020202,
0000000011112220, //80

3000110010010100,
1000110010010100,
1000110010001100,
1000110011010100,

1000110010010100,
1000110010010100,
1000110010001100, //87

4,
4000000020020020,
0010101020120120,
0010111000100100,

0,
1112221112221120,
0102020102020101,
#GOGOEND
1,

0,
0,
0,
0, //99
#END


COURSE:Hard
LEVEL:7

STYLE:SINGLE
SCOREINIT:2830
SCOREDIFF:0
BALLOON:4

#START
0000101010101110,
00110000,
4,
4000000020020020,

02020202,
02020202,
02020202,
00001110,

1111,
1111,
1111,
1000030040000040,

3000100010010000,
1000100010010000,
1111,
1000100010010000, //16

02020002,
02020002,
00020002,
0000000011103000,

3111,
10000044,
4111,
40044000,

3111,
10000044,
4111, //27

4,
4000000020020020,
0000101010101110,
00110000,

0020002000000020,
0020002000000020,
0000002000000020,
0000002000000020,

3000100010010000,
1000100010010000,
1111,
3011100000000040,

00020202,
0000111010000000,
00020202,
0004000040001000,

00020202,
0000111010000000,
00020200, //46

1010111020202020,
0,
1010111020202020,
0,

1010111022202020,
0,
1010111022202020,
20221030,

0000001111001010,
70000804, //56

0000102010010020,
1020102010010020,
0000001000100000,
0000000000002000,

4,
0,
4222,
2000202220000000,

3111,
1011,
1111,
3000000010101040,

02020202,
02020000,
02020202,
0011100000000000, //72

#GOGOSTART
3000100010010000,
1000100010010000,
1111,
1000100010010100,

1000100010010000,
1000100010010000,
1111,
2220202000000000,

02020202,
02020202,
00020002,
00020202,

02020202,
02020202,
00020200, //87

0000101020120120,
0000101000100100,
4,
4000000020020020,

1111002222001000,
0,
1111000000001020,
#GOGOEND
3,

0,
0,
0,
0, //99
#END

STYLE:DOUBLE
SCOREINIT:2830
SCOREDIFF:0
BALLOON:4

#START P1
0000101010101110,
00110000,
4,
4000000020020020,

02020202,
02020202,
02020202,
00001110,

1111,
1111,
1111,
1000030040000040,

3000100010010000,
1000100010010000,
1111,
1000100010010000, //16

02020002,
02020002,
00020002,
0000000011103000,

3111,
10000044,
4111,
40044000,

3111,
10000044,
4111, //27

4,
4000000020020020,
0000101010101110,
00110000,

0020002000000020,
0020002000000020,
0000002000000020,
0000002000000020,

3000100010010000,
1000100010010000,
1111,
3011100000000040,

00020202,
0000111010000000,
00020202,
0004000040001000,

00020202,
0000111010000000,
00020200, //46

1010111020202020,
0,
1010111020202020,
0,

1010111022202020,
0,
1010111022202020,
20221030,

0000001111001010,
70000804, //56

0000102010010020,
1020102010010020,
0000001000100000,
0000000000002000,

4,
0,
4222,
2000202220000000,

3111,
1011,
1111,
3000000010101040,

02020202,
02020000,
02020202,
0011100000000000, //72

#GOGOSTART
3000100010010000,
1000100010010000,
1111,
1000100010010100,

1000100010010000,
1000100010010000,
1111,
2220202000000000,

02020202,
02020202,
00020002,
00020202,

02020202,
02020202,
00020200, //87

0000101020120120,
0000101000100100,
4,
4000000020020020,

1111002222001000,
0,
1111000000001020,
#GOGOEND
3,

0,
0,
0,
0, //99
#END

SCOREINIT:2830
SCOREDIFF:0
BALLOON:4

#START P2
4,
4000000020020020,
0000101010101110,
00110000,

3000100010010000,
1000100010010000,
1000100010010000,
1000222000000000,

02020000,
02020000,
02020000,
0040000000030040,

02020002,
02020002,
00020002,
00020002, //16

3000100010010000,
1000100010010000,
1111,
3011100000000040,

00020202,
0000111010000000,
00020202,
0004000040001000,

00020202,
0000111010000000,
00020200, //27

0000101010101110,
0000101000000000,
4,
4000000020020020,

3000100010010000,
1000100010010000,
1111,
1000100010010000,

0020002000000020,
0020002000000020,
00020002,
0000000011103000,

3111,
10000044,
4111,
40044000,

3111,
10000044,
4111, //46

0,
1010111020202020,
0,
1010111020202020,

0,
1010111022202020,
0,
20221030,

1111000000001010,
7084, //56

4,
0,
4222,
2000202220000000,

0000102010010020,
1020102010010020,
00010100,
0002,

02020202,
02020000,
02020202,
0011100000000000,

3111,
1011,
1111,
30001114, //72

#GOGOSTART
02020202,
02020202,
00020002,
00020202,

02020202,
02020202,
00020200,
0000000011101010,

3000100010010000,
1000100010010000,
1111,
1000100010010100,

1000100010010000,
1000100010010000,
1111, //87

4,
4000000020020020,
0000101020120120,
0000101000100100,

0,
1111002222001000,
0000002222001020,
#GOGOEND
3,

0,
0,
0,
0, //99
#END


COURSE:Normal
LEVEL:5

STYLE:SINGLE
SCOREINIT:4030
SCOREDIFF:0
BALLOON:3

#START
00111110,
00110000,
4,
42,

00020000,
00020000,
00020000,
00001110,

1111,
1111,
1111,
1000040040000000,

3000100010010000,
1000100010010000,
1111,
1000100010010000, //16

00020000,
00020000,
00020000,
0,

3111,
10000044,
4111,
40044000,

3111,
10000044,
4111, //27

4,
42,
00111110,
00110000,

00020000,
00020000,
00020000,
00020000,

3000100010010000,
1000100010010000,
1111,
500000000000000000000000000000000008000000000000,

00000202,
00111000,
00020200,
0004000040000000,

00000202,
00111000,
00020200, //46

11112220,
0,
11112220,
0,

11112222,
0,
11112222,
20111030,

0000001001001010,
70008004, //56

0000100010010010,
1000100010010010,
00010100,
00002220,

4,
0,
4222,
2200,

3111,
11,
1111,
500000000000000000000008000000000000000000000000,

02020202,
02020000,
02020202,
0, //72

#GOGOSTART
3000100010010000,
1000100010010000,
1111,
1110,

1000100010010000,
1000100010010000,
1111,
1,

00020000,
00020000,
00020002,
00020000,

00020000,
00020000,
00020000, //87

0000101010010010,
00110000,
4,
42,

500000000008000000500000000008000000100000000000,
0,
500000000008000000000000000000000000100000100000,
#GOGOEND
3,

0,
0,
0,
0, //99
#END

STYLE:DOUBLE
SCOREINIT:4030
SCOREDIFF:0
BALLOON:3

#START P1
00111110,
00110000,
4,
42,

00020000,
00020000,
00020000,
00001110,

1111,
1111,
1111,
1000040040000000,

3000100010010000,
1000100010010000,
1111,
1000100010010000, //16

00020000,
00020000,
00020000,
0,

3111,
10000044,
4111,
40044000,

3111,
10000044,
4111, //27

4,
42,
00111110,
00110000,

00020000,
00020000,
00020000,
00020000,

3000100010010000,
1000100010010000,
1111,
500000000000000000000000000000000008000000000000,

00000202,
00111000,
00020200,
0004000040000000,

00000202,
00111000,
00020200, //46

11112220,
0,
11112220,
0,

11112222,
0,
11112222,
20111030,

0000001001001010,
70008004, //56

0000100010010010,
1000100010010010,
00010100,
00002220,

4,
0,
4222,
2200,

3111,
11,
1111,
500000000000000000000008000000000000000000000000,

02020202,
02020000,
02020202,
0, //72

#GOGOSTART
3000100010010000,
1000100010010000,
1111,
1110,

1000100010010000,
1000100010010000,
1111,
1,

00020000,
00020000,
00020002,
00020000,

00020000,
00020000,
00020000, //87

0000101010010010,
00110000,
4,
42,

500000000008000000500000000008000000100000000000,
0,
500000000008000000000000000000000000100000100000,
#GOGOEND
3,

0,
0,
0,
0, //99
#END

SCOREINIT:4030
SCOREDIFF:0
BALLOON:3

#START P2
4,
42,
00111110,
00110000,

3000100010010000,
1000100010010000,
1000100010010000,
1100,

02020000,
02020000,
02020000,
0040000000040040,

00020000,
00020000,
00020000,
00020000, //16

3000100010010000,
1000100010010000,
1111,
500000000000000000000000000000000008000000000000,

00000202,
00111000,
00020200,
0004000040000000,

00000202,
00111000,
00020200, //27

00111110,
00110000,
4,
42,

3000100010010000,
1000100010010000,
1111,
1000100010010000,

00020000,
00020000,
00020000,
0,

3111,
10000044,
4111,
40044000,

3111,
10000044,
4111, //46

0,
11112220,
0,
11112220,

0,
11112222,
0,
20111030,

1001000000001010,
7084, //56

4,
0,
4222,
2200,

0000100010010010,
1000100010010010,
00010100,
00002220,

02020202,
02020000,
02020202,
0,

3111,
11,
1111,
500000000000000000000008000000000000000000000000, //72

#GOGOSTART
00020000,
00020000,
00020002,
00020000,

00020000,
00020000,
00020000,
00111110,

3000100010010000,
1000100010010000,
1111,
1110,

1000100010010000,
1000100010010000,
1111, //87

4,
42,
0000101010010010,
00110000,

0,
500000000008000000500000000008000000100000000000,
000000000000000000500000000008000000100000100000,
#GOGOEND
3,

0,
0,
0,
0, //99
#END


COURSE:Easy
LEVEL:4

STYLE:SINGLE
SCOREINIT:6080
SCOREDIFF:0
BALLOON:2

#START
0011,
0100,
4,
42,

0,
0,
0,
0,

1110,
1110,
1110,
500000000000000008000000000000000000000000000000,

3110,
1110,
1111,
1110, //16

00020000,
00020000,
00020000,
0,

3111,
1004,
4111,
600000000000000000000008000000000000000000000000,

3111,
1004,
4111, //27

4,
42,
0011,
0100,

00020000,
00020000,
00020000,
0,

3110,
1110,
1111,
500000000000000000000000000008000000000000000000,

00000200,
0,
00000200,
0,

00000200,
0,
0, //46

10101110,
0,
10101110,
0,

11101110,
0,
11101110,
20111000,

000000000000000000500000000008000000000000000000,
78, //56

0111,
1110,
0,
0,

4,
0,
40202022,
2220,

3111,
1110,
1111,
500000000000000000000008000000000000000000000000,

00000200,
0,
00000200,
0, //72

#GOGOSTART
3110,
1110,
1111,
1110,

1110,
1110,
1111,
500000000000000000000008000000000000000000000000,

00020000,
00020000,
00020000,
0,

00020000,
00020000,
00020000, //87

0110,
00110000,
4,
42,

500000000008000000500000000008000000000000000000,
0,
500000000008000000000000000000000000000000000000,
#GOGOEND
3,

0,
0,
0,
0, //99
#END

STYLE:DOUBLE
SCOREINIT:6080
SCOREDIFF:0
BALLOON:2

#START P1
0011,
0100,
4,
42,

0,
0,
0,
0,

1110,
1110,
1110,
500000000000000008000000000000000000000000000000,

3110,
1110,
1111,
1110, //16

00020000,
00020000,
00020000,
0,

3111,
1004,
4111,
600000000000000000000008000000000000000000000000,

3111,
1004,
4111, //27

4,
42,
0011,
0100,

00020000,
00020000,
00020000,
0,

3110,
1110,
1111,
500000000000000000000000000008000000000000000000,

00000200,
0,
00000200,
0,

00000200,
0,
0, //46

10101110,
0,
10101110,
0,

11101110,
0,
11101110,
20111000,

000000000000000000500000000008000000000000000000,
78, //56

0111,
1110,
0,
0,

4,
0,
40202022,
2220,

3111,
1110,
1111,
500000000000000000000008000000000000000000000000,

00000200,
0,
00000200,
0, //72

#GOGOSTART
3110,
1110,
1111,
1110,

1110,
1110,
1111,
500000000000000000000008000000000000000000000000,

00020000,
00020000,
00020000,
0,

00020000,
00020000,
00020000, //87

0110,
00110000,
4,
42,

500000000008000000500000000008000000000000000000,
0,
500000000008000000000000000000000000000000000000,
#GOGOEND
3,

0,
0,
0,
0, //99
#END

SCOREINIT:6080
SCOREDIFF:0
BALLOON:2

#START P2
4,
42,
0011,
0100,

3110,
1110,
1110,
1110,

00020000,
00020000,
00020000,
000000000000000000000000500000000008000000000000,

00020000,
00020000,
00020000,
0, //16

3110,
1110,
1111,
500000000000000000000000000008000000000000000000,

00000200,
0,
00000200,
0,

00000200,
0,
00000200, //27

0011,
0100,
4,
42,

3110,
1110,
1111,
1110,

00020000,
00020000,
00020000,
0,

3111,
1004,
4111,
600000000000000000000008000000000000000000000000,

3111,
1004,
4110, //46

0,
10101110,
0,
10101110,

0,
11101110,
0,
20111000,

500000000008000000000000000000000000000000000000,
78, //56

4,
0,
40202022,
20202000,

0111,
1110,
0,
0,

00000200,
0,
00000200,
0,

3111,
1110,
1111,
500000000000000000000008000000000000000000000000, //72

#GOGOSTART
00020000,
00020000,
00020000,
0,

00020000,
00020000,
00020000,
0,

3110,
1110,
1111,
1110,

1110,
1110,
1111, //87

4,
42,
0110,
00110000,

0,
500000000008000000500000000008000000000000000000,
000000000000000000500000000008000000000000000000,
#GOGOEND
3,

0,
0,
0,
0, //99
#END