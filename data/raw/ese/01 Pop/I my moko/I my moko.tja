//TJADB Project
TITLE:I my moko
TITLEJA:I my moko
SUBTITLE:--<PERSON><PERSON><PERSON>
SUBTITLEJA:桃井はるこ
BPM:140
WAVE:I my moko.ogg
OFFSET:-1.822 
DEMOSTART:5.250

//Shinuchi: 5940/4040/2650/2100

COURSE:Oni
LEVEL:8
BALLOON:
SCOREINIT:700,2140
SCOREDIFF:188

#START
0,
0000000000102022,

1000200010102022,
1010101022201020,
1000200010102022,
1010101022201020,

1000200010102022,
1010101022201020,
1000200010102022,
1010201011102022, //10

11201212,
1010220210201020,
1010200010201022,
1010220212221020,

1010220210201020,
1010220210201020,
1010220210201020,
1010222010102010, //18

01220122,
12020000,
01222122,
03030300,

0010202022101020,
10120210,
0122,
1000102011102010, //26

01220122,
12020000,
01222112,
10120120,

01221022,
10120210,
12210110,
500000000008000000100000100000100000200200100000, //34

1000100012102022,
1020102220201010,
500000000000000000000000000000000000000008000000,
1010202012102022,

500000000008000000200000500000000000000008000000,
1010202212102020,
1022201202102000,
1022201202102000,
0000000000102022, //43

#GOGOSTART
1020102010221020,
1022102212221020,
1020102010221020,
1022102212021020,

1020102010221020,
1022102012221000,
1010201000102022,
1010201022102022, //51

1020102010221020,
1022102212221020,
1020102010221020,
1022102212021020,

1022102210201060,
000000000008000000300000000000100000200000200200,
1022102010221060,
000000000000000008000000200200100000200200200000, //59
#GOGOEND

2000200020100022,
200100200100200100000222,
200000202020200000200200200000100000200000100000,
33330000, //63
#END


COURSE:Hard
LEVEL:6
BALLOON:
SCOREINIT:660,2760
SCOREDIFF:188

#START
0,
00000120,

10201100,
11110120,
10201100,
11110120,

10201100,
11110120,
10201100,
10210120, //10

11201212,
11201212,
11201212,
1010200011102000,

11201212,
11201212,
11201212,
1010222010102010, //18

01210120,
12010000,
01202101,
03030300,

01201120,
10120210,
000000000000500008000000500008000000500008000000,
500000000008000000200000200200200000200000100000, //26

01210101,
12010000,
01201022,
10120010,

01201022,
10120020,
10210210,
500000000008000000100000100000100000200200200000, //34

10201120,
1020102000222020,
10201120,
1020102000222020,

10201120,
1020102000222020,
10210120,
10210120,
00000122, //43

#GOGOSTART
12121121,
0010222010102010,
12121121,
0010222010102010,

12121121,
0010222010102220,
10210120,
10210122, //51

12121121,
0010222010102010,
12121121,
0010222010102010,

12121121,
0010222010102220,
12121121,
0010222010102220, //59
#GOGOEND

30303300,
30303300,
30303300,
11110000, //63
#END


COURSE:Normal
LEVEL:5
BALLOON:
SCOREINIT:760,4310
SCOREDIFF:243

#START
0,
0,

10101100,
10110000,
10101100,
10110000,

10101100,
10110000,
10101100,
0, //10

10101202,
1120,
10101202,
10102220,

10101202,
1120,
10101202,
500000000000000000000000000000000000000008000000, //18

01200120,
10210000,
01201120,
03030300,

01201120,
10010010,
0222,
500000000000000000000000000000000000000008000000, //26

01200120,
10210000,
01201120,
10010010,

01201120,
10210010,
10210110,
500000000000000000000000000000000000000008000000, //34

10101100,
10110000,
10101100,
10110000,

10101100,
10110000,
10210100,
10210100,
0, //43

#GOGOSTART
10101212,
10000222,
10101212,
10000222,

10101212,
10110000,
10210020,
10210222, //51

10101212,
10000222,
10101212,
10000222,

10101212,
10010222,
10101212,
500000000000000000000000000000000000000008000000, //59
#GOGOEND

30303300,
30303300,
30303300,
10110000, //63
#END


COURSE:Easy
LEVEL:3
BALLOON:6,6
SCOREINIT:650,6610
SCOREDIFF:243

#START
0,
0,

1110,
1100,
1110,
1100,

1110,
1100,
1110,
0, //10

1022,
1022,
1022,
1120,

1022,
1022,
1022,
1120, //18

1202,
1202,
1202,
03030300,

1202,
1202,
9009,
8, //26

1202,
1202,
1202,
30030030,

1202,
1202,
9009,
8, //34

1110,
1100,
1110,
1100,

1110,
1100,
10010000,
10010000,
0, //43

#GOGOSTART
1011,
2202,
1011,
2202,

1011,
2202,
10010000,
10010000, //51

1011,
2202,
1011,
2202,

1011,
2202,
1011,
500000000000000000000000000008000000000000000000, //59
#GOGOEND

3330,
3330,
3330,
1100, //63
#END