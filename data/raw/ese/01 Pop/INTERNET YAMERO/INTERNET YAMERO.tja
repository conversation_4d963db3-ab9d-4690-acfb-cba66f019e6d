//TJADB Project
TITLE:INTERNET YAMERO
TITLEJA:INTERNET YAMERO
SUBTITLE:--Aiobahn x kotoko x nyalra / Theme for NEEDY GIRL OVERDOSE
SUBTITLEJA:Aiobahn x kotoko x nyalra / Theme for NEEDY GIRL OVERDOSE
BPM:185
WAVE:INTERNET YAMERO.ogg
OFFSET:-1.056
DEMOSTART:106.782

COURSE:Oni
LEVEL:9
BALLOON:4
SCOREINIT:1020
SCOREDIFF:0

#START
20102212,
12012011,
30223020,
3000300030001111,

1020102010221020,
11221022,
1020102010221020,
1020101010102222, //8

1020102010221020,
11221122,
1011202020121122,
22121040,

10122212,
02120212,
10122212,
02120212, //16

10210121,
11201222,
10211020,
10211120,

10122212,
02120212,
10122212,
02120212, //24

10211020,
10201222,
02021020,
1010201000111010,

20222202,
0,
20222202,
0, //32

1022102210221022,
1022102210221022,
1022102210221022,
1022102210221010,

1022122011221210,
1022121211212000,
1020122011112000,
1120122011221000, //40

1111212011212000,
1111220211221000,
1022122211212000,
1022101011112002,

0020020020020020,
0200200200200200,
2111111121111111,
2111112020202020, //48

#GOGOSTART
33030406,
000000000000000008000000000000200000200000200000,
33040406,
000000000000000008000000000000200000200000200000,

33030406,
000000000000000008000000000000200000200000200000,
33040406,
000000000000000008000000000000200000200000200000, //56

3020112010221020,
1120112110202020,
3020112010221022,
1220102210202020,

3020112010221020,
1120112110202020,
33,
44334343,

300000000000100200200200100000202020100000202020,
100000200000100200200200100000200000100000202020,
100000200000100200200200100000200000100000202020,
100000200000100000202020100000200000100100100100, //68
#GOGOEND

2,
0,
4,
0,

4,
0,
4,
4000000020022020, //76

4222,
20202022,
4222,
2000200020022020,

4020222020222020,
4020222020222020,
30343030,
3000300040002222, //84

#GOGOSTART
1022102210201122,
1022112010201120,
1022102011221120,
1020112030001111,

1022102210201122,
1022112010201122,
1022112211112010,
2220111110104000, //82

1022102210201122,
1022112010201120,
1022102011221120,
1020112010201111,

1022102011112022,
1022112010201122,
1111222211221010,
1110111110104000, //100
#GOGOEND

1222102011212020,
1122121020001020,
1120112011212020,
3040403030403040,

1110222011112020,
1122121020001020,
1120112011212020,
1222122210004000, //108

32323232,
32323232,
32323232,
32323232,

33333333,
33333333,
1111111111111111, //115

//actual branch cond.: all big notes big-perfect -> #M, otherwise #N
#BRANCHSTART p,-1,95
#N
5,
000000000000000000000000000000000000000000000008,
0,
0,
0, //n120

#E
5,
000000000000000000000000000000000000000000000008,
0,
0,
0, //e120

#M
111111111111111111111111,
111111111111111111111111,
700008000000,
0,
0, //m120
#BRANCHEND
#END


COURSE:Hard
LEVEL:7
BALLOON:20
SCOREINIT:1520
SCOREDIFF:0

#START

20102210,
12012011,
3232,
3330,

12121210,
11221020,
12121210,
12111020, //8

12121210,
11221020,
11221110,
22121040,

10201120,
10201120,
10201120,
10201122, //16

1212,
10201120,
1212,
10210120,

10201120,
10201120,
10201120,
10201122, //24

1212,
10201120,
1212,
1000201000111010,

10222202,
0,
20222202,
0, //32

1111,
1111,
12121212,
12121211,

1011100010111000,
1011101010102000,
2020222010102000,
1020102011101000, //40

1110202010202000,
2220200011101000,
1020102011102000,
1010101011107000,

0,
08,
20112011,
20022220, //48

#GOGOSTART
33030406,
000000000000000008000000000000200000200000000000,
33040406,
000000000000000008000000000000200000200000000000,

33030406,
000000000000000008000000000000200000200000000000,
33040406,
000000000000000008000000000000200000200000000000, //56

33021212,
1110100010202000,
33021212,
1110101010202000,

33021212,
1110100010202020,
33,
44443333,

30121010,
10121210,
10121210,
1020100010201110, //68
#GOGOEND

4,
0,
4,
0,

4,
0,
4,
40002022, //76

4222,
2222,
4222,
20202022,

40222022,
40222020,
30333030,
3342, //84

#GOGOSTART
1020102220202011,
12121022,
1020102011101110,
12103011,

1020102220202011,
12121022,
1020102011102010,
22103040, //92

1020102220202011,
12121022,
1020102011101110,
12101011,

1020102220202011,
12121022,
1020102011102010,
22103040, //100
#GOGOEND

11102220,
1010111020001020,
1010101011102000,
12211212,

11201120,
1010111020001020,
1010101011102000,
12121040, //108

12121212,
12121212,
12121212,
12121212,

11111111,
11111111,
1110111011101110, //115

6,
000000000000000000000000000000000000000000000008,
0,
0,
0, //120
#END


COURSE:Normal
LEVEL:5
BALLOON:16
SCOREINIT:2420
SCOREDIFF:0

#START
0,
0,
33,
3330,

1110,
11101000,
1110,
10111000, //8

1110,
11101000,
1111,
22204000,

1012,
10001110,
1012,
10001110, //16

1012,
10001110,
1012,
10202220,

1012,
10201110,
1012,
10201110, //24

1012,
10201110,
1212,
10202220,

20002202,
0,
20002202,
0, //32

1111,
1111,
1111,
1111,

1212,
11101010,
2222,
10101110, //40

1111,
2211,
1212,
11101070,

0,
08,
11,
10022020, //48

#GOGOSTART
33030306,
000000000000000008000000000000000000000000000000,
33030306,
000000000000000008000000000000000000000000000000,

33030306,
000000000000000008000000000000000000000000000000,
33030306,
000000000000000008000000000000000000200000000000, //56

30101110,
11101010,
30101110,
1122,

30101110,
11101010,
33,
44403330,

3111,
10101210,
1111,
10101210, //68
#GOGOEND

4,
0,
4,
0,

4,
0,
4,
4022, //76

42,
22,
42,
22,

4222,
4220,
3344,
3340, //84

#GOGOSTART
10101110,
11101000,
11111010,
11103000,

10101110,
11101020,
11102020,
11103040, //92

10101110,
11101000,
11111010,
11103000,

10102220,
11101020,
11102020,
11103040, //100
#GOGOEND

1122,
11101020,
1112,
10101111,

1212,
11102010,
1112,
22201040, //108

1111,
1111,
1111,
1111,

11101110,
11101110,
11111111, //115

6,
000000000000000000000000000000000000000000000008,
0,
0,
0, //120
#END


COURSE:Easy
LEVEL:3
BALLOON:10
SCOREINIT:4410
SCOREDIFF:0

#START
0,
0,
33,
3330,

11,
1110,
11,
1110, //8

11,
1110,
11,
22,

1,
11,
1,
11, //16

1,
1,
1,
11,

1,
11,
1,
11, //24

1,
1,
1,
11,

1,
0,
1,
0, //32

11,
11,
1110,
1110,

11,
1110,
11,
1110, //40

11,
1110,
11,
1117,

0,
08,
1,
1, //48

#GOGOSTART
30000006,
000000000000000008000000000000000000000000000000,
30000006,
000000000000000008000000000000000000000000000000,

30000006,
000000000000000008000000000000000000000000000000,
30000006,
000000000000000008000000000000000000000000000000, //56

31,
1110,
31,
1110,

31,
1111,
33,
4440,

3110,
1111,
11,
1110, //68
#GOGOEND

4,
0,
4,
0,

4,
0,
4,
42, //76

4,
2,
4,
2,

42,
42,
3333,
4440, //84

#GOGOSTART
1110,
1110,
1111,
1130,

1110,
2220,
1111,
13, //92

1110,
1110,
1111,
1110,

1110,
2220,
1111,
13, //100
#GOGOEND

12,
1110,
1110,
1110,

12,
1110,
1110,
2220, //108

11,
11,
11,
11,

1111,
1111,
500000000000000000000000000000000008000000000000, //115

6,
000000000000000000000000000000000000000000000008,
0,
0,
0, //120
#END