//TJADB Project
TITLE:<PERSON><PERSON>tte mo Umaku Ikanai
TITLEJA:なにやってもうまくいかない
SUBTITLE:--meiyo
SUBTITLEJA:meiyo
BPM:125
WAVE:Nani Yatte mo Umaku Ikanai.ogg
OFFSET:-0.792
DEMOSTART:50.121


COURSE:Oni
LEVEL:8
BALLOON:32,32,85
SCOREINIT:1750
SCOREDIFF:0

#START
7,
0081,

#GOGOSTART
3011112210101012,
0201201101012010,
3011112210102011,
1020101110201000,

3011112210101012,
0201201101122010,
3011112210102000,
10111122
#GOGOEND
10002000, //10

1000200010112010,
1000200010111020,
1011101020111020,
1011102010111010,

2020201010111020,
2011101110101020,
1112,
500000000000000000000000000000000000000008000000, //18

1011201010222010,
1011102010001010,
1011202020111020,
1011102010112010,

1000100020112010,
1122112210101000,
7,
0081, //26

#GOGOSTART
3011112210101012,
0201201101012010,
3011112210102011,
1020101110201000,

3011112210101012,
0201201101122010,
3011112210102000,
100000100100100100200200
#GOGOEND
100000000000500000000008,

#MEASURE 2/4
000000000002200000000000, //35

#MEASURE 4/4
1000200010112011,
1010201110002000,
1011102010111020,
1011101020222010,

1000101202012010,
1011102010112020,
1011102020112010,
1011201030002000, //43

1011201010112010,
1011102020001000,
1011201020112010,
1011102010002000,

7,
0,
0,
0081, //51

#GOGOSTART
3011112210201012,
0111201101112010,
3011112210102011,
1020101110201020,

3011112210101012,
0111201101122010,
3011112210201020,
1011112210201020, //59

3011221120201012,
0111201101112010,
3011221120201011,
1020101122101020,

3011221120201012,
0111201101112010,
3011221120201000,
10112211
#GOGOEND
20000000, //67

0003,
0,
0, //70
#END


COURSE:Hard
LEVEL:5
BALLOON:26,26,52
SCOREINIT:2630
SCOREDIFF:0

#START
7,
08,

#GOGOSTART
3011101010001011,
0100101101002000,
3011101010002000,
1000101100201000,

3011101010001011,
0100101101002000,
3011101010002000,
10111010
#GOGOEND
10002000, //10

10201110,
10201110,
10112022,
11121010,

22201110,
1011101110101000,
1112,
500000000000000000000000000000000000000008000000, //18

11101120,
11121020,
11101120,
11121020,

1000100020111010,
500000000000000000000008000000200000200000000000,
7,
0081, //26

#GOGOSTART
3011101010001011,
0100101101002000,
3011101010002000,
1000101100201000,

3011101010001011,
0100101101002000,
3011101010002000,
100000100100100000100000
#GOGOEND
100000000000500000000008,

#MEASURE 2/4
02, //35

#MEASURE 4/4
1202,
11221020,
11221122,
11121122,

100000000000500000000000000000000000000008000000,
11121020,
11121112,
11103020, //43

11121112,
11122000,
11121122,
11221020,

9,
0,
000000000009000000000000000000000000000000000008,
0, //51

#GOGOSTART
3011101010001011,
0100101101002000,
3011101010002000,
1000101100201000,

3011101010001011,
0100101101002000,
3011101010002000,
1011101010001000, //59

3011102010001011,
0100101101002000,
3011102010002000,
1000101100201000,

3011102010001011,
0100101101002000,
3011102010001000,
10111020
#GOGOEND
10000000, //67

0003,
0,
0, //70
#END


COURSE:Normal
LEVEL:4
BALLOON:17,17,34
SCOREINIT:4000
SCOREDIFF:0

#START
7,
08,

#GOGOSTART
31101020,
20111020,
31101010,
2021,

31101020,
20111020,
31101020,
1110
#GOGOEND
1000, //10

1011,
1011,
1122,
11101000,

20201110,
20201110,
1110,
500000000000000000000000000000000000000008000000, //18

10101110,
1110,
10101110,
1120,

1120,
1122,
7,
0081, //26

#GOGOSTART
31101020,
20111020,
31101010,
2021,

31101020,
20111020,
31101020,
1110
#GOGOEND
1000,

#MEASURE 2/4
2, //35

#MEASURE 4/4
1,
2001,
10101110,
1112,

1122,
1112,
1122,
11103000, //43

11102000,
11101000,
1122,
11101020,

9,
0,
000000000009000000000000000000000000000000000008,
0, //51

#GOGOSTART
31101020,
20111020,
31101010,
2021,

31101020,
20111020,
31101010,
11102020, //59

31102020,
20111020,
31101010,
2221,

31102020,
20111020,
31101020,
1110
#GOGOEND
2000, //67

0003,
0,
0,
#END


COURSE:Easy
LEVEL:3
BALLOON:14,14,28
SCOREINIT:7260
SCOREDIFF:0

#START
7,
08,

#GOGOSTART
3110,
11,
3110,
22,

3110,
11,
3111,
1
#GOGOEND
1, //10

1,
12,
12,
1,

11,
1,
12,
500000000000000000000000000008000000000000000000, //18

11,
1110,
1110,
1110,

12,
12,
7,
08, //26

#GOGOSTART
3110,
11,
3110,
22,

3110,
11,
3111,
1
#GOGOEND
1,

#MEASURE 2/4
2, //35

#MEASURE 4/4
1,
2,
11,
11,

1,
1110,
12,
1130, //43

12,
1110,
12,
1110,

9,
0,
000000000009000000000000000000000000000000000008,
0, //51

#GOGOSTART
3110,
11,
3110,
22,

3110,
11,
3110,
2220, //59

3110,
11,
3110,
22,

3110,
22,
3111,
1
#GOGOEND
2, //67

0003,
0,
0, //70
#END