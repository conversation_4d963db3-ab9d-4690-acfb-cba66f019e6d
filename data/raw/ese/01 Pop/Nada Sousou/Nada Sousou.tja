//TJADB Project
TITLE:Nada Sousou
TITLEJA:涙そうそう
SUBTITLE:--<PERSON><PERSON><PERSON>
BPM:76
WAVE:Nada Sousou.ogg
OFFSET:-6.684
DEMOSTART:54.438

COURSE:Oni
LEVEL:4
SCOREINIT:1370
SCOREDIFF:500


#START


000000000000000000000000000000000000000000000000,
#BPMCHANGE 76.27
000000000000000000000000000000000000100000200000,
#BPMCHANGE 75.72
100000000100100000200000100000000200200000100000,
#BPMCHANGE 76
100000000100100000200000100000000200200000200000,
100000000100100000200000100000100000200000000000,
100000200000200100100000100000000000200000000000,
#BPMCHANGE 75.95
100000200000200100100000100000000000200000000000,
#BPMCHANGE 75.97
100000000000100100000000100000000000100100000000,
100000000000100000100100000000100000200000100100,
#BPMCHANGE 76.01
100000000000100100000000100000000000100000000000,
100100000000200200000000100000000200200000100000,
100000000000100100000000100000000000100100000000,
#BPMCHANGE 76.26
100100000000100000100100000000100000200000100100,
#BPMCHANGE 75.99
100000000000100100000000100000000000100000000100,
#BPMCHANGE 75.7
100000000000100100000000100000000000000000000000,
#MEASURE 2/4
#BPMCHANGE 74.96
000000100000100000200000,

#GOGOSTART
#MEASURE 4/4
#BPMCHANGE 76.71
100100200200200000100000100000000100100000200000,
#BPMCHANGE 76.14
100000000100100000200000100000100200200000100000,
#BPMCHANGE 75.95
100000000100200000100000100000000100200000100000,
100000000000000000000000000000100000100000200000,
100100200200200000100000100000000100200000100000,
100000000100200000100000100000000000200000000000,

#GOGOEND
#BPMCHANGE 76
100000000000200200000000100000100200200000000000,
#BPMCHANGE 75.84
100000000000200200000000100000100200200000000000,
#BPMCHANGE 76.18
100000000000200200000000100000000000100000000000,
#BPMCHANGE 76
300000000000300000000000600000000000000000000000,
#MEASURE 2/4
000000000000000000000008,


#END


COURSE:Hard
LEVEL:3
SCOREINIT:2110
SCOREDIFF:500


#START


000000000000000000000000000000000000000000000000,
000000000000000000000000000000000000100000200000,
100000000000100000200000100000000000200000100000,
100000000000100000200000100000000000200000200000,
100000000000100000200000100000100000200000000000,
100000000000100100100000100000000000200000000000,
100000000000100100100000100000000000200000000000,
100000000000100100000000100000000000100100000000,
100000000000100000100100000000000000000000000000,
100000000000100100000000100000000000100000000000,
100100000000200200000000100000000000000000000000,
100000000000100100000000100000000000100100000000,
100000000000100000100100000000000000000000000000,
100000000000100100000000100000000000100000000000,
100000000000100100000000100000000000000000000000,
#MEASURE 2/4
000000100000100000200000,
#GOGOSTART
#MEASURE 4/4
100000000200200000100000100000000000100000200000,
100000000000100000200000100000000000000000000000,
000000100000200000100000500000000000000000000000,
000000000000000800000000000000100000100000200000,
100000000200200000100000100000000000100000200000,
100000000000100000200000100000000000200000000000,
#GOGOEND
100000000000200200000000100000000000000000000000,
100000000000200200000000100000000000000000000000,
100000000000200200000000100000000000100000000000,
300000000000300000000000600000000000000000000000,
#MEASURE 2/4
000000000000000000000008,


#END


COURSE:Normal
LEVEL:1
SCOREINIT:1700
SCOREDIFF:800


#START


000000000000000000000000000000000000000000000000,
000000000000000000000000000000000000000000000000,
100000000000100000100000100000000000000000000000,
100000000000100000100000100000000000000000000000,
100000000000100000100000100000000000100000000000,
100000000000000000000000000000000000000000000000,
100000000000000000000000000000000000000000000000,
100000000000100100000000100000000000000000000000,
100000000000000000000000100000200000200000000000,
100000000000100100000000100000000000000000000000,
100000000000100000000000100000000000000000000000,
100000000000100100000000100000000000000000000000,
100000000000000000000000100000200000200000000000,
100000000000100100000000100000000000000000000000,
100000000000100000000000100000000000000000000000,
#MEASURE 2/4
000000000000000000000000,
#GOGOSTART
#MEASURE 4/4
100000100000200000000000100000000000000000000000,
100000100000200000000000100000000000000000000000,
100000000000200000000000500000000000000000000000,
000000000000000000800000000000000000000000000000,
100000100000200000000000100000000000000000000000,
100000100000200000000000100000000000000000000000,
#GOGOEND
100000000000000000000000100000000000100000100000,
100000000000000000000000100000000000100000100000,
100000000000000000000000100000000000100000000000,
300000000000300000000000500000000000000000000000,
#MEASURE 2/4
000000000000000000800000,


#END


COURSE:Easy
LEVEL:2
SCOREINIT:3100
SCOREDIFF:900


#START


000000000000000000000000000000000000000000000000,
000000000000000000000000000000000000000000000000,
100000000000000000000000100000000000000000000000,
100000000000200000000000100000000000000000000000,
100000000000000000000000100000000000000000000000,
100000000000000000000000000000000000000000000000,
100000000000000000000000000000000000000000000000,
100000000000000000000000100000000000000000000000,
100000000000000000000000100000000000200000000000,
100000000000000000000000100000000000000000000000,
100000000000000000000000100000000000200000000000,
100000000000000000000000100000000000000000000000,
100000000000000000000000100000000000200000000000,
100000000000000000000000100000000000000000000000,
100000000000100000000000100000000000000000000000,
#MEASURE 2/4
000000000000000000000000,
#GOGOSTART
#MEASURE 4/4
100000000000200000000000100000000000000000000000,
100000000000100000000000100000000000000000000000,
100000000000200000000000100000000000000000000000,
500000000000000000800000000000000000000000000000,
100000000000200000000000100000000000000000000000,
100000000000200000000000100000000000000000000000,
#GOGOEND
100000000000100000000000100000000000000000000000,
100000000000100000000000100000000000000000000000,
100000000000000000000000100000000000000000000000,
100000000000100000000000500000000000000000000000,
#MEASURE 2/4
000000000000000000800000,


#END

