//TJADB Project
TITLE:Override
TITLEJA:オーバーライド
SUBTITLE:--<PERSON><PERSON><PERSON> feat. <PERSON><PERSON><PERSON><PERSON>
SUBTITLEJA:吉田夜世 feat. 重音テト
BPM:204
WAVE:Override.ogg
OFFSET:-1.859
DEMOSTART:40.370

COURSE:Oni
LEVEL:9
BALLOON:10
SCOREINIT:340,970
SCOREDIFF:75

#START
12112210,

3000102210001022,
1020102210102010,
1000102210001022,
1020102220201020,

1000102210001022,
1020102210102010,
1000102210001022,
1020102110122020, //9

10120221,
11200222,
10122122,
11200222,

10120221,
21112020,
10011010,
2000202010002211, //17

1000101010201120,
1020100010221020,
12101222,
1110202010002010,

1000102210102020,
1022202010001020,
1010222010102220,
1022102011102220, //25

1000222210002222,
1000222010202020,
1000222210002222,
1000222010222020,

1020102210201022,
11111111,
30034003,
4431, //33

#GOGOSTART
3022102210102020,
1110222010221020,
1022102210222020,
1111202010222020,

1022102210102020,
1110222010221020,
1022102210104000,
3000404030001122, //41

1022102210102020,
1110222010221020,
1022102210222020,
1111202010222020,

1022102210102020,
1110222010221020,
1022102210102010,
1111102020102020, //49
#GOGOEND

1000102210001022,
1020102210102010,
1000102210001022,
1020102220201020,

1000102210001022,
1020102210102010,
1000102210001022,
1020102110122020, //57

10111122,
11221222,
10111122,
12221122,

10111122,
11221222,
10111122,
12121010, //65

1020201110202011,
1020201110221020,
1000201110202011,
1020201110222020,

1000201110202011,
1020201110221020,
1000201110202011,
1020201110222020,
10000001,
41414010, //75

#GOGOSTART
3022102210102020,
1110222010221020,
1022102210222020,
1111202010222020,

1022102210102020,
1110222010221020,
1022102210104000,
3000404030001122, //83

1022102210102020,
1110222010221020,
1022102210222020,
1111202010222020,

1022102210102020,
1110222010221020,
1022102210102010,
1111101020201020, //91

500000000000000000000000000000000000000008000000,
70000008,
6,
000000000000000000000000000000000008000000000000,

2210101120102010,
1020101120102020,
1120101220102010,
2120201120112211, //99

1022102210102020,
1110222011102220,
1022102210102020,
1111201120001020,

1022102210102020,
1110222011102220,
1022102210102020,
1111102020102020, //107
#GOGOEND

1000102210001022,
1020102210102010,
1000102210001022,
1020102220201020,

1000102210001022,
1020102210102010,
1000102210001022,
1020102110122020,
3,
0, //117
#END


COURSE:Hard
LEVEL:6
BALLOON:8
SCOREINIT:380,1520
SCOREDIFF:90

#START
11111110,

30121012,
10121120,
10121012,
10122210,

10121012,
10121120,
10121012,
10122210, //9

10110201,
10100220,
10110201,
10200220,

10110220,
11102020,
10011010,
2210, //17

10101222,
10101222,
10101222,
10222020,

10111020,
22201010,
1212,
11101110, //25

10021002,
10021020,
10021002,
10021020,

10101212,
10111110,
30033003,
3431, //33

#GOGOSTART
30101122,
10222012,
10112212,
10221020,

10101122,
10112022,
10221120,
30403011, //41

10101122,
10222012,
10112212,
10221020,

10101122,
10112022,
10221120,
10022122, //49
#GOGOEND

10121012,
10121120,
10121012,
10122210,

10121012,
10121120,
10121012,
10122212, //57

10111010,
11111010,
10111010,
11102220,

10111010,
11111010,
10111010,
12121010, //65

10111011,
11111011,
10111011,
11111022,

10111011,
11111011,
10111010,
22221122,
10000001,
21212010, //75

#GOGOSTART
30101122,
10222012,
10112212,
10221020,

10101122,
10112022,
10221120,
30403011, //83

10101122,
10222012,
10112212,
10221020,

10101122,
10112022,
10221120,
10022020, //91

500000000000000000000000000000000000000008000000,
70000008,
6,
000000000000000000000000000000000008000000000000,

22112020,
12112020,
12112020,
1110101010001010, //99

10121122,
10201120,
10121122,
10212010,

10121122,
10201120,
10121120,
10022122, //107
#GOGOEND

10121012,
10121120,
10121012,
10122210,

10121012,
10121120,
10121012,
10122212,
3,
0, //117
#END


COURSE:Normal
LEVEL:5
BALLOON:6
SCOREINIT:490,2670
SCOREDIFF:133

#START
1011,

31,
1110,
11,
1110,

11,
1110,
11,
2220, //9

1,
1100,
1,
1100,

1,
1110,
11,
2220, //17

11,
1100,
1100,
1110,

11,
2220,
11,
1111, //25

11,
1012,
11,
1012,

1110,
1111,
33,
3330, //33

#GOGOSTART
30101110,
1120,
10102220,
1110,

10101110,
1120,
10102220,
3330, //41

10101110,
1120,
10102220,
1110,

10101110,
1120,
10102220,
1022, //49
#GOGOEND

31,
1110,
11,
1110,

11,
1110,
11,
2220, //57

11,
1011,
11,
1022,

21,
1011,
11,
1120, //65

1011,
1011,
1011,
1022,

2011,
1011,
1011,
1122,
1,
2220, //75

#GOGOSTART
30101110,
1120,
10102220,
1110,

10101110,
1120,
10102220,
3330, //83

10101110,
1120,
10102220,
1110,

10101110,
1120,
10102220,
1022, //91

500000000000000000000000000000000000000008000000,
70000008,
6,
000000000000000000000000000000000008000000000000,

20111020,
10111020,
10111020,
11112000, //99

10101110,
1212,
10101110,
2220,

10101110,
1212,
10101110,
1022, //107
#GOGOEND

31,
1110,
11,
1110,

11,
1110,
11,
2222,
3,
0, //117
#END


COURSE:Easy
LEVEL:3
BALLOON:4
SCOREINIT:490,4710
SCOREDIFF:165

#START
11,

3,
11,
1,
11,

1,
11,
1,
22, //9

1,
1,
1,
1,

1,
11,
1,
22, //17

11,
1,
1,
11,

1,
22,
11,
11, //25

1,
0,
1,
0,

1,
11,
33,
3330, //33

#GOGOSTART
31,
1110,
11,
22,

11,
1110,
11,
3330, //41

11,
1110,
11,
22,

11,
1110,
11,
12, //49
#GOGOEND

3,
11,
1,
11,

1,
11,
1,
2, //57

1,
0,
1,
0,

1,
0,
1,
11, //65

1,
11,
1,
11,

1,
11,
1,
12,
1,
22, //75

#GOGOSTART
31,
1110,
11,
22,

11,
1110,
11,
3330, //83

11,
1110,
11,
22,

11,
1110,
11,
2, //91

500000000000000000000000000000000000000008000000,
70000008,
6,
000000000000000000000000000008000000000000000000,

22,
11,
22,
1110, //99

11,
1110,
11,
2220,

11,
1110,
11,
12, //107
#GOGOEND

3,
11,
1,
11,

1,
11,
1,
22,
3,
0, //117
#END
