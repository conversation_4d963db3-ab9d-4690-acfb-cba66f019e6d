//TJADB Project
TITLE:<PERSON>enenenenenenene! Daibakusou
TITLEJA:ねねねねねねねね!大爆走
SUBTITLE:--Momosuzu Nene
SUBTITLEJA:桃鈴ねね
BPM:170
WAVE:Nenenenenenenene! Daibakusou.ogg
OFFSET:-1.105
DEMOSTART:57.918
 
//Shinuchi: 7240/4380/2910/2010

COURSE:Oni
LEVEL:7
BALLOON:33
SCOREINIT:610,2200
SCOREDIFF:165

#START
600000000000000000000000000008000000300000000000,

#GOGOSTART
1010201010112010,
01210441, //Note: First don SENOTE is a forced "ƒR"
1011101010111010,
2220202022202020,

1010201010112010,
0010201110404000,
10221121,
2220202022202020, //9
#GOGOEND

1011000020220011,
1010111011011020,
12211221,
0000202020000011,

1010201010102011,
11211120,
1000100010010010,
0020020020000011, //17

1010202010102011,
1010201000202011,
11221121,
01010033,

0010101110204030,
0010101110004000,
1020201010202011,
12211222,

1001201010012010,
1001201010004000,
1001201010012010,
1001201010004000, //29

#MEASURE 3/4
100010002002,
100110002000,
112,
100000202,

100102200,
122,
13,
33, //37

#MEASURE 4/4
1000201010102011,
11211220,
1011102010111020,
100000000000000000600000000000000000000008000000,
33,
3000300030001011, //43

#GOGOSTART
1020102010102011,
1020102010102011,
12121121,
0000400040001011,

1020102010004011,
1020102010004000,
1020102010111020,
0000400040001011, //51

1011102010102011,
1011102010002011,
1011102010102011,
1011102010200011,

1011102010404011,
1011102010404000,
1011102010111020,
0222, //59

3011200030112000,
3011200030112000,
12121212,
1120102011201020,

6,
000008000000000000000000000000000000000000000000,
1011102010111020,
100000200200100000200000600000000000000008000000, //67

7,
000000000000000000000000000000000000000000000008,
03,
#GOGOEND
0,
0, //72
#END


COURSE:Hard
LEVEL:4
BALLOON:9,17
SCOREINIT:620,3460
SCOREDIFF:183

#START
600000000000000000000000000008000000300000000000,

#GOGOSTART
10101121,
01010440,
10101110,
4444,

10101121,
01010440,
10101110,
4444, //9
#GOGOEND

500000000000000000000000000000000008000000000000,
11211020,
11201121,
00222000,

10101120,
10101120,
1000100010010010,
0020020020000010, //17

01201121,
11010121,
01201121,
01010033,

01111043,
01111040,
5,
000000000000000000000000000000000008000000000000,

1001001010010010,
1001001010004000,
1001001010010010,
1001001010004000, //29

#MEASURE 3/4
122,
112,
102,
102,

112,
122,
13,
33, //37

#MEASURE 4/4
10211021,
10211220,
5,
000000000000000008000000000000000000000000000000,
33,
30303001, //43

#GOGOSTART
01111111,
01111111,
01111111,
00404011,

01111041,
01111040,
01122112,
00404011, //51

01111221,
01111021,
01111227,
000000000000000000000000000000000000080000100000,

01111441,
01111440,
02211221,
00202020, //59

3434,
3434,
1111,
12121212,

6,
000008000000000000000000000000000000000000000000,
1111,
100000200000100000200000600000000000000008000000, //67

9,
09008000,
03,
#GOGOEND
0,
0, //72
#END


COURSE:Normal
LEVEL:4
BALLOON:8,7,11
SCOREINIT:740,5360
SCOREDIFF:245

#START
600000000000000000000000000000000008000000000000,

#GOGOSTART
10101001,
01010000,
10101110,
4444,

10101001,
01010000,
10101110,
4444, //9
#GOGOEND

500000000000000000000000000000000008000000000000,
1011,
10201011,
00202000,

1110,
1110,
1170,
00000801, //17

0111,
11010001,
00101101,
01010003,

00101003,
00101000,
5,
000000000000000000000000000000000008000000000000,

10011000,
10011040,
10011000,
10011040, //29

#MEASURE 3/4
101,
102,
101,
102,

122,
122,
13,
33, //37

#MEASURE 4/4
10111000,
10111000,
5,
000000000000000008000000000000000000000000000000,
33,
30000001, //43

#GOGOSTART
00101011,
00101011,
00101011,
00404001,

00101041,
00101040,
01010101,
00404001, //51

00101011,
00101011,
00101017,
000000000000000000000000000000000000080000100000,

00101041,
00101040,
01010101,
00202020, //59

3434,
3434,
1,
11,

6,
000008000000000000000000000000000000000000000000,
11,
1111,

9,
09008000,
03,
#GOGOEND
0,
0, //72
#END


COURSE:Easy
LEVEL:3
BALLOON:9,10
SCOREINIT:750,8790
SCOREDIFF:363

#START
600000000000000000000000000008000000000000000000,

#GOGOSTART
11,
0,
1110,
4440,

11,
0,
1010,
4440, //9
#GOGOEND

500000000000000000000000000008000000000000000000,
11,
1011,
0,

11,
11,
7,
00008001, //17

01,
10000001,
00001005,
000000000000000008000000000000000000000000300000,

00000003,
0,
5,
000000000000000000000000000008000000000000000000,

11,
1013,
11,
1013, //29

#MEASURE 3/4
1,
2,
2,
2,

1,
2,
13,
33, //37

#MEASURE 4/4
11,
1110,
5,
000000000000000008000000000000000000000000000000,
33,
30000001, //43

#GOGOSTART
00101001,
00101001,
00101001,
00000001,

00101001,
00101000,
01010101,
00000001, //51

00101001,
00101001,
00101001,
00000001,

00101001,
00101000,
01010101,
00101010, //59

33,
33,
1,
11,

6,
000008000000000000000000000000000000000000000000,
1,
11, //67

9,
09008000,
03,
#GOGOEND
0,
0, //72
#END