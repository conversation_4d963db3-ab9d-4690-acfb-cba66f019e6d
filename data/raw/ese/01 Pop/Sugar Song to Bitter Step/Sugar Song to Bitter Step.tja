//TJADB Project
TITLE:Sugar Song to Bitter Step
TITLEJA:シュガーソングとビターステップ
SUBTITLE:--UNISON SQUARE GARDEN/Blood Blockade Battlefront
SUBTITLEJA:「血界戦線」より
BPM:132
WAVE:Sugar Song to Bitter Step.ogg
OFFSET:-1.916
DEMOSTART:67.378

//Shinuchi: 5630/4500/2710/1790


COURSE:Oni
LEVEL:8
BALLOON:
SCOREINIT:580,1870
SCOREDIFF:153

#START
1110202212020200,
100000000000200000000200100100100000200010001000,
1110202212020200,
100000000000200000000200100100100000200010001000,

1110202212020200,
1110201122020220,
3334,
1111011110004000,

1022120101201120,
1022120101201120,
1022120100102020,
3330,

0010222010221201,
0110202210220011,
2010210100221201,
0110201200002012,

0000201200002022,
1010202210200011,
1010201200111020,
1020120020022211,

1010222010221201,
0120112011120011,
1010220200221201,
0110211200002012,

0000201200002022,
1010202210200011,
1010201201110200,
1020120020112220,

1010210030002020,
1010210030004000,
1020102210210020,
1020112010210010,

100000200000100100000500000000000000000000000008,
0000001020101022,
1012020021112112,
000000000111200000200000,

#GOGOSTART
1110202212020011,
1020221120120011,
2010202210102022,
1010221010210000,

1110202212020022,
1010221210220011,
2010220100102202,
0020210200221120,

3000400030110101,
0011202101221000,
#GOGOEND
2022120100102020,
3333,

#GOGOSTART
1110202212020011,
1020221120120011,
2010202210102022,
1010221010210000,

1110202212020022,
1010221210220011,
2010221100102212,
0020211200221120,

3000400030110101,
0011202101221020,
3000400030120101,
0120201202103000,
#GOGOEND

0,
#END


COURSE:Hard
LEVEL:5
BALLOON:10,2,5
SCOREINIT:640,2990
SCOREDIFF:173

#START
1010201111010200,
0,
1010201111010200,
0,

1010201111010200,
1010201111010110,
3334,
7008,

1010110100102000,
1010110100102000,
1010110100102020,
3330,

10201120,
1000200010110000,
10201120,
1000202200001011,

0000101100001000,
11201200,
1010202200102000,
500000000000000000000008000000000000000000000000,

11201120,
1010200010110000,
11201120,
1010202200001011,

0000101100001020,
11201200,
1010201101020200,
100000000000000700008000,

1010110030002020,
1010110030000000,
1020102010110020,
1020102010110010,

100000200000100100000500000000000000000000000008,
00012122,
100000100100000100000000700000000000000000008000,
0000001110002000, //36

#GOGOSTART
1010200011010010,
0010201110110010,
11201120,
1020102010110000,

1010200011010010,
0010201110110010,
2020110100101101,
0010110100102020,

3000300040030306,
000000000000000000000000000000000000008000000000,//46
#GOGOEND
0,
0, //48

#GOGOSTART
1110202011020010,
0010201110110010,
11201120,
1020102010110000,

1110202011020010,
0010201110110010,
2020110100101101,
0010110100102020,

3000300040030306,
000000000000000000000000000000008000000000000000,
3000300040030306,
000000000000000000000000008000000000300000000000,//60
#GOGOEND

0,
#END


COURSE:Normal
LEVEL:4
BALLOON:8,2,2,2,2,19,19,21
SCOREINIT:810,5860
SCOREDIFF:250

#START
3,
10201120,
3,
10201120,

3,
3,
3334,
7008,

10100220,
10100220,
10100220,
3330,

1202,
1202,
1202,
1217,

8781,
1210,
1212,
500000000000000000000008000000000000000000000000,

1212,
1210,
1212,
1217,

8781,
1210,
1212,
10000300,

600000000000000000000008000000000000000000000000,
600000000000000000000008000000000000000000000000,
33303000,
33303000,

1100,
1100,
500000000000000000000000000000000008000000000000,
3334, //36

#GOGOSTART
10201110,
10201110,
10102220,
3330,

10201120,
10201120,
30300220,
30300220,

7,
0008,
#GOGOEND
0,
0, //48

#GOGOSTART
11201110,
11201110,
10102220,
3330,

11201120,
11201120,
30300220,
30300220,

7,
0008,
9,
0000090000008000, //60
#GOGOEND

0,
#END


COURSE:Easy
LEVEL:3
BALLOON:5,12,12,14
SCOREINIT:610,7890
SCOREDIFF:230

#START
3,
1212,
3,
1212,

3,
3,
33,
7008,

1012,
1012,
1012,
3330,

1200,
1200,
1200,
12,

33,
1120,
1011,
500000000000000000000000000008000000000000000000,

1210,
1210,
1210,
12,

33,
1120,
1011,
1,

600000000000000000000008000000000000000000000000,
600000000000000000000008000000000000000000000000,
3430,
3430,

1,
1,
11,
3334, //36

#GOGOSTART
1210,
1210,
3333,
4440,

1210,
1210,
3302,
3302,

7,
0008,
#GOGOEND
0,
0, //48

#GOGOSTART
1210,
1210,
3333,
4440,

1210,
1210,
3302,
3302,

7,
0008,
9,
0000090000008000, //60
#GOGOEND

0,
#END