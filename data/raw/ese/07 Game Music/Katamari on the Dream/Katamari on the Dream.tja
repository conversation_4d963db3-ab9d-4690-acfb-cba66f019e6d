//TJADB Project
TITLE:<PERSON><PERSON><PERSON> on the Dream
TITLEJA:塊オンザドリーム
SUBTITLE:--<PERSON><PERSON><PERSON> Damacy Rolling LIVE
SUBTITLEJA:「塊魂 Rolling LIVE」より
BPM:178
WAVE:<PERSON><PERSON><PERSON> on the Dream.ogg
OFFSET:-2.403
DEMOSTART:64.080

COURSE:Oni
LEVEL:7
BALLOON:11,50
SCOREINIT:700,2130
SCOREDIFF:185

#START
30030000,
30040000,
0000000000002222,
2,

#MEASURE 6/4
100011112010111120111010,
100011112010122220222010,
#MEASURE 5/4
10001111201011112220,
#MEASURE 4/4
3000002220000000,
1022201022104000, //9

10000002,
0,
10000002,
02020100,

10100002,
00010000,
10222223,
0004, //17

1000200011102010,
0010200011102010,
1000200011102010,
0010201011102020,

1000200011102010,
0010201011102000,
1000201110102010,
0010201000202222, //25

1000101120100010,
0010001020111000,
1000101120100010,
0020001020111000,

1000101120100020,
01012100,
#MEASURE 6/4
500000000000000000000000000000000000000000000000000000000000000008000000, //32

#MEASURE 4/4
30002003,
0022202020102000,
3000200030222030,
0010221022104000,

1000000020000022,
2120,
1000000020000022,
2000100020002222,

1000000020000022,
2120,
1000102210201022,
2010112210204000, //44

#GOGOSTART
1000201111102010,
0010201120102000,
1000201111102010,
0010201120102020,

1000201111102010,
0010201120102000,
#MEASURE 6/4
301301700000,
#GOGOEND
#MEASURE 4/4
8,
04, //53

#GOGOSTART
1000201011112010,
0010200011112020,
1000201011112010,
0010201022112000,

1000201011112010,
0010200011112000,
#GOGOEND
#MEASURE 6/4
200010200011200010102210,
200011112000201022222000, //61

#MEASURE 9/8
111000222000111000,
#MEASURE 4/4
3000201110102010,
1020221022112011,
1000222211112222,
#MEASURE 7/4
1020102030403040221100000000, //66

#MEASURE 4/4
7,
0,
0,
0800,

0,
0,
0,
0, //74
#END


COURSE:Hard
LEVEL:5
BALLOON:33,10,39
SCOREINIT:880,3580
SCOREDIFF:260

#START
3,
7,
0,
08,

#MEASURE 6/4
101101100000,
101101100000,
#MEASURE 5/4
1011011000,
#MEASURE 4/4
32,
22, //9

10000002,
0,
10000002,
02020200,

10000002,
00020000,
10202023,
0004, //17

10201021,
00201120,
10201021,
00201120,

10201021,
00201120,
10210101,
02020200, //25

10102101,
01012000,
10102101,
01012000,

10102101,
01012000,
#MEASURE 6/4
500000000000000000000000000000000000000000000000000000000000000008000000, //32

#MEASURE 4/4
30002003,
02222020,
30203023,
00222040,

10002001,
0120,
10002001,
0120,

10002001,
0120,
10101111,
01010240, //44

#GOGOSTART
10201121,
01201120,
10201121,
01202120,

10201121,
01201120,
#MEASURE 6/4
301301700000,
#GOGOEND
#MEASURE 4/4
8,
04, //53

#GOGOSTART
10201121,
01201120,
10201121,
01201120,

10201121,
01201120,
#GOGOEND
#MEASURE 6/4
100200101120,
202220012220, //61

#MEASURE 9/8
300400300,
#MEASURE 4/4
30201121,
10212120,
10201120,
#MEASURE 7/4
11221122000000, //66

#MEASURE 4/4
9,
0,
0000000900000000,
0800,

0,
0,
0,
0, //74
#END


COURSE:Normal
LEVEL:4
BALLOON:18,13,6,20
SCOREINIT:1190,6580
SCOREDIFF:498

#START
0,
7,
0,
08,

#MEASURE 6/4
1110,
1110,
#MEASURE 5/4
1001001000,
#MEASURE 4/4
7,
000000000000000000000000000008000000000000000000, //9

10000001,
0,
10000001,
00010000,

10000001,
00010000,
10101003,
0, //17

10101001,
02,
10101001,
02,

10101001,
02,
10010001,
00020000, //25

10002001,
01002000,
10002001,
01002000,

10002001,
01002000,
#MEASURE 6/4
500000000000000000000000000000000000000000000000000000000008000000000000, //32

#MEASURE 4/4
30000003,
0,
30303003,
0,

12,
02,
12,
02,

12,
02,
10101005,
000000000000000000000000000008000000000000000000, //44

#GOGOSTART
10101001,
0120,
10101001,
0220,

10101001,
0120,
#MEASURE 6/4
3370,
#GOGOEND
#MEASURE 4/4
8,
04, //53

#GOGOSTART
10101001,
0120,
10101001,
0220,

10101001,
0120,
#GOGOEND
#MEASURE 6/4
100111,
022, //61

#MEASURE 9/8
333,
#MEASURE 4/4
31,
1120,
12,
#MEASURE 7/4
1111000, //66

#MEASURE 4/4
9,
0,
0000000900000000,
0800,

0,
0,
0,
0, //74
#END


COURSE:Easy
LEVEL:3
BALLOON:13,10,4,16
SCOREINIT:1320,11880
SCOREDIFF:965

#START
0,
7,
0,
08,

#MEASURE 6/4
1,
1,
#MEASURE 5/4
1,
#MEASURE 4/4
7,
000000000000000000000000000008000000000000000000, //9

1,
0,
1,
0,

1,
0,
10001001,
0, //17

11,
01,
11,
02,

11,
01,
5,
000000000000000008000000000000000000000000000000, //25

1,
02,
1,
02,

1,
02,
#MEASURE 6/4
500000000000000000000000000000000000000000000000000008000000000000000000, //32

#MEASURE 4/4
3,
0,
30000003,
0,

22,
02,
22,
02,

22,
02,
10001005,
000000000000000000000000000008000000000000000000, //44

#GOGOSTART
10001001,
01,
10001001,
0,

10001001,
01,
#MEASURE 6/4
37,
#GOGOEND
#MEASURE 4/4
8,
0, //53

#GOGOSTART
10001001,
01,
10001001,
02,

10001001,
01,
#GOGOEND
#MEASURE 6/4
100011,
002, //61

#MEASURE 9/8
333,
#MEASURE 4/4
3,
0,
1,
#MEASURE 7/4
1010000, //66

#MEASURE 4/4
9,
0,
0000000900000000,
0800,

0,
0,
0,
0, //74
#END