//TJADB Project
TITLE:shiny smile -G<PERSON>ha Hibiki Version-
TITLEJA:shiny smile
SUBTITLE:--<PERSON><PERSON> feat. <PERSON><PERSON>umakura/THE iDOLM@STER： Live for You!
SUBTITLEJA:別歌唱バージョン ‐ 響
BPM:170
WAVE:shiny smile -<PERSON><PERSON><PERSON> Hibiki Version-.ogg
OFFSET:-1.737
DEMOSTART:42.678


COURSE:Edit
LEVEL:7
BALLOON:
SCOREINIT:580,2100
SCOREDIFF:153

#START
20002002,
0,
20002002,
0,
1111, //5

10201121,
0011102000102000,
10201121,
0011102000102000,

10210121,
00330000,
1011102000001011,
1020001011102000, //13

10201121,
0011102000102000,
10201121,
0011102000102000,
10210121,
00033033, //19

#GOGOSTART
00100100,
#GOGOEND
1000111000222020,
#GOGOSTART
00100100,
#GOGOEND
1000111000222020,

#GOGOSTART
00100100,
#GOG<PERSON><PERSON>
1000111000222020,
10201120,
11210120,
1010,
3030003000302222, //29

#GOGOSTART
2000101120102010,
1120101120102010,
1020102010222020,
3000300030002222,

2000101120102010,
1120101120102010,
1020102010222020,
1022102010221010, //37

0000101120102010,
1120101120102010,
1020102010222000,
0000300030002222,

2000101120102010,
3434,
1000101120002000,
0333, //45

35,
000000000000000000000000000000000008000000000000,
10202012,
0022200020001020,
#BARLINEOFF
0, //50
#GOGOEND

#BARLINEON
00222020,
00222020,
0222,
0220,

10222020,
10222020,
1222,
1022, //58

10221020,
10221020,
1212,
1220,

10221010,
1212,
1122,
0333, //66

#GOGOSTART
1120101120102010,
1120101120102010,
1020102010222020,
3000300030002222,

2000101120102010,
1120101120102010,
1020102010222020,
1022102010221010, //74

0000101120102010,
1120101120102010,
1020102010222000,
0000300030002222,

2000101120102010,
3434,
1022102010221000,
0000300030003022, //82

2333,
36,
000000000000000000000000000000000008000000000000,
#GOGOEND
0,
0,
0, //88
#END


COURSE:Oni
LEVEL:8
BALLOON:
SCOREINIT:530,1780
SCOREDIFF:135

#START
10001003,
0000002220202020,
10001003,
0000202220002000,
3000202210112000, //5

1000200010102021,
11210120,
1000200010102021,
1000200010102022,

1000201000102021,
10210120,
1010202110102022,
1010202110102022, //13

1000200010102021,
1010201220102022,
1000200010102021,
1000202110002000,
1000201000102021,
1010201220122010, //19

#BARLINEOFF
#SCROLL 2
#GOGOSTART
000000000000600000000000000000000008000000000000,
#GOGOEND
#BARLINEON
#SCROLL 1
2220002220002220,
#BARLINEOFF
#SCROLL 2
#GOGOSTART
000000000000600000000000000000000008000000000000,
#GOGOEND
#BARLINEON
#SCROLL 1
1110001110001110, //23

#BARLINEOFF
#SCROLL 2
#GOGOSTART
000000000000600000000000000000000008000000000000,
#GOGOEND
#BARLINEON
#SCROLL 1
2220002220002220,
10201120,
1010202110102000,
33,
3030003000302222, //29

#GOGOSTART
2022101020221010,
2022101020221010,
2220111022201110,
2220111040004000,

1011202010112020,
1012202010122020,
2220111022201110,
2220102211221122, //37

2022101020221010,
2022101020221010,
4345,
000000000000000000000000000000000008000000000000, //41

1011202010112020,
1110222011102220,
2220111022201110,
0444, 

36,
000000000000000000000000000000000008000000000000,
10202012,
00202103,
#BARLINEOFF
0, //50
#GOGOEND

#BARLINEON
#SCROLL 0.5
2,
#BARLINEOFF
22,
#BARLINEON
2,
#BARLINEOFF
22, 

#BARLINEON
2,
#BARLINEOFF
22,
#BARLINEON
2,
#BARLINEOFF
22, //58

#BARLINEON
#SCROLL 1
1000200010102002,
1000200210002000,
1010200010102002,
1000200210002000,

1000200010102002,
1000200210002000,
1111222211112222,
0333, //66

#GOGOSTART
2022101020221010,
2022101020221010,
2220111022201110,
2220111040004000,

1011202010112020,
1012202010122020,
2220111022201110,
2220102211221122, //74

2022101020221010,
2022101020221010,
4345,
000000000000000000000000000000000008000000000000,

1011202010112020,
1110222011102220,
2220111022201110,
0000400040004011, //82

1333,
36,
000000000000000000000000000000000008000000000000,
#GOGOEND
0,
0,
0, //88
#END


COURSE:Hard
LEVEL:4
BALLOON:
SCOREINIT:550,3310
SCOREDIFF:150

#START
10001003,
0,
10001003,
0,
3044, //5

10201121,
01210120,
10201121,
00201121,

10210121,
00210020,
11210121,
11011120, //13

10201120,
10210121,
01211121,
00201120,
10210121,
01212021, //19

#GOGOSTART
000000000000600000000000000000000008000000000000,
#GOGOEND
30030030,
#GOGOSTART
000000000000600000000000000000000008000000000000,
#GOGOEND
30030030,

#GOGOSTART
000000000000600000000000000000000008000000000000,
#GOGOEND
30030030,
10201120,
11210120,
33,
33030300, //29

#GOGOSTART
20112010,
20112010,
4343,
4344,

10210121,
20112010,
4343,
40304033, //37

00212021,
20112010,
4345,
000000000000000000000000000000000008000000000000,

10210122,
1212,
4343,
0444, //45

36,
000000000000000000000000000000000008000000000000,
0,
0,
#BARLINEOFF
0, //50
#GOGOEND

#BARLINEON
#SCROLL 0.5
2,
#BARLINEOFF
22,
#BARLINEON
2,
#BARLINEOFF
22, 

#BARLINEON
2,
#BARLINEOFF
22,
#BARLINEON
2,
#BARLINEOFF
22, //58

#BARLINEON
#SCROLL 1
10201120,
1212,
10201120,
1210,

10201120,
1212,
20112010,
0333, //66

#GOGOSTART
20112010,
20112010,
4343,
4344,

10210121,
20112010,
4343,
40304033, //74

00212021,
20112010,
4345,
000000000000000000000000000000000008000000000000,

10210122,
1212,
4343,
0444, //82

0333,
36,
000000000000000000000000000000000008000000000000,
#GOGOEND
0,
0,
0, //88
#END


COURSE:Normal
LEVEL:4
BALLOON:6
SCOREINIT:580,4970
SCOREDIFF:183

#START
10000003,
0,
10000003,
0,
3044, //5

0101,
0101,
0101,
0101,

2101,
2101,
2101,
2101, //13

0101,
0101,
2101,
2101,
1111,
1011, //19

#GOGOSTART
000000000000600000000000000000000008000000000000,
#GOGOEND
0,
#GOGOSTART
000000000000600000000000000000000008000000000000,
#GOGOEND
0,

#GOGOSTART
000000000000600000000000000000000008000000000000,
#GOGOEND
0,
11,
1110,
33,
7008, //29

#GOGOSTART
0121,
2121,
4343,
4344,

0121,
2121,
4343,
4343, //37

0121,
2121,
4345,
000000000000000000000000000000000008000000000000,

0212,
3434,
4343,
0444, //45

36,
000000000000000000000000000000000008000000000000,
0,
0,
#BARLINEOFF
0, //50
#GOGOEND

#BARLINEON
#SCROLL 0.5
2,
#BARLINEOFF
22,
#BARLINEON
2,
#BARLINEOFF
22, 

#BARLINEON
2,
#BARLINEOFF
22,
#BARLINEON
2,
#BARLINEOFF
22, //58

#BARLINEON
#SCROLL 1
0202,
0222,
0222,
0220,

0222,
1212,
2121,
0333, //66

#GOGOSTART
0121,
2121,
4343,
4344,

0121,
2121,
4343,
4343, //74

0121,
2121,
4345,
000000000000000000000000000000000008000000000000,

0212,
3434,
4343,
0444, //82

0333,
36,
000000000000000000000000000000000008000000000000,
#GOGOEND
0,
0,
0, //88
#END


COURSE:Easy
LEVEL:3
BALLOON:4
SCOREINIT:590,6640
SCOREDIFF:210

#START
0,
0,
0,
0,
0, //5

0101,
0101,
0101,
0101,

0202,
0202,
0202,
0202, //13

0101,
0101,
0202,
0202,
0101,
0111, //19

#GOGOSTART
000000000000600000000000000000000008000000000000,
#GOGOEND
0,
#GOGOSTART
000000000000600000000000000000000008000000000000,
#GOGOEND
0,

#GOGOSTART
000000000000600000000000000000000008000000000000,
#GOGOEND
0,
11,
1110,
33,
7008, //29

#GOGOSTART
0111,
0101,
0111,
0144,

0111,
0101,
0111,
0101, //37

0111,
0101,
0111,
0110,

0222,
0202,
1111,
0444, //45

36,
000000000000000000000000000000000008000000000000,
0,
0,
#BARLINEOFF
0, //50
#GOGOEND

#BARLINEON
#SCROLL 0.5
2,
#BARLINEOFF
0,
#BARLINEON
2,
#BARLINEOFF
0, 

#BARLINEON
2,
#BARLINEOFF
2,
#BARLINEON
2,
#BARLINEOFF
2, //58

#BARLINEON
#SCROLL 1
0202,
0202,
0202,
0220,

0202,
0202,
0202,
0333, //66

#GOGOSTART
0111,
0101,
0111,
0144,

0111,
0101,
0111,
0101, //74

0111,
0101,
0111,
0110,

0222,
0202,
1111,
0444, //82

0333,
36,
000000000000000000000000000000000008000000000000,
#GOGOEND
0,
0,
0, //88
#END