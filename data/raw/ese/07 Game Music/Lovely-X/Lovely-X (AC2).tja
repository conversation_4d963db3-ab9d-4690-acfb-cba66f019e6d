//TJADB Project
TITLE:Lovely-X -AC2/AC3 Audio/Chart-
TITLEJA:ラブリーX
SUBTITLE:--<PERSON> feat. <PERSON><PERSON><PERSON><PERSON>/NEW RALLY-X
SUBTITLEJA:AC2/AC3音源・譜面
BPM:134
WAVE:Lovely-X (AC2).ogg
OFFSET:-3.471
DEMOSTART:21.360

//No Hard chart in AC3

//Branches location and condition unconfirmed. Oni(Donderful) condition different from Hard.
//Donderful/Hard DP Chart #N/#E unconfirmed but the difference with SP chart seems to be in #32/33 only, 
//Currently Hard #8/11/19/27 and Oni #11 are more-or-less confirmed from AC2/CS1 chart videos,
//Other branch positions are purely deductions and not necessarily accurate.

//Vid Sources:
//Hard DP (CS1): https://www.youtube.com/watch?v=MJonL8MJ1a0
//Hard (AC2) (Branch @ #8): https://www.youtube.com/watch?v=NyCdQKtBABs
//Hard (CS1) (Branch @ #11/19): https://www.youtube.com/watch?v=fAXbLRTL68c
//Hard (AC2) (Branch @ #27): https://www.youtube.com/watch?v=t2b5c_zPewI
//Donderful (AC2) (Branch @ #11): https://www.youtube.com/watch?v=T6-W3DfI3PU

//Track Source: https://www.youtube.com/watch?v=rSapN4IAyW4 and tweaked BPM from 134.25 to 134.

COURSE:Oni
LEVEL:10

STYLE:SINGLE
BALLOON:
SCOREINIT:
SCOREDIFF:
SCOREMODE:0

#START
0,
0,
0,

1000100010222020,
11120110,
1000100010111010, //6
3, //7

00000110,
11121112,
11121000, //10

#SECTION
#BRANCHSTART p,75,90
#N
11121120,
11121010,
11121111,
1022202010101010,

11121120,
11121010,
11121111,
1022202010101000, //n18

#E
11121120,
1011102010111000,
11121111,
1022202010111020,

11121120,
1011102010111000,
11121111,
1022202010111022, //e18

#M
1011102010102000,
1011102010111000,
1011102011011010,
1022202010111020,

1011102010102000,
1011102010111000,
1011102011011010,
1022202010111022, //m18

#SECTION
#BRANCHSTART p,75,90
#N
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000, //n26

#E
10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000,

10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //e26

#M
1000102010111020,
1101102010222020,
1101102010222020,
500000000000000000000008000000200200200000200000,

1000102010111020,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //m26

#SECTION
#BRANCHSTART p,75,90
#N
11121120,
11121120,
11121120,
1011102010101000,

0,
11121112,
0,
0, //n34

#E
11121120,
11121120,
1011102011102000,
1011102010101000,

0,
1010102210101020,
0,
0, //e34

#M
1011102011102000,
1011102011102000,
1011102011102000,
1011102010101000,

0,
1010102210101020,
0,
0, //m34

#SECTION
#BRANCHSTART p,75,90
#N
10001100,
1010001010222020,
10001100,
1010001022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202020, //n43

#E
10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202220, //e43

#M
10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011021020,
1022202011021020,
1022202011021020,
12212212,
2202202202202220, //m43

#SECTION
#BRANCHSTART p,75,90
#N
0,
00000110,
11121112,
11121000, //n47

#E
0,
00000110,
1011102011011020,
1011102210000000, //e47

#M
0,
00000110,
1011102011011020,
1011102210000000, //m47


#SECTION
#BRANCHSTART p,75,90
#N
11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n55

#E
11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e55

#M
1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m55

#SECTION
#BRANCHSTART p,75,90
#N
11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n63

#E
11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e63

#M
1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m63

#SECTION
#BRANCHSTART p,75,90
#N
1000100010222020,
00000110,
11101103,
0,

0,
0,
0, //n70

#E
1000100010222020,
0000000011111110,
1011100010100030,
0,

0,
0,
0, //e70

#M
1000100010222020,
0000000011121112,
1011100010100030,
0,

0,
0,
0, //m70
#BRANCHEND
#END

STYLE:DOUBLE
BALLOON:
SCOREINIT:
SCOREDIFF:
SCOREMODE:0

#START P1
0,
0,
0,

1000100010222020,
11120110,
1000100010111010, //6
3, //7

00000110,
11121112,
11121000, //10

#SECTION
#BRANCHSTART p,75,90
#N
11121120,
11121010,
11121111,
1022202010101010,

11121120,
11121010,
11121111,
1022202010101000, //n18

#E
11121120,
1011102010111000,
11121111,
1022202010111020,

11121120,
1011102010111000,
11121111,
1022202010111022, //e18

#M
1011102010102000,
1011102010111000,
1011102011011010,
1022202010111020,

1011102010102000,
1011102010111000,
1011102011011010,
1022202010111022, //m18

#SECTION
#BRANCHSTART p,75,90
#N
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000, //n26

#E
10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000,

10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //e26

#M
1000102010111020,
1101102010222020,
1101102010222020,
500000000000000000000008000000200200200000200000,

1000102010111020,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //m26

#SECTION
#BRANCHSTART p,75,90
#N
11121120,
11121120,
11121120,
1011102010101000,

0,
11121112,
0,
0, //n34

#E
11121120,
11121120,
1011102011102000,
1011102010101000,

0,
1010102210101020,
0,
0, //e34

#M
1011102011102000,
1011102011102000,
1011102011102000,
1011102010101000,

0,
1010102210101020,
0,
0, //m34

#SECTION
#BRANCHSTART p,75,90
#N
10001100,
1010001010222020,
10001100,
1010001022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202020, //n43

#E
10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202220, //e43

#M
10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011021020,
1022202011021020,
1022202011021020,
12212212,
2202202202202220, //m43

#SECTION
#BRANCHSTART p,75,90
#N
0,
00000110,
11121112,
11121000, //n47

#E
0,
00000110,
1011102011011020,
1011102210000000, //e47

#M
0,
00000110,
1011102011011020,
1011102210000000, //m47


#SECTION
#BRANCHSTART p,75,90
#N
11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n55

#E
11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e55

#M
1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m55

#SECTION
#BRANCHSTART p,75,90
#N
11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n63

#E
11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e63

#M
1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m63

#SECTION
#BRANCHSTART p,75,90
#N
1000100010222020,
00000110,
11101103,
0,

0,
0,
0, //n70

#E
1000100010222020,
0000000011111110,
1011100010100030,
0,

0,
0,
0, //e70

#M
1000100010222020,
0000000011121112,
1011100010100030,
0,

0,
0,
0, //m70
#BRANCHEND
#END


BALLOON:
SCOREINIT:
SCOREDIFF:
SCOREMODE:0

#START P2
0,
0,
0,

1000100010222020,
11120110,
1000100010111010, //6
3, //7

00000110,
11121112,
11121000, //10

#SECTION
#BRANCHSTART p,75,90
#N
11121120,
11121010,
11121111,
1022202010101010,

11121120,
11121010,
11121111,
1022202010101000, //n18

#E
11121120,
1011102010111000,
11121111,
1022202010111020,

11121120,
1011102010111000,
11121111,
1022202010111022, //e18

#M
1011102010102000,
1011102010111000,
1011102011011010,
1022202010111020,

1011102010102000,
1011102010111000,
1011102011011010,
1022202010111022, //m18

#SECTION
#BRANCHSTART p,75,90
#N
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000, //n26

#E
10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000,

10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //e26

#M
1000102010111020,
1101102010222020,
1101102010222020,
500000000000000000000008000000200200200000200000,

1000102010111020,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //m26

#SECTION
#BRANCHSTART p,75,90
#N
11121120,
11121120,
11121120,
1011102010101000,

0,
0,
11121112,
0, //n34

#E
11121120,
11121120,
1011102011102000,
1011102010101000,

0,
0,
1010102210101020,
0, //e34

#M
1011102011102000,
1011102011102000,
1011102011102000,
1011102010101000,

0,
0,
1010102210101020,
0, //m34

#SECTION
#BRANCHSTART p,75,90
#N
10001100,
1010001010222020,
10001100,
1010001022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202020, //n43

#E
10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202220, //e43

#M
10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011021020,
1022202011021020,
1022202011021020,
12212212,
2202202202202220, //m43

#SECTION
#BRANCHSTART p,75,90
#N
0,
00000110,
11121112,
11121000, //n47

#E
0,
00000110,
1011102011011020,
1011102210000000, //e47

#M
0,
00000110,
1011102011011020,
1011102210000000, //m47


#SECTION
#BRANCHSTART p,75,90
#N
11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n55

#E
11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e55

#M
1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m55

#SECTION
#BRANCHSTART p,75,90
#N
11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n63

#E
11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e63

#M
1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m63

#SECTION
#BRANCHSTART p,75,90
#N
1000100010222020,
00000110,
11101103,
0,

0,
0,
0, //n70

#E
1000100010222020,
0000000011111110,
1011100010100030,
0,

0,
0,
0, //e70

#M
1000100010222020,
0000000011121112,
1011100010100030,
0,

0,
0,
0, //m70
#BRANCHEND
#END


COURSE:Hard
LEVEL:7

STYLE:SINGLE
BALLOON:
SCOREINIT:570
SCOREDIFF:120
SCOREMODE:1

#START
0,
0,
0,

1000100010222020,
11120110,
1000100010111010, //6
3, //7

#SECTION
#BRANCHSTART p,65,80
#N
00000110,
11121112,
11121000, //n10

#E
00000110,
11121112,
11121000, //e10

#M
00000110,
11121112,
11121000, //m10

#SECTION
#BRANCHSTART p,65,80
#N
11121120,
11121010,
11121111,
1022202010101010,

11121120,
11121010,
11121111,
1022202010101000, //n18

#E
11121120,
1011102010111000,
11121111,
1022202010111020,

11121120,
1011102010111000,
11121111,
1022202010111022, //e18

#M
1011102010102000,
1011102010111000,
1011102011011010,
1022202010111020,

1011102010102000,
1011102010111000,
1011102011011010,
1022202010111022, //m18

#SECTION
#BRANCHSTART p,65,80
#N
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000, //n26

#E
10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000,

10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //e26

#M
1000102010111020,
1101102010222020,
1101102010222020,
500000000000000000000008000000200200200000200000,

1000102010111020,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //m26

#SECTION
#BRANCHSTART p,65,80
#N
11121120,
11121120,
11121120,
1011102010101000,

0,
11121112,
0,
0, //n34

#E
11121120,
11121120,
1011102011102000,
1011102010101000,

0,
1010102210101020,
0,
0, //e34

#M
1011102011102000,
1011102011102000,
1011102011102000,
1011102010101000,

0,
1010102210101020,
0,
0, //m34

#SECTION
#BRANCHSTART p,65,80
#N
10001100,
1010001010222020,
10001100,
1010001022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202020, //n43

#E
10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202220, //e43

#M
10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011021020,
1022202011021020,
1022202011021020,
12212212,
2202202202202220, //m43

#SECTION
#BRANCHSTART p,65,80
#N
0,
00000110,
11121112,
11121000, //n47

#E
0,
00000110,
1011102011011020,
1011102210000000, //e47

#M
0,
00000110,
1011102011011020,
1011102210000000, //m47


#SECTION
#BRANCHSTART p,65,80
#N
11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n55

#E
11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e55

#M
1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m55

#SECTION
#BRANCHSTART p,65,80
#N
11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n63

#E
11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e63

#M
1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m63

#SECTION
#BRANCHSTART p,65,80
#N
1000100010222020,
00000110,
11101103,
0,

0,
0,
0, //n70

#E
1000100010222020,
0000000011111110,
1011100010100030,
0,

0,
0,
0, //e70

#M
1000100010222020,
0000000011121112,
1011100010100030,
0,

0,
0,
0, //m70
#BRANCHEND
#END

STYLE:DOUBLE
BALLOON:
SCOREINIT:570
SCOREDIFF:120
SCOREMODE:1

#START P1
0,
0,
0,

1000100010222020,
11120110,
1000100010111010, //6
3, //7

#SECTION
#BRANCHSTART p,65,80
#N
00000110,
11121112,
11121000, //n10

#E
00000110,
11121112,
11121000, //e10

#M
00000110,
11121112,
11121000, //m10

#SECTION
#BRANCHSTART p,65,80
#N
11121120,
11121010,
11121111,
1022202010101010,

11121120,
11121010,
11121111,
1022202010101000, //n18

#E
11121120,
1011102010111000,
11121111,
1022202010111020,

11121120,
1011102010111000,
11121111,
1022202010111022, //e18

#M
1011102010102000,
1011102010111000,
1011102011011010,
1022202010111020,

1011102010102000,
1011102010111000,
1011102011011010,
1022202010111022, //m18

#SECTION
#BRANCHSTART p,65,80
#N
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000, //n26

#E
10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000,

10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //e26

#M
1000102010111020,
1101102010222020,
1101102010222020,
500000000000000000000008000000200200200000200000,

1000102010111020,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //m26

#SECTION
#BRANCHSTART p,65,80
#N
11121120,
11121120,
11121120,
1011102010101000,

0,
11121112,
0,
0, //n34

#E
11121120,
11121120,
1011102011102000,
1011102010101000,

0,
1010102210101020,
0,
0, //e34

#M
1011102011102000,
1011102011102000,
1011102011102000,
1011102010101000,

0,
1010102210101020,
0,
0, //m34

#SECTION
#BRANCHSTART p,65,80
#N
10001100,
1010001010222020,
10001100,
1010001022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202020, //n43

#E
10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202220, //e43

#M
10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011021020,
1022202011021020,
1022202011021020,
12212212,
2202202202202220, //m43

#SECTION
#BRANCHSTART p,65,80
#N
0,
00000110,
11121112,
11121000, //n47

#E
0,
00000110,
1011102011011020,
1011102210000000, //e47

#M
0,
00000110,
1011102011011020,
1011102210000000, //m47


#SECTION
#BRANCHSTART p,65,80
#N
11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n55

#E
11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e55

#M
1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m55

#SECTION
#BRANCHSTART p,65,80
#N
11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n63

#E
11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e63

#M
1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m63

#SECTION
#BRANCHSTART p,65,80
#N
1000100010222020,
00000110,
11101103,
0,

0,
0,
0, //n70

#E
1000100010222020,
0000000011111110,
1011100010100030,
0,

0,
0,
0, //e70

#M
1000100010222020,
0000000011121112,
1011100010100030,
0,

0,
0,
0, //m70
#BRANCHEND
#END


BALLOON:
SCOREINIT:570
SCOREDIFF:120
SCOREMODE:1

#START P2
0,
0,
0,

1000100010222020,
11120110,
1000100010111010, //6
3, //7

#SECTION
#BRANCHSTART p,65,80
#N
00000110,
11121112,
11121000, //n10

#E
00000110,
11121112,
11121000, //e10

#M
00000110,
11121112,
11121000, //m10

#SECTION
#BRANCHSTART p,65,80
#N
11121120,
11121010,
11121111,
1022202010101010,

11121120,
11121010,
11121111,
1022202010101000, //n18

#E
11121120,
1011102010111000,
11121111,
1022202010111020,

11121120,
1011102010111000,
11121111,
1022202010111022, //e18

#M
1011102010102000,
1011102010111000,
1011102011011010,
1022202010111020,

1011102010102000,
1011102010111000,
1011102011011010,
1022202010111022, //m18

#SECTION
#BRANCHSTART p,65,80
#N
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000, //n26

#E
10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000,

10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //e26

#M
1000102010111020,
1101102010222020,
1101102010222020,
500000000000000000000008000000200200200000200000,

1000102010111020,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //m26

#SECTION
#BRANCHSTART p,65,80
#N
11121120,
11121120,
11121120,
1011102010101000,

0,
0,
11121112,
0, //n34

#E
11121120,
11121120,
1011102011102000,
1011102010101000,

0,
0,
1010102210101020,
0, //e34

#M
1011102011102000,
1011102011102000,
1011102011102000,
1011102010101000,

0,
0,
1010102210101020,
0, //m34

#SECTION
#BRANCHSTART p,65,80
#N
10001100,
1010001010222020,
10001100,
1010001022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202020, //n43

#E
10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202220, //e43

#M
10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011021020,
1022202011021020,
1022202011021020,
12212212,
2202202202202220, //m43

#SECTION
#BRANCHSTART p,65,80
#N
0,
00000110,
11121112,
11121000, //n47

#E
0,
00000110,
1011102011011020,
1011102210000000, //e47

#M
0,
00000110,
1011102011011020,
1011102210000000, //m47


#SECTION
#BRANCHSTART p,65,80
#N
11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n55

#E
11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e55

#M
1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m55

#SECTION
#BRANCHSTART p,65,80
#N
11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n63

#E
11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e63

#M
1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m63

#SECTION
#BRANCHSTART p,65,80
#N
1000100010222020,
00000110,
11101103,
0,

0,
0,
0, //n70

#E
1000100010222020,
0000000011111110,
1011100010100030,
0,

0,
0,
0, //e70

#M
1000100010222020,
0000000011121112,
1011100010100030,
0,

0,
0,
0, //m70
#BRANCHEND
#END