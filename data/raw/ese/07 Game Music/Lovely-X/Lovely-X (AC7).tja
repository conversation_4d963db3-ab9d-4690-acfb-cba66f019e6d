//TJADB Project
TITLE:Lovely-X -AC7/PSP2 Chart-
TITLEJA:ラブリーX
SUBTITLE:--<PERSON> feat. <PERSON><PERSON><PERSON><PERSON>/NEW RALLY-X
SUBTITLEJA:AC7/PSP2譜面
BPM:134
WAVE:Lovely-X.ogg
OFFSET:-2.146
DEMOSTART:34.375

//Donderful/Oni: 9* in CS1, 8* in AC4-6, 7* in AC7/PSP
//No Easy chart in CS1/AC4-6.
//Branches location in Hard based on PSP2 Chart.

COURSE:Oni
LEVEL:7

STYLE:SINGLE
BALLOON:
SCOREINIT:700
SCOREDIFF:140

//PSP2 Score. AC7 uses Donderful Scoring

#START
0,
0,
0,

1000100010222020,
11120110,
1000100010111010, //6
3, //7

00000110,
11121112,
11121000, //10

1011102010102000,
1011102010111000,
1011102011011010, //13
1022202010111020,

1011102010102000,
1011102010111000,
1011102011011010,
1022202010111022, //m18

#GOGOSTART
1000102010111020,
1101102010222020,
1101102010222020,
500000000000000000000008000000200200200000200000,

1000102010111020,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //m26

1011102011102000,
1011102011102000,
1011102011102000,
1011102010101000,
#GOGOEND

0,
1010102210101020,
0,
0, //m34

10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011021020,
1022202011021020,
1022202011021020,
12212212,
2202202202202220, //m43

0,
00000110,
1011102011011020,
1011102210000000, //m47

1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m55

1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m63

1000100010222020,
0000000011121112,
1011100010100030,
0,

0,
0,
0, //m70
#END


STYLE:DOUBLE
BALLOON:
SCOREINIT:700
SCOREDIFF:140

#START P1
0,
0,
0,

1000100010222020,
11120110,
1000100010111010, //6
3, //7

00000110,
11121112,
11121000, //10

1011102010102000,
1011102010111000,
1011102011011010, //13
1022202010111020,

1011102010102000,
1011102010111000,
1011102011011010,
1022202010111022, //m18

#GOGOSTART
1000102010111020,
1101102010222020,
1101102010222020,
500000000000000000000008000000200200200000200000,

1000102010111020,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //m26

1011102011102000,
1011102011102000,
1011102011102000,
1011102010101000,
#GOGOEND

0,
1010102210101020,
0,
0, //m34

10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011021020,
1022202011021020,
1022202011021020,
12212212,
2202202202202220, //m43

0,
00000110,
1011102011011020,
1011102210000000, //m47

1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m55

1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m63

1000100010222020,
0000000011121112,
1011100010100030,
0,

0,
0,
0, //m70
#END

BALLOON:
SCOREINIT:700
SCOREDIFF:140

#START P2
0,
0,
0,

1000100010222020,
11120110,
1000100010111010, //6
3, //7

00000110,
11121112,
11121000, //10

1011102010102000,
1011102010111000,
1011102011011010, //13
1022202010111020,

1011102010102000,
1011102010111000,
1011102011011010,
1022202010111022, //m18

#GOGOSTART
1000102010111020,
1101102010222020,
1101102010222020,
500000000000000000000008000000200200200000200000,

1000102010111020,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //m26

1011102011102000,
1011102011102000,
1011102011102000,
1011102010101000,
#GOGOEND

0,
0,
1010102210101020,
0, //m34

10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011021020,
1022202011021020,
1022202011021020,
12212212,
2202202202202220, //m43

0,
00000110,
1011102011011020,
1011102210000000, //m47

1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m55

1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m63

1000100010222020,
0000000011121112,
1011100010100030,
0,

0,
0,
0, //m70
#END


COURSE:Hard
LEVEL:6

STYLE:SINGLE
BALLOON:
SCOREINIT:500
SCOREDIFF:120

//PSP2 Score. 490/160 in AC7

#START
0,
0,
0,

1000100010222020,
11120110,
1000100010111010, //6
3, //7

00000110,
11121112,
11121000, //10

11121120,
11121010,
11121111, //13

#SECTION
#BRANCHSTART p,60,80
#N
1022202010101010,

11121120,
11121010,
11121111,
1022202010101000, //n18

#GOGOSTART
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

#E
1022202010111020,

11121120,
1011102010111000,
11121111,
1022202010111022, //e18

#GOGOSTART
10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000,

#M
1022202010111020,

1011102010102000,
1011102010111000,
1011102011011010,
1022202010111022, //m18

#GOGOSTART
1000102010111020,
1101102010222020,
1101102010222020,
500000000000000000000008000000200200200000200000,

#SECTION
#BRANCHSTART p,60,80
#N
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000, //n26

11121120,
11121120,
11121120,
1011102010101000,
#GOGOEND

0,

#E
10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //e26

11121120,
11121120,
1011102011102000,
1011102010101000,
#GOGOEND

0,

#M
1000102010111020,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //m26

1011102011102000,
1011102011102000,
1011102011102000,
1011102010101000,
#GOGOEND

0,

#SECTION
#BRANCHSTART p,60,80
#N
11121112,
0,
0, //n34

10001100,
1010001010222020,
10001100,
1010001022022020,

1022202011011020,
1022202011011020,
1022202011011020,

#E
1010102210101020,
0,
0, //e34

10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011011020,
1022202011011020,
1022202011011020,

#M
1010102210101020,
0,
0, //m34

10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011021020,
1022202011021020,
1022202011021020,

#SECTION
#BRANCHSTART p,60,80
#N
12212212,
2202202202202020, //n43

0,
00000110,
11121112,
11121000, //n47

11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n55

11121112,

#E
12212212,
2202202202202220, //e43

0,
00000110,
1011102011011020,
1011102210000000, //e47

11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e55

11121112,

#M
12212212,
2202202202202220, //m43

0,
00000110,
1011102011011020,
1011102210000000, //m47

1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m55

1011102011011020,

#SECTION
#BRANCHSTART p,60,80
#N
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n63

1000100010222020,
00000110,

#E
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e63

1000100010222020,
0000000011111110,

#M
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m63

1000100010222020,
0000000011121112,

#SECTION
#BRANCHSTART p,60,80
#N
11101103,
0,

0,
0,
0, //n70

#E
1011100010100030,
0,

0,
0,
0, //e70

#M

1011100010100030,
0,

0,
0,
0, //m70
#BRANCHEND
#END

STYLE:DOUBLE
BALLOON:
SCOREINIT:500
SCOREDIFF:120

#START P1
0,
0,
0,

1000100010222020,
11120110,
1000100010111010, //6
3, //7

00000110,
11121112,
11121000, //10

11121120,
11121010,
11121111, //13

#SECTION
#BRANCHSTART p,60,80
#N
1022202010101010,

11121120,
11121010,
11121111,
1022202010101000, //n18

#GOGOSTART
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

#E
1022202010111020,

11121120,
1011102010111000,
11121111,
1022202010111022, //e18

#GOGOSTART
10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000,

#M
1022202010111020,

1011102010102000,
1011102010111000,
1011102011011010,
1022202010111022, //m18

#GOGOSTART
1000102010111020,
1101102010222020,
1101102010222020,
500000000000000000000008000000200200200000200000,

#SECTION
#BRANCHSTART p,60,80
#N
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000, //n26

11121120,
11121120,
11121120,
1011102010101000,
#GOGOEND

0,

#E
10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //e26

11121120,
11121120,
1011102011102000,
1011102010101000,
#GOGOEND

0,

#M
1000102010111020,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //m26

1011102011102000,
1011102011102000,
1011102011102000,
1011102010101000,
#GOGOEND

0,

#SECTION
#BRANCHSTART p,60,80
#N
11121112,
0,
0, //n34

10001100,
1010001010222020,
10001100,
1010001022022020,

1022202011011020,
1022202011011020,
1022202011011020,

#E
1010102210101020,
0,
0, //e34

10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011011020,
1022202011011020,
1022202011011020,

#M
1010102210101020,
0,
0, //m34

10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011021020,
1022202011021020,
1022202011021020,

#SECTION
#BRANCHSTART p,60,80
#N
12212212,
2202202202202020, //n43

0,
00000110,
11121112,
11121000, //n47

11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n55

11121112,

#E
12212212,
2202202202202220, //e43

0,
00000110,
1011102011011020,
1011102210000000, //e47

11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e55

11121112,

#M
12212212,
2202202202202220, //m43

0,
00000110,
1011102011011020,
1011102210000000, //m47

1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m55

1011102011011020,

#SECTION
#BRANCHSTART p,60,80
#N
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n63

1000100010222020,
00000110,

#E
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e63

1000100010222020,
0000000011111110,

#M
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m63

1000100010222020,
0000000011121112,

#SECTION
#BRANCHSTART p,60,80
#N
11101103,
0,

0,
0,
0, //n70

#E
1011100010100030,
0,

0,
0,
0, //e70

#M

1011100010100030,
0,

0,
0,
0, //m70
#BRANCHEND
#END

BALLOON:
SCOREINIT:500
SCOREDIFF:120

#START P2
0,
0,
0,

1000100010222020,
11120110,
1000100010111010, //6
3, //7

00000110,
11121112,
11121000, //10

11121120,
11121010,
11121111, //13

#SECTION
#BRANCHSTART p,60,80
#N
1022202010101010,

11121120,
11121010,
11121111,
1022202010101000, //n18

#GOGOSTART
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

#E
1022202010111020,

11121120,
1011102010111000,
11121111,
1022202010111022, //e18

#GOGOSTART
10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000,

#M
1022202010111020,

1011102010102000,
1011102010111000,
1011102011011010,
1022202010111022, //m18

#GOGOSTART
1000102010111020,
1101102010222020,
1101102010222020,
500000000000000000000008000000200200200000200000,

#SECTION
#BRANCHSTART p,60,80
#N
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000, //n26

11121120,
11121120,
11121120,
1011102010101000,
#GOGOEND

0,

#E
10121112,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //e26

11121120,
11121120,
1011102011102000,
1011102010101000,
#GOGOEND

0,

#M
1000102010111020,
1101102010222020,
1010102210221000,
500000000000000000000008000000200200200000200000, //m26

1011102011102000,
1011102011102000,
1011102011102000,
1011102010101000,
#GOGOEND

0,

#SECTION
#BRANCHSTART p,60,80
#N
0,
11121112,
0, //n34

10001100,
1010001010222020,
10001100,
1010001022022020,

1022202011011020,
1022202011011020,
1022202011011020,

#E
0,
1010102210101020,
0, //e34

10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011011020,
1022202011011020,
1022202011011020,

#M
0,
1010102210101020,
0, //m34

10001100,
1011101010222020,
10001100,
1211101022022020,

1022202011021020,
1022202011021020,
1022202011021020,

#SECTION
#BRANCHSTART p,60,80
#N
12212212,
2202202202202020, //n43

0,
00000110,
11121112,
11121000, //n47

11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n55

11121112,

#E
12212212,
2202202202202220, //e43

0,
00000110,
1011102011011020,
1011102210000000, //e47

11121112,
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e55

11121112,

#M
12212212,
2202202202202220, //m43

0,
00000110,
1011102011011020,
1011102210000000, //m47

1011102011011020,
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m55

1011102011011020,

#SECTION
#BRANCHSTART p,60,80
#N
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000, //n63

1000100010222020,
00000110,

#E
1011102010102000,
11121112,
1022202011011010,

11121112,
1011102010102000,
11121112,
1022202010111022, //e63

1000100010222020,
0000000011111110,

#M
1011102010102000,
1011102011011020,
1022202011011010,

1011102011011020,
1011102010102000,
1011102011011020,
1022202010111022, //m63

1000100010222020,
0000000011121112,

#SECTION
#BRANCHSTART p,60,80
#N
11101103,
0,

0,
0,
0, //n70

#E
1011100010100030,
0,

0,
0,
0, //e70

#M

1011100010100030,
0,

0,
0,
0, //m70
#BRANCHEND
#END


COURSE:Normal
LEVEL:6

STYLE:SINGLE
BALLOON:
SCOREINIT:480
SCOREDIFF:120

//PSP2 Score. AC7 Score 500/120

#START
0,
0,
0,

1000100010222020,
11120110,
1000100010111010,

3,
00000110,
11121112,
11121000,

11121120,
11121010,
11121111,
1022202010101010,

11121120,
11121010,
11121111,
1022202010101000,

#GOGOSTART
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

11121120,
11121120,
11121120,
1011102010101000,
#GOGOEND

0,
11121112,
0,
0,

10001100,
1010001010222020,
10001100,
1010001022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202020,

0,
00000110,
11121112,
11121000,

11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000,

11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000,

1000100010222020,
00000110,
11101103,
0,

0,
0,
0,
#END

STYLE:DOUBLE
BALLOON:
SCOREINIT:480
SCOREDIFF:120

//PSP2 Score. AC7 Score 500/120

#START P1
0,
0,
0,

1000100010222020,
11120110,
1000100010111010,

3,
00000110,
11121112,
11121000,

11121120,
11121010,
11121111,
1022202010101010,

11121120,
11121010,
11121111,
1022202010101000,

#GOGOSTART
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

11121120,
11121120,
11121120,
1011102010101000,
#GOGOEND

0,
11121112,
0,
0,

10001100,
1010001010222020,
10001100,
1010001022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202020,

0,
00000110,
11121112,
11121000,

11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000,

11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000,

1000100010222020,
00000110,
11101103,
0,

0,
0,
0,
#END


BALLOON:
SCOREINIT:480
SCOREDIFF:120

//PSP2 Score. AC7 Score 500/120

#START P2
0,
0,
0,

1000100010222020,
11120110,
1000100010111010,

3,
00000110,
11121112,
11121000,

11121120,
11121010,
11121111,
1022202010101010,

11121120,
11121010,
11121111,
1022202010101000,

#GOGOSTART
10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

10121112,
1010101010222020,
13,
500000000000000000000008000000200200200000200000,

11121120,
11121120,
11121120,
1011102010101000,
#GOGOEND

0,
0,
11121112,
0,

10001100,
1010001010222020,
10001100,
1010001022022020,

1022202011011020,
1022202011011020,
1022202011011020,
12212212,
2202202202202020,

0,
00000110,
11121112,
11121000,

11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000,

11121112,
11121120,
11121112,
1022202010101010,

11121112,
11121120,
11121112,
1022202010101000,

1000100010222020,
00000110,
11101103,
0,

0,
0,
0,
#END



COURSE:Easy
LEVEL:5
BALLOON:
SCOREINIT:500
SCOREDIFF:150

//PSP2 Score. AC7 Score 550/150

#START
0,
0,
0,

10000220,
10110000,
10000220,

3,
00000110,
11110000,
11110000,

11101000,
11100020,
11101000,
2022200000000000,

11101000,
11100020,
11101000,
1011100010000000,

#GOGOSTART
10101110,
1110,
1130,
500000000000000000000008000000000000000000000000,

10101110,
1110,
13,
500000000000000000000008000000000000000000000000,

11002200,
11002200,
11002200,
500000000000000000000000000000000008000000000000,
#GOGOEND

0001,
10011110,
10000110,
1,

10001100,
10111000,
10001100,
10111000,

11001100,
11002200,
11,
1,
0,

3,
00000110,
11110000,
11110000,

11101000,
11100020,
11101000,
2022200000000000,

11101000,
11100020,
11101000,
1011100010000000,

11101000,
11100020,
11101000,
2022200000000000,


11002200,
11002200,
11002200,
500000000000000000000000000000000008000000000000,

1000100010222020,
00000110,
11101103,
0,

0,
0,
0,
#END