//TJADB Project
TITLE:<PERSON> <PERSON><PERSON> o <PERSON>ku -76<PERSON><PERSON><PERSON> ALLSTARS Version-
TITLEJA:エージェント夜を往く
SUBTITLE:--<PERSON> AI-CUE feat. 765PRO ALLSTARS/THE iDOLM@STER
BPM:178
WAVE:<PERSON> <PERSON><PERSON> o <PERSON>ku -765<PERSON><PERSON> ALLSTARS Version-.ogg
OFFSET:-4.241
DEMOSTART:55.475

//Vita MS Chart

COURSE:Oni
LEVEL:7
BALLOON:30
SCOREINIT:470,1540
SCOREDIFF:118

#START
11022011,
01200000,
11022011,
01200000,

22011022,
01200000,
22021112,
03403400, //8

11211121,
01211121,
10121021,
0010201011102220,

11211121,
01211121,
10120121,
0010201011201120, //16

1010201110102011,
1010201110112210,
11211211,
2010102010102220,
30300003,
0300, //22

11212122,
0010201020102220,
11212122,
0010201011111110,

1010201000222010,
0010201000222010,
1010201000222010,
10110110, //30

1010201000222010,
0010201000222010,
1010201000222010,
10110110,

12121211,
0010201010102220,
5,
000000000000000000000000000000000008000000000000, //38

#GOGOSTART
1010201011102010,
0010201011201120,
1010201011201010,
0011201011221011,

1020111010201110,
1020111011222010,
11210120,
01121122, //46

1010201011102010,
0010201011201120,
1010201011201010,
0011201011221011,

1020111010201110,
1020111011222011,
11022011,
#MEASURE 3/4
012000, //54
#GOGOEND

#MEASURE 4/4
1001100000100010,
1001100000101101,
1001100000100010,
1001100000101101,
7,
000000000000000000000000000008000000000000000000, //60

11022011,
01200000,
11022011,
01200000,

22011022,
01200000,
22021112,
03403400, //68

#GOGOSTART
1010201011102010,
0010201011201120,
1010201011201010,
0010201011221011,

1020112010201120,
1020112011221020,
11210120,
03343344, //76

1010201011102010,
0010201011201120,
1010201011201010,
0011201011221011,

1020112010201120,
1020112011201111,
11022011,
#MEASURE 3/4
001020001110, //84

#MEASURE 4/4
33044033,
#MEASURE 3/4
003040111010,
#MEASURE 4/4
33044033,
04400000,
#GOGOEND
0, //89
#END


COURSE:Hard
LEVEL:5
BALLOON:20
SCOREINIT:460,2040
SCOREDIFF:125

#START
11011011,
01104040,
11011011,
01104040,

22011022,
01104040,
22021111,
03403400, //8

11201121,
01212020,
1100,
0,

11201121,
01212022,
0,
0, //16

10210121,
01212020,
11122211,
12221010,
40400004,
0400, //22

10210121,
01212020,
10210121,
02120000,

10210120,
10210220,
10210120,
10220220, //30

10210120,
10210220,
10210120,
10220220,

10210121,
01112222,
500000000000000000000000000000000008000000000000,
3333, //38

#GOGOSTART
00404001,
01212020,
10210121,
01212020,

10210121,
01212020,
10210120,
03333344, //46

00404001,
01212020,
10210121,
01212020,

10210121,
01212020,
33033044,
#MEASURE 3/4
044000, //54
#GOGOEND

#MEASURE 4/4
0,
10100101,
10100101,
10100101,
7,
000000000000000000000000000008000000000000000000, //60

11011011,
01104040,
11011011,
01104040,

22011022,
01104040,
22021111,
03403400, //68

#GOGOSTART
00404001,
01212020,
10210121,
01212020,

10210121,
01212020,
10210120,
03333344, //76

00404001,
01212020,
10210121,
01212020,

10210121,
01212020,
33033044,
#MEASURE 3/4
044000, //84

#MEASURE 4/4
33033044,
#MEASURE 3/4
044000,
#MEASURE 4/4
33033044,
04400000,
#GOGOEND
0, //89
#END


COURSE:Normal
LEVEL:4
BALLOON:15
SCOREINIT:550,3220
SCOREDIFF:163

#START
0,
0044,
0,
0044,

0,
0044,
0,
03303300, //8

10101011,
1111,
1100,
0,

10101110,
1122,
0,
0, //16

10101011,
1122,
30030030,
03003000,
40400004,
0400, //22

10101011,
1122,
10101011,
10220000, 

10010020,
10010020,
10010020,
10010020, //30

10010020,
10010020,
10010020,
10010020,

0202,
1111,
500000000000000000000000000000000008000000000000,
3333, //38

#GOGOSTART
0440,
1122,
1122,
1120,

1111,
1222,
1111,
000000600000000000000000000000000000000008000000, //46

0440,
1122,
1122,
1110,

1111,
1222,
33033044,
#MEASURE 3/4
044000, //54
#GOGOEND

#MEASURE 4/4
0,
0,
0,
0,
7,
000000000000000000000000000008000000000000000000, //60

0,
0044,
0,
0044,

0,
0044,
0,
03303300, //68

#GOGOSTART
0440,
1122,
1122,
1110,

1111,
1222,
1111,
000000600000000000000000000000000000000008000000, //76

0440,
1122,
1122,
1110,

1111,
1222,
33033044,
#MEASURE 3/4
044000, //84

#MEASURE 4/4
33033044,
#MEASURE 3/4
044000,
#MEASURE 4/4
33033044,
04400000,
#GOGOEND
0, //89
#END


COURSE:Easy
LEVEL:4
BALLOON:13
SCOREINIT:470,4250
SCOREDIFF:165

#START
0,
0044,
0,
0044,

0,
0044,
0,
03303300, //8

11,
1110,
1100,
0,

11,
10101003,
0,
0, //16

1110,
2220,
30030030,
03003000,
40400004,
0400, //22

11,
1110,
1011,
03000000,

10010020,
10010020,
10010020,
10010020, //30

10010020,
10010020,
10010020,
10010020,

0101,
0101,
500000000000000000000000000000000008000000000000,
3333, //38

#GOGOSTART
0440,
1110,
11,
1110,

11,
1110,
1111,
000000600000000000000000000000000000000008000000, //46

0440,
1110,
11,
1110,

11,
1110,
30030030,
#MEASURE 3/4
030000, //54
#GOGOEND

#MEASURE 4/4
0,
0,
0,
0,
7,
000000000000000000000000000008000000000000000000, //60

0,
0044,
0,
0044,

0,
0044,
0,
03303300, //68

#GOGOSTART
0440,
1110,
11,
1110,

11,
1110,
1111,
000000600000000000000000000000000000000008000000, //76

0440,
1110,
11,
1110,

11,
1110,
30030030,
#MEASURE 3/4
030000, //84

#MEASURE 4/4
30030030,
#MEASURE 3/4
030000,
#MEASURE 4/4
30030030,
03000000,
#GOGOEND
0, //89
#END
