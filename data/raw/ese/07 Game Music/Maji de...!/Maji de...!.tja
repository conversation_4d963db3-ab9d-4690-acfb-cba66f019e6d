//TJADB Project
TITLE:Maji de...⁉
TITLEJA:マジで…！？
SUBTITLE:--<PERSON><PERSON><PERSON><PERSON> feat. 765PRO ALLSTARS/THE iDOLM@STER MUST SONGS： Blue Album
BPM:140
WAVE:Maji de...!.ogg
OFFSET:-1.871
DEMOSTART:57.147

//Shinuchi: 5940/4430/2400/1590

COURSE:Oni
LEVEL:8
BALLOON:13,13
SCOREINIT:500,1910
SCOREDIFF:133

#START
#BARLINEOFF
#SCROLL 3
0003,

#BARLINEON
#SCROLL 1
2020202020220010,
2020202020222020,
2020202020220010,
2222, //5

500000000000000000000000000008000000000000100000,
#BARLINEOFF
#SCROLL 3
4, //7

#BARLINEON
#SCROLL 1
#GOGOSTART
03030303,
03030444,
03030303,
43434343, //11

03030303,
03030444,
03030303,
#GO<PERSON>OEND
2222, //15

#GOGOSTART
11111030
#GOGOEND
00112010,
2002002011010000,
#GOGOSTART
11111030
#GOGOEND
00112010,
2002002011010000, //19

#GOGOSTART
22222030
#GOGOEND
00112010,
2002002011010000,
#GOGOSTART
22222030
#GOGOEND
00112010, //23

2002002020003000,
1011201010112010,
1011201010222010,
1011201010112010, //27

1011201010222010,
1010101010111010,
1010101010111010,
#SCROLL 1.5
11111110
#SCROLL 2
11111110,
#SCROLL 3
2222111122221110, //31

#BARLINEOFF
0,
#BARLINEON
7008, //33

#SCROLL 1
#GOGOSTART
10221022102100
#SCROLL 3
30,
#SCROLL 1
20221022102100
#SCROLL 3
40,
#SCROLL 1
1022201010222010,
1122112211221120, //37

10221022102100
#SCROLL 3
30,
#SCROLL 1
20221022102100
#SCROLL 3
40,
#SCROLL 1
33044400,
#GOGOEND
#SCROLL 2
500008000000500008000000500008000000
#SCROLL 4
#GOGOSTART
400000000000, //41

#SCROLL 1
10221022102100
#SCROLL 3
30,
#SCROLL 1
20221022102100
#SCROLL 3
40,
#SCROLL 1
1022201010222010,
1122112211221120, //45

10221021102100
#SCROLL 3
30,
#SCROLL 1
20221021102100
#SCROLL 3
40,
#SCROLL 1
33044400,
#GOGOEND
#SCROLL 2
500008000000500008000000500008000000
#SCROLL 4
#GOGOSTART
400000000000, //49
#GOGOEND

#SCROLL 2
500008000000500008000000500008000000
#SCROLL 4
#GOGOSTART
400000000000,
#GOGOEND
#SCROLL 2
300000300000000000000000600000000008000000300000,
3
#SCROLL 4
#GOGOSTART
4, //52
#GOGOEND

#SCROLL 1
2022202220202020,
1022102110221020,
2022202220202020,
2000100000200200, //56

#GOGOSTART
03030303,
03030444,
03030303,
1122112211221120,
#GOGOEND
1111101020222220,
#SCROLL 3
7008, //62

#SCROLL 1
#GOGOSTART
10112012201200
#SCROLL 3
30,
#SCROLL 1
20221021102100
#SCROLL 3
40,
#SCROLL 1
1022201010222010,
1122112211221120, //66

10112012201200
#SCROLL 3
30,
#SCROLL 1
20221021102100
#SCROLL 3
40,
#SCROLL 1
33044400,
#GOGOEND
#SCROLL 2
500008000000500008000000500008000000
#SCROLL 4
#GOGOSTART
400000000000, //70
#GOGOEND

#SCROLL 2
500008000000500008000000500008000000
#SCROLL 4
#GOGOSTART
400000000000,
#GOGOEND
#SCROLL 2
300000300000000000000000600000000008000000300000,
3
#SCROLL 4
#GOGOSTART
4,
#GOGOEND
#BARLINEOFF
0, //74
#END


COURSE:Hard
LEVEL:6
BALLOON:13,13
SCOREINIT:580,3140
SCOREDIFF:165

#START
#BARLINEOFF
0,
#BARLINEON
0202,
0000200000222020,
0202,
1111, //5

500000000000000000000000000008000000000000000000,
#BARLINEOFF
#SCROLL 2
4, //7

#BARLINEON
#SCROLL 1
#GOGOSTART
03030303,
03030444,
03030303,
43434343, //11

03030303,
03030444,
03030304,
#GOGOEND
0, //15

#GOGOSTART
10111000
#GOGOEND
00000000,
1001001011010000,
#GOGOSTART
10111000
#GOGOEND
00000000,
1001001011010000, //19

#GOGOSTART
22022000
#GOGOEND
00000000,
2002002022020000,
#GOGOSTART
22022000
#GOGOEND
00000000,
2002002020003000, //23

10201120,
10201220,
10201120,
10201220, //27

11111111,
11111111,
1110111011103000,
#SCROLL 2
600000000000000000000000000000000000000008000000, //31x

#BARLINEOFF
0,
#SCROLL 3
#BARLINEON
7008, //33

#SCROLL 1
#GOGOSTART
11101010200200
#SCROLL 2
30,
#SCROLL 1
00222020100100
#SCROLL 2
40,
#SCROLL 1
1110100022202000,
3344, //37

11101010200200
#SCROLL 2
30,
#SCROLL 1
00222020100100
#SCROLL 2
40,
#SCROLL 1
11022200,
#GOGOEND
500008000000500008000000500008000000
#SCROLL 2
#GOGOSTART
400000000000, //41

#SCROLL 1
10111010200200
#SCROLL 2
30,
#SCROLL 1
00222020100100
#SCROLL 2
40,
#SCROLL 1
1110100022202000,
3344, //45

11101010200200
#SCROLL 2
30,
#SCROLL 1
00222020100100
#SCROLL 2
40,
#SCROLL 1
11022200,
#GOGOEND
500008000000500008000000500008000000
#SCROLL 2
#GOGOSTART
400000000000, //49
#GOGOEND

#SCROLL 1
500008000000500008000000500008000000
#SCROLL 2
#GOGOSTART
400000000000,
#GOGOEND
#SCROLL 1
5,
000008000000000000000000
#SCROLL 2
#GOGOSTART
400000000000000000000000, //52
#GOGOEND

#SCROLL 1
00200220,
00200220,
00200220,
2000200000200200, //56

#GOGOSTART
03030303,
03030444,
03030303,
43434343, //60
#GOGOEND

0,
#SCROLL 3
7008, //62

#SCROLL 1
#GOGOSTART
11101010200200
#SCROLL 2
30,
#SCROLL 1
00222020100100
#SCROLL 2
40,
#SCROLL 1
1110100022202000,
3344, //66

11101010200200
#SCROLL 2
30,
#SCROLL 1
00222020100100
#SCROLL 2
40,
#SCROLL 1
11022200,
#GOGOEND
500008000000500008000000500008000000
#SCROLL 2
#GOGOSTART
400000000000, //70
#GOGOEND

#SCROLL 1
500008000000500008000000500008000000
#SCROLL 2
#GOGOSTART
400000000000,
#GOGOEND
#SCROLL 1
6,
000008000000000000000000
#SCROLL 3
#GOGOSTART
400000000000000000000000,
#GOGOEND
#BARLINEOFF
0, //74
#END


COURSE:Normal
LEVEL:5
BALLOON:8,8
SCOREINIT:790,7130
SCOREDIFF:320

#START
#BARLINEOFF
0,
#BARLINEON
0,
0,
0,
1111, //5

500000000000000000000000000008000000000000000000,
#SCROLL 2
#BARLINEOFF
4, //7

#BARLINEON
#SCROLL 1
#GOGOSTART
03030303,
03030444,
03030303,
4444,

03030303,
03030444,
03030304,
#GOGOEND
0, //15

#GOGOSTART
11
#GOGOEND
00,
11,
#GOGOSTART
11
#GOGOEND
00,
11, //19

#GOGOSTART
22
#GOGOEND
00,
22,
#GOGOSTART
22
#GOGOEND
00,
2021, //23

10101110,
10102220,
10101110,
20202220, //27

11101110,
22202220,
11111130,
#SCROLL 2
600000000000000000000000000000000000000008000000,
#BARLINEOFF
0,
#BARLINEON
9000000009008000, //33

#SCROLL 1
#GOGOSTART
0000000
#SCROLL 2
3,
#SCROLL 1
0000000
#SCROLL 2
4,
#SCROLL 1
0,
3344, //37

0000000
#SCROLL 2
3,
#SCROLL 1
0000000
#SCROLL 2
4,
#SCROLL 1
0,
#GOGOEND
500008000000500008000000500008000000
#SCROLL 2
#GOGOSTART
400000000000, //41

#SCROLL 1
0000000
#SCROLL 2
3,
#SCROLL 1
0000000
#SCROLL 2
4,
#SCROLL 1
0,
3344, //45

0000000
#SCROLL 2
3,
#SCROLL 1
0000000
#SCROLL 2
4,
#SCROLL 1
0,
#GOGOEND
500008000000500008000000500008000000
#SCROLL 2
#GOGOSTART
400000000000, //49
#GOGOEND

#SCROLL 1
500008000000500008000000500008000000
#SCROLL 2
#GOGOSTART
400000000000,
#GOGOEND
#SCROLL 1
5,
000008000000000000000000
#SCROLL 2
#GOGOSTART
400000000000000000000000, //52
#GOGOEND

#SCROLL 1
0,
0,
0,
0, //56

#GOGOSTART
03030303,
03030444,
03030303,
4444, //60
#GOGOEND

0,
#SCROLL 2
9000000009008000, //62

#SCROLL 1
#GOGOSTART
0000000
#SCROLL 2
3,
#SCROLL 1
0000000
#SCROLL 2
4,
#SCROLL 1
0,
3344, //66

0000000
#SCROLL 2
3,
#SCROLL 1
0000000
#SCROLL 2
4,
#SCROLL 1
0,
#GOGOEND
500008000000500008000000500008000000
#SCROLL 2
#GOGOSTART
400000000000, //70
#GOGOEND

#SCROLL 1
500008000000500008000000500008000000
#SCROLL 2
#GOGOSTART
400000000000,
#GOGOEND
#SCROLL 1
6,
000008000000000000000000
#SCROLL 2.5
#GOGOSTART
400000000000000000000000,
#GOGOEND
#BARLINEOFF
0, //74
#END


COURSE:Easy
LEVEL:3
BALLOON:7,7,7
SCOREINIT:610,9910
SCOREDIFF:338

#START
#BARLINEOFF
0,
#BARLINEON
0,
0,
0,
0, //5

500000000000000000000000000008000000000000000000,
#BARLINEOFF
#SCROLL 1.5
4, //7

#BARLINEON
#SCROLL 1
#GOGOSTART
03030303,
03030000,
03030303,
0, //11

03030303,
03030000,
03030304,
#GOGOEND
0, //15

#GOGOSTART
11
#GOGOEND
00,
11,
#GOGOSTART
11
#GOGOEND
00,
11, //19

#GOGOSTART
22
#GOGOEND
00,
22,
#GOGOSTART
22
#GOGOEND
00,
22, //23

11,
1110,
11,
2220, //27

1111,
2222,
1212,
#SCROLL 1.5
600000000000000000000000000000000000000008000000, //31

#BARLINEOFF
0,
#SCROLL 2
#BARLINEON
9000000009008000, //33

#SCROLL 1
#GOGOSTART
0000000
#SCROLL 1.5
3,
#SCROLL 1
0000000
#SCROLL 1.5
4,
#SCROLL 1
0,
3344, //37

0000000
#SCROLL 1.5
3,
#SCROLL 1
0000000
#SCROLL 1.5
4,
#SCROLL 1
0,
#GOGOEND
500000000000000000000008000000000000
#SCROLL 1.5
#GOGOSTART
400000000000, //41

#SCROLL 1
0000000
#SCROLL 1.5
3,
#SCROLL 1
0000000
#SCROLL 1.5
4,
#SCROLL 1
0,
3344, //45

0000000
#SCROLL 1.5
3,
#SCROLL 1
0000000
#SCROLL 1.5
4,
#SCROLL 1
0,
#GOGOEND
500000000000000000000008000000000000
#SCROLL 1.5
#GOGOSTART
400000000000, //49
#GOGOEND

#SCROLL 1
500000000000000000000008000000000000
#SCROLL 1.5
#GOGOSTART
400000000000,
#GOGOEND
#SCROLL 1
5,
000008000000000000000000
#SCROLL 1.5
#GOGOSTART
400000000000000000000000, //52
#GOGOEND

#SCROLL 1
0,
0,
0,
0, //56

#GOGOSTART
03030303,
03030000,
03030303,
7008, //60
#GOGOEND

0,
#SCROLL 2
9000000009008000, //62

#SCROLL 1
#GOGOSTART
0000000
#SCROLL 1.5
3,
#SCROLL 1
0000000
#SCROLL 1.5
4,
#SCROLL 1
0,
3344, //66

0000000
#SCROLL 1.5
3,
#SCROLL 1
0000000
#SCROLL 1.5
4,
#SCROLL 1
0,
#GOGOEND
500000000000000000000008000000000000
#SCROLL 1.5
#GOGOSTART
400000000000, //70
#GOGOEND

#SCROLL 1
500000000000000000000008000000000000
#SCROLL 1.5
#GOGOSTART
400000000000,
#GOGOEND
#SCROLL 1
6,
000008000000000000000000
#SCROLL 2.5
#GOGOSTART
400000000000000000000000,
#GOGOEND
#BARLINEOFF
0, //74
#END