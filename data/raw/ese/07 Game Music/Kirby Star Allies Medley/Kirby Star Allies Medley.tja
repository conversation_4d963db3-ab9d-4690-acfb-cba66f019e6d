//TJADB Project
TITLE:Kirby Star Allies Medley
TITLEJA:星のカービィ　スターアライズ　メドレー
SUBTITLE:--
SUBTITLEJA:
BPM:116
WAVE:Kirby Star Allies Medley.ogg
OFFSET:-1.206
DEMOSTART:56.050


COURSE:Oni
LEVEL:8
BALLOON:6,16,16,14
SCOREINIT:480,1430
SCOREDIFF:118

#START
3003004000112110,
1020120102112120,

1021012210102202,
1011022110210210,
1021012210102202,
1021022210210200,

1021012210102202,
1011022010210210,
3004003001102210,
3030402700008022, //10

1012022001221121,
100000200100000200500000000000000000000008000000,
1012022001221121,
100000200100000100500000000000000000000008000000,

1012022001221121,
1012022020210110,
500000000000000000000008000000200000100000100000,
3004003001012222, //18

1021012010221220,
1021012120210200,
1012021010221220,
1021012120211220,

1021112120101020,
500000000008000000100000100000100200000000000000,
3004003001202210,
#MEASURE 2/4
30302207,
#MEASURE 4/4
0008, //27

#BPMCHANGE 149
4000100120001020,
1020101020111000,
70000008,
2000102220001001,

500000000000000000000000000008000000100000000100,
1010201010001002,
1000100210002001,
500000000000000000000008000000000000100000000100, //35

500000000000000000000000000008000000100000000100,
1010201010001002,
1000100210002001,
500000000000000000000000000000000000000008000000,

3001204000102010,
2000102000110110,
2002004000002001, //42

1000100220001001,
2000200110001001,
2000100220001002,
500000000000000000000008000000000000100000000100, 

2020101120201021,
2020102110001011,
2020102120201021,
1010210120000000, //50

#BPMCHANGE 258
#SCROLL 0.75
#GOGOSTART
30210260,
000000000000000000000000000000000008000000000000,
11020260,
000000000000000000000008000000000000000000000000,

30201202,
10201201,
10211202,
10211201, //58

10120120,
01221122,
10120120,
01221122,

10120220,
01221122,
10211000,
20220222, //66

10120120,
01221122,
10120220,
02112211,

10120220,
01011020,
500000000000000000000000000000000000000000000008,
0, //74
#GOGOEND

10110220,
01112020,
12110110,
22120220,

10110220,
01112020,
11210110,
21220220, //82

10110112,
01111020,
11110104,
00300202,

10110104,
03040307,
0,
0800, //90

#GOGOSTART
10120120,
01221122,
10010020,
02201010,

10120120,
01221122,
40030060,
000000000000000000000000000000000008000000000000, //98

10120120,
01221122,
20010022,
10000011,
11011011,
01103030, //104

31011011,
02222220,
11011011,
02112212,
#GOGOEND

3,
0,
0,
0,
0, //113
#END


COURSE:Hard
LEVEL:6
BALLOON:5,2,14,10,6,8,12
SCOREINIT:570,2170
SCOREDIFF:143

#START
3003004000111020,
1010110100000000,

1011001010201000,
1011002010010000,
1011001010201000,
1011002020020000,

1011001010201000,
1011002010010000,
3004003000000000,
3030300700008000, //10

1001001020002000,
100000100100000100500000000000000000000008000000,
1001001020002000,
100000100100000100500000000000000000000008000000,

1001001020002000,
1001002010020010,
500000000000000000000008000000200000100000100000,
3004003004070080, //18

1001001000102000,
1001001000202000,
1001001000102000,
1001001000202000,

10001112,
500000000008000000100000100000000200000000000000,
3004003000000000,
#MEASURE 2/4
30303007,
#MEASURE 4/4
0008, //27

#BPMCHANGE 149
4000100110002020,
11221010,
900000000000000000000000000000000009000000000008,
0,

500000000000000000000000000008000000000000000000,
1010201010001001,
1000100110001001,
500000000000000000000008000000000000000000000000,

500000000000000000000000000008000000000000000000,
1010201010001001,
1000100110001001,
500000000000000000000000000000000000000008000000, //39

3001104000102010,
10020110,
2002004000002000, //42

1000100110001001,
1000100110001001,
1000100110001001,
500000000000000000000008000000000000100000000000,

1000100110002001,
1000200110001001,
1000100110001001,
1120, //50

#BPMCHANGE 258
#SCROLL 0.75
#GOGOSTART
30030060,
000000000000000000000000000000000008000000000000,
30040060,
000000000000000000000008000000000000000000000000,

30001001,
10101100,
10101100,
7008, //58

10110110,
0212,
10110110,
0212,

10110110,
0212,
37,
08, //66

10110110,
0212,
10110110,
0212,

10220220,
01011010,
500000000000000000000000000000000000000000000008,
0, //74
#GOGOEND

10110110,
0120,
10110110,
20220220,

10110110,
0120,
10110110,
20220220, //82

10010101,
02,
10110103,
0300,

30030003,
00030007,
0,
0800, //90

#GOGOSTART
10110110,
0212,
10010020,
0011,

10110110,
0212,
40030060,
000000000000000000000000000000000008000000000000, //98

10110110,
0212,
20010011,
1002,

30030030,
03003030,
3,
0,
30030030,
0, //108
#GOGOEND

3,
0,
0,
0,
0, //113
#END


COURSE:Normal
LEVEL:5
BALLOON:4,10,9,6,8
SCOREINIT:730,3640
SCOREDIFF:205

#START
3003003000000000,
1010100100000000,

10001110,
1000000010010000,
10001110,
12,

10001110,
1000000010010000,
3003003000000000,
3030300700008000, //10

1022,
500000000000000000000000000008000000000000000000,
1022,
500000000000000000000000000008000000000000000000,

1022,
11,
500000000000000000000008000000000000000000000000,
3003003000000000, //18

10000110,
10000220,
10000110,
10000220,

1011,
500000000008000000000000100000000000000000000000,
3003003000000000,
#MEASURE 2/4
30303007,
#MEASURE 4/4
0008, //27

#BPMCHANGE 149
4012,
1111,
900000000000000000000000000000000009000000000008,
0,

500000000000000000000000000008000000000000000000,
10111010,
1212,
500000000000000000000008000000000000000000000000, //35

500000000000000000000000000008000000000000000000,
10111010,
1212,
500000000000000000000000000000000000000008000000,

30040000,
20020000,
2002004000000000, //42

12,
1210,
12,
500000000000000000000008000000000000000000000000,

12,
1210,
1212,
1120, //50

#BPMCHANGE 258
#SCROLL 0.75
#GOGOSTART
30030060,
000000000000000000000000000000000008000000000000,
30030060,
000000000000000000000008000000000000000000000000, 

3,
11,
1,
1, //58

10010010,
01,
10010010,
01,

10010010,
02,
37,
08, //66

10010010,
01,
10010010,
02,

10010010,
01,
500000000000000000000000000000000000000000000008,
0, //74
#GOGOEND

10010010,
02,
10010000,
20020000,

10010010,
02,
10010000,
20020000, //82

10010001,
02,
10010003,
0300,

30030003,
00030007,
0,
0800, //90

#GOGOSTART
10010010,
01,
10010010,
0,

10010010,
01,
40040060,
000000000000000000000000000000000008000000000000, //98

10010010,
02,
10010000,
1,

30030030,
03003030,
3,
0,
30030030,
0, //108
#GOGOEND

3,
0,
0,
0,
0, //113
#END


COURSE:Easy
LEVEL:3
BALLOON:4,8,6,4,6
SCOREINIT:710,6280
SCOREDIFF:258

#START
30030000,
1100,

11,
1,
11,
1,

11,
22,
30030000,
300000000000700000000000000000080000000000000000, //10

1,
500000000000000000000000000008000000000000000000,
1,
500000000000000000000000000008000000000000000000,

2,
11,
500000000000000000000008000000000000000000000000,
30030000, //18

1001,
1001,
1001,
1001,

1011,
500000000000000000000008000000000000000000000000,
30030000,
#MEASURE 2/4
30003007,
#MEASURE 4/4
0008, //27

#BPMCHANGE 149
41,
1111,
900000000000000000000000000000000009000000000008,
0,

500000000000000000000000000008000000000000000000,
1110,
11,
500000000000000000000008000000000000000000000000, //35

500000000000000000000000000008000000000000000000,
1110,
11,
500000000000000000000000000008000000000000000000,

30030000,
0,
4, //42

1,
1,
11,
500000000000000000000008000000000000000000000000, 

11,
11,
1110,
1110, //50

#BPMCHANGE 258
#SCROLL 0.75
#GOGOSTART
3006,
000000000000000000000000000008000000000000000000,
3006,
000000000000000000000008000000000000000000000000,

3,
1,
1,
0, //58

1001,
0,
1001,
0, 

1001,
0,
37,
08, //66

1001,
0,
1002,
0,

1001,
01,
500000000000000000000000000000000000000000000008,
0, //74
#GOGOEND

1,
0,
1,
2,

1,
0,
1,
2, //82

10000001,
0,
10000003,
0,

30000003,
00000007,
0,
0800, //90

#GOGOSTART
1001,
0,
1001,
0,

1001,
0,
4006,
000000000000000000000000000008000000000000000000, //98

1001,
0,
1,
1,

3003,
03,
3,
0,
3003,
0, //108
#GOGOEND

3,
0,
0,
0,
0, //113
#END