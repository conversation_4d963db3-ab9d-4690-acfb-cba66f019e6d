//TJADB Project
TITLE:MUSIC♪
TITLEJA:MUSIC♪
SUBTITLE:--<PERSON><PERSON> feat. 765<PERSON><PERSON> ALLSTARS/THE iDOLM@STER SHINY FESTA
SUBTITLEJA:「アイドルマスター」より
BPM:138
WAVE:MUSIC.ogg
OFFSET:-2.050
DEMOSTART:73.354

COURSE:Edit
LEVEL:8
BALLOON:
SCOREINIT:500,1500
SCOREDIFF:125

#START
0202,
0202,
0202,
00200022,

00202022,
02020202,
00202203,
0030003000222210, //8

1022120220120010,
1022120210102102,
1022120220120010,
1012021020112010,

1022120220122010,
1021012010221010,
1022120220212012,
2012120210112211, //16

1022120220120010,
1022120210102102,
1022120220120010,
1012021020122010,

1022120220122010,
1021012010221010,
1022120210212022,
1122112211212212, //24

1011201102112022,
1011201101102010,
1011202210212022,
1011201101102010,

1011201102112012,
2012201220122012,
1010201101011010,
3333, //32

4011201102112022,
1011201101102010,
1011202210212022,
1011201101102010,

1011201102112012,
2012201220102012,
0210201202102012,
0212021120404000,
0, //41

#GOGOSTART
1101201011012011,
0101201022112020,
1101201020112011,
0221201012122010,

1001201022102012,
0210201020122012,
0210201020122030,
0030003022022000, //49

1101201011012011,
0101201022112020,
1101201020112011,
0221201000202020,

1011201010112010,
1011201022112010,
2210201022102010,
2210201022122010, //57

1010121010101210,
1010121010112211,
2011201120112030,
03030303, //61
#GOGOEND

1011201102112011,
0211201101102010,
2010221102102210,
2010221102102211,

1011201202112011,
0211202101102010,
2010221102112021,
500000000000000000000000000000000008000000000000,
3, //70
#END


COURSE:Oni
LEVEL:6
BALLOON:18,15,15
SCOREINIT:630,2090
SCOREDIFF:165

#START
0202,
0202,
0202,
0202,

1010200011102000,
1010200011102000,
1010200011102040,
00040000, //8

2220202220201020,
1020220220000000,
2220202220201020,
1020220220000000,

2220202220201020,
1020220220000070,
000000000000000000000000000000000008000000000000,
2222, //16

1110202220102000,
1110202220102000,
700000008000,
20022220,

1110202210202000,
1110202210202000,
700000008000,
2222, //24

1010202011102000,
1010202011102000,
1110202011102020,
1222,

1020112010201120,
1020112010201120,
500000000000000000000000000008000000000000300000,
3333, //32

3000200020222020,
0020002000201110,
2020111020201110,
20000022,

2000202000200022,
202220200003030,
00330033,
04040440,
0, //41

#GOGOSTART
1010202022202010,
2010201020222020,
1010202022202010,
2010201020222020,

1010202011102020,
1010202011102020,
1010202011102020,
1110202000002000, //49

1010202022201020,
1010202022201020,
1010202022201020,
1010202022201020,

1111,
12101210,
1120100011201000,
1120100011201000, //57

1120102011201020,
1120112011201120,
1220122012201220,
10030303, //61
#GOGOEND

0022202000222020,
02020202,
0022202000222020,
02020202,

0022202000222020,
02020202,
12021202,
1,
3, //70
#END


COURSE:Hard
LEVEL:6
BALLOON:
SCOREINIT:630,2570
SCOREDIFF:183

#START
0,
0,
0,
0,

00202022,
02020202,
00202203,
0030003000111010, //8

20210121,
0010201000100100,
21012103,
01011221,

21012101,
21012121,
01210103,
0010201000111010, //16

21012101,
2010001020100100,
21012103,
01011221,

21012101,
21010121,
21012101,
3333, //24

4002201000202010,
02202022,
11221122,
10211011,

22112101,
21212125,
000000000000000000000000000008000000000000300000,
3333, //32

4002202000102010,
01210121,
22112211,
20211011,

20212101,
21210033,
00330033,
03030440,
0, //41

#GOGOSTART
1010222010100010,
21012121,
20122101,
21210220,

11210121,
01210121,
01212123,
03030220, //49

1010222010100010,
21210121,
20122101,
2010201000222020,

12121212,
12121210,
21212121,
21212120, //57

12121211,
12121215,
000000000000000000000008000000000000300000300000, 
03030304, //61
#GOGOEND

0002,
0202,
0202,
0222,

0202,
0202,
0222,
500000000000000000000000000000000008000000000000,
3, //70
#END


COURSE:Normal
LEVEL:5
BALLOON:
SCOREINIT:800,4290
SCOREDIFF:280

#START
0,
0,
0,
0,

0202,
0202,
00202203,
03030000, //8

2101,
2101,
500000000000000000000000000008000000000000300000,
0021,

2101,
20100015,
000000000000000000000000000008000000000000300000,
0, //16

2101,
2101,
500000000000000000000000000008000000000000300000,
0021,

2101,
20100015,
000000000000000000000000000000000008000000000000,
3333, //24

40000222,
02000022,
20222022,
20000011,

10220101,
01010105,
000000000000000000000000000008000000000000300000,
3333, //32

40000222,
02000022,
20222022,
20000011,

10220101,
01010033,
00330060,
000000000000000008000000000000400000400000000000,
0, //41

#GOGOSTART
00111101,
01010101,
00102201,
01010000,

10220101,
01010101,
00220103,
03030000, //49

00111101,
01010101,
00102201,
01010000,

10111010,
10111000,
20222020,
20222000, //57

10111011,
10111015,
000000000000000000000008000000000000300000300000, 
03030303, //61
#GOGOEND

0002,
0202,
0202,
0202,

0202,
0202,
0202,
500000000000000000000000000000000008000000000000,
3, //70
#END


COURSE:Easy
LEVEL:4
BALLOON:
SCOREINIT:560,4980
SCOREDIFF:200

#START
0,
0,
0,
0,

0202,
0202,
00202203,
00030000, //8

0101,
0101,
500000000000000000000000000008000000000000300000,
0021,

2101,
00100015,
000000000000000000000000000008000000000000300000,
0, //16

2101,
2101,
500000000000000000000000000008000000000000300000,
0011,

2101,
20100015,
000000000000000000000000000000000008000000000000,
3333, //24

3001,
2101,
2121,
2101,

2101,
20102015,
000000000000000000000000000008000000000000000000,
3333, //32

3001,
2101,
2121,
2101,

2101,
2103,
0306,
000000000000000008000000000000000000400000000000,
0, //41

#GOGOSTART
1122,
1122,
1122,
1120,

1122,
1122,
10102003,
03030000, //49

1122,
1122,
1122,
1120,

1111,
1111,
2121,
2121, //57

1111,
10101015,
000000000000000000000008000000000000000000300000, 
03030303, //61
#GOGOEND

0002,
0202,
0202,
0202,

0202,
0202,
0202,
500000000000000000000000000000000008000000000000,
3, //70
#END
