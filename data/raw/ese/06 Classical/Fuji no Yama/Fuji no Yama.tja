//TJADB Project
TITLE:Fuji no Yama
TITLEJA:ふじの山
SUBTITLE:--Sazanami Iwaya
SUBTITLEJA:
BPM:91.993
WAVE:Fuji no Yama.ogg
OFFSET:-2.805
DEMOSTART:23.675

//CS7 chart: BPM 92, HS1.01 in gogotime, HS1 otherwise

COURSE:Oni
LEVEL:3
BALLOON:12
SCOREINIT:1050,3980
SCOREDIFF:323

#START
11211210,
21121121,
11211210,
21121121,

11211210,
21121121,
11211211,
1010202010102210, //8

12101210,
12111211,
10211021,
500000000000000000000000000008000000000000000000,

11201120,
11221120,
10221021,
500000000000000000000000000008000000200200100000, //16

11211121,
1120,
10211021,
500000000000000000000000000008000000000000000000,

#GOGOSTART
11221121,
11221212,
30403004,
600000000000000000000000000000000008000000000000, //24
#GOGOEND

12101210,
12111211,
10211021,
500000000000000000000000000008000000000000000000,

11201120,
11221120,
10211021,
500000000000000000000000000008000000200200100000, //32

11211121,
1120,
10211021,
500000000000000000000000000008000000000000000000,

#GOGOSTART
11221121,
11221212,
30403004,
600000000000000000000000000000000008000000000000, //40
#GOGOEND

10201120,
10201121,
10201207,
000000000000000000000000000000000000080000000000,
0,
0, //46
#END


COURSE:Hard
LEVEL:3
BALLOON:10
SCOREINIT:1380,6370
SCOREDIFF:553

#START
2211,
2120,
2211,
2120,

22201110,
22102000,
22201110,
2, //8

10011010,
1000100010222020,
10011010,
500000000000000000000000000008000000000000000000,

10011010,
1011,
10011010,
500000000000000000000000000008000000000000000000, //16

10011010,
1000100010222020,
10011010,
500000000000000000000000000008000000000000000000,

#GOGOSTART
1012,
1012,
30303003,
600000000000000000000000000000000008000000000000, //24
#GOGOEND

10011020,
1000100010222020,
10011020,
500000000000000000000000000008000000000000000000,

10011020,
10011020,
10011010,
500000000000000000000000000008000000000000000000, //32

10011020,
1000100010222020,
10011020,
500000000000000000000000000008000000000000000000,

#GOGOSTART
1012,
1012,
30303003,
600000000000000000000000000000000008000000000000, //40
#GOGOEND

1011,
1011,
10201209,
000000000000000000000000009000000000080000000000,
0,
0, //46
#END


COURSE:Normal
LEVEL:1
BALLOON:8,8,8
SCOREINIT:1300,9800
SCOREDIFF:823

#START
1202,
2120,
1202,
2120,

1202,
2120,
1202,
1, //8

1011,
11,
1011,
500000000000000000000000000008000000000000000000,

11,
1110,
1011,
500000000000000000000000000008000000000000000000, //16

1120,
1120,
900000000000000009000008,
0,

#GOGOSTART
1011,
1011,
3330,
600000000000000000000000000008000000000000000000, //24
#GOGOEND

12,
1110,
1011,
500000000000000000000000000008000000000000000000,

12,
1110,
1011,
500000000000000000000000000008000000000000000000, //32

1022,
12,
900000000000000009000008,
0,

#GOGOSTART
1022,
1011,
3330,
600000000000000000000000000008000000000000000000, //40
#GOGOEND

1011,
1011,
10003009,
000000000000000000000000009000000000080000000000,
0,
0, //46
#END


COURSE:Easy
LEVEL:2
BALLOON:6,6,6
SCOREINIT:1180,11060
SCOREDIFF:790

//1* in CS7

#START
11,
1120,
11,
1120,

11,
2120,
11,
2120, //8

1011,
11,
1011,
500000000000000000000000000008000000000000000000,

11,
1011,
1011,
500000000000000000000000000008000000000000000000, //16

12,
12,
900000000000000009000008,
0,

#GOGOSTART
11,
11,
33,
600000000000000000000000000008000000000000000000, //24
#GOGOEND

11,
1110,
1011,
500000000000000000000000000008000000000000000000,

11,
1011,
1011,
500000000000000000000000000008000000000000000000, //32

12,
12,
900000000000000009000008,
0,

#GOGOSTART
1011,
1011,
33,
600000000000000000000000000008000000000000000000, //40
#GOGOEND

1011,
1011,
10003009,
000000000000000000000000009000000000080000000000,
0,
0, //46
#END