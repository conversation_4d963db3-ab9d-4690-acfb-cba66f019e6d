//TJADB Project
TITLE:Utsukushi<PERSON> Isogashiki Danube
TITLEJA:美しく忙しきドナウ
SUBTITLE:--Katsuro Tajima
BPM:210
WAVE:Utsukushiku Isogashiki Danube.ogg
OFFSET:-1.736
DEMOSTART:23.736

//Shinuchi: 3580/2800/2010/1370/920

COURSE:Edit
LEVEL:10
BALLOON:2,2
SCOREINIT:350,960
SCOREDIFF:83

#START
#MEASURE 3/4
#GOGOSTART
3,
3,
3,
101202100, //4

302120,
302120,
300000200100200010,
200020100010200200,
#MEASURE 2/4
10112000,

#MEASURE 3/4
4,
112211221122,
1,
221122112211,
2, //14

102100200,
100020100100200200,
102100200,
100000002000100000100000200200200000,

101221,
201221,
221020102210,
112211221122, //22

100000100111200000,
100000110220100000,
140,
#BA<PERSON><PERSON><PERSON>OFF
0,//26
#GOGOEND

#BARLINE<PERSON>
500000000000000000000008000000000000,
500000000000000000000008000000000000,
500000000000000000000008000000000000, //29

122112100,
122112100,
122112122,
102102100,

112112100,
112112100,
112112122,
102102100, //37

111221200,
111221200,
111221221,
102102102,

122122102,
122122102,
122122122,
102102102, //45

122121102,
122121102,
122121121,
101202102,

122121102,
122121102,
122121121,
111222121, //53

122121122,
122121122,
122121122,
122121122,

100010200020700800,
100010200020700800,
112211222,
#BPMCHANGE 160
#SCROLL 1.31
200000
#BPMCHANGE 145
#SCROLL 1.44
220000
#BPMCHANGE 136
#SCROLL 1.54
220000, //61

#BPMCHANGE 200
#SCROLL 1.05
121221,
112121,
121221,
112121,

101210,
001112, //67

#BPMCHANGE 193
#SCROLL 1.08
10
#BPMCHANGE 182
#SCROLL 1.15
22
#BPMCHANGE 173
#SCROLL 1.21
10, //68

#MEASURE 4/4
#BPMCHANGE 163
#SCROLL 1.28
1111
#BPMCHANGE 134
#SCROLL 1.56
2000
#BPMCHANGE 162
#SCROLL 1.29
2020
#BPMCHANGE 187
#SCROLL 1.12
1010, //69

#MEASURE 3/4
#BPMCHANGE 200
#SCROLL 1.05
121221,
112121,
121221,
112121,

111221,
111221,
333020,
#BPMCHANGE 189
#SCROLL 1.11
30
#BPMCHANGE 178
#SCROLL 1.17
40
#BPMCHANGE 164
#SCROLL 1.28
12, //77

#BPMCHANGE 142
#SCROLL 1.47
1222
#BPMCHANGE 148
#SCROLL 1.41
1222
#BPMCHANGE 153
#SCROLL 1.37
1222,

#BPMCHANGE 165
#SCROLL 1.27
1222
#BPMCHANGE 176
#SCROLL 1.19
1222
#BPMCHANGE 187
#SCROLL 1.12
1221, //79

#BPMCHANGE 195
#SCROLL 1.07
122212221222,
122212221221,

122212221222,
122212221221,
122212221222,
122212221221, //85

100012202000,
100012202000,
100012202000,
101012201020,

101022102010,
222010201020,
333020,
344,
334, //94

#GOGOSTART
100000222200200000,
100000222200200000,
110220110220110220,
100000222200100200,

100000222200200000,
100000222200200000,
221122,
100000222200100200, //102

100000220220100100,
220220100100100200,
100000222200200000,
1,
102220,
102222,

3,
3,
3,
112221,
110000000000000000, //113

#BPMCHANGE 190
#SCROLL 1.1
#GOGOEND
500000000000000000000008000000000000,
#BPMCHANGE 210
#SCROLL 1
500000000000000000000008000000000000,
#BPMCHANGE 220
#SCROLL 0.95
500000000000000000000008000000000000, //116

#SCROLL 1.19
111122220000,
111122212000,
101010200200100100200200100100200200,
101202100,

111122220000,
111122212000,
101010200200100100200200100100200200,
101202100, //124

111122220000,
111122212000,
101010200200100100200200100100200200,
101202100,

111122220000,
111122212000,
101010200200100100200200100100200200,
101202100, //132

111122220000,
111122212000,
101010200200100100200200100100200200,
101202100,

111122220000,
111122212000,
101010200200100200200200100200200200,
101202102, //140

102020102011102020,
102020102011102020,
102020102011102020,
102020102011102020,
100201201,
100201201,

101221,
200010222010,
200010202210,
221221, //150

#BPMCHANGE 240
#SCROLL 1.09
#GOGOSTART
112212,
101022201020,
101020221020,
101022201022,

102210222010,
102210222010,
102210222010,
102210222010, //158

3,
#BPMCHANGE 165
#SCROLL 1.59
400003,
6,
#BARLINEOFF
000000000000000000000000000000000008,
#GOGOEND

0,
0,
0, //165
#END


COURSE:Oni
LEVEL:8
BALLOON:2,2,2,2,2,2,2
SCOREINIT:460,1460
SCOREDIFF:110

#START
#MEASURE 3/4
#GOGOSTART
102220,
102220,
102220,
122122100, //4

102220,
102220,
102220,
122122100, //8
#MEASURE 2/4
10221000,

#MEASURE 3/4
400020202000,
111022201110,
202220,
222011102220,
102220, //14

102120,
100000201010200100,
201210,
201221, //18

200000201010200100,
200000201010200100,
112211,
222011102220, //22

300111200,
300111200,
340,
#BARLINEOFF
000, //26
#GOGOEND

#BARLINEON
#MEASURE 3/4
2,
2,
2, //29

122,
100201200,
100200202,
112, //33

122,
100201200,
100200202,
112, //37

122,
100201200,
100200202,
101202100, //41

122,
100201200,
101200202,
101200200, //45

500000000000000008000000100000000000,
780010,
780000,
101200200, //49

500000000000000008000000100000000000,
780010,
700800000000200020,
101200200, //53

100122100,
100121200,
100112100,
100121100, //57

112,
112,
300020202022,
#BPMCHANGE 160
#SCROLL 1.31
2
#BPMCHANGE 145
#SCROLL 1.44
2
#BPMCHANGE 136
#SCROLL 1.54
2, //61

#BPMCHANGE 200
#SCROLL 1.05
102211,
212,
102211,
211, //65

500000000000000008000000700008000000,
000000100010200000,
#BPMCHANGE 193
#SCROLL 1.08
500000000000
#BPMCHANGE 182
#SCROLL 1.15
008000000000
#BPMCHANGE 173
#SCROLL 1.21
700008000000, //68

#MEASURE 4/4
#BPMCHANGE 163
#SCROLL 1.28
1111
#BPMCHANGE 134
#SCROLL 1.56
2000
#BPMCHANGE 162
#SCROLL 1.29
2020
#BPMCHANGE 187
#SCROLL 1.12
1010, //69

#MEASURE 3/4
#BPMCHANGE 200
#SCROLL 1.05
102210,
202210,
201120,
122, //73

112121,
221212,
333020,
#BPMCHANGE 189
#SCROLL 1.11
3
#BPMCHANGE 178
#SCROLL 1.17
4
#BPMCHANGE 164
#SCROLL 1.28
2, //77

#BPMCHANGE 142
#SCROLL 1.47
1
#BPMCHANGE 148
#SCROLL 1.41
2
#BPMCHANGE 153
#SCROLL 1.37
2,

#BPMCHANGE 165
#SCROLL 1.27
1000
#BPMCHANGE 176
#SCROLL 1.19
2220
#BPMCHANGE 187
#SCROLL 1.12
2000, //79

#BPMCHANGE 195
#SCROLL 1.07
122,
100022202020, //81

122,
100022202000,
122,
100022201020, //85

102210102000,
102210102000,
102210102020,
100022102020, //89

102210102020,
102210102020,
333020,
312,
112212, //94

#GOGOSTART
100022202000,
100022202000,
100022202010,
200022201020, //98

100022202000,
100022202000,
101022202020,
101022201010, //102

200022201020,
201022201020,
201022201020,
122, //106

100022202000,
100022202020,

102120,
102120, //110
1,
222222,
700008000000000000000000000000000000, //113
#GOGOEND

#BPMCHANGE 190
#SCROLL 1.1
#GOGOEND
2,
#BPMCHANGE 210
#SCROLL 1
2,
#BPMCHANGE 220
#SCROLL 0.95
2, //116

122,
100201200,
100200202,
112, //120

122,
100201200,
100200202,
112, //124

122,
100201200,
100200202,
101202100, //128

122,
100201200,
101200202,
101200202, //132

101201200,
101201200,
101202102,
112, //136

101201200,
101201200,
101,
122, //140

100221200,
100121200,
100221200,
100121200, //144

112,
112, //146

302211,
201211,
201122,
222010102220, //150

#BPMCHANGE 240
#SCROLL 0.87
#GOGOSTART
101121,
211121,
112121,
212112, //154

112211,
112211,
112211,
112211, //158

3,
#BPMCHANGE 165
#SCROLL 1.27
400003,
6,
#BARLINEOFF
000000000000000000000000000000000008,

#GOGOEND
0,
0,
0, //165
#END


COURSE:Hard
LEVEL:6
BALLOON:5,10
SCOREINIT:520,2210
SCOREDIFF:133

#START
#MEASURE 3/4
#SCROLL 0.85
#GOGOSTART
122,
122,
122,
112020,

122,
122,
122,
112020,
#MEASURE 2/4
3, //9

#MEASURE 3/4
0,
500000000000000000000000000000000008,
0,
500000000000000000000000000000000008,
0,

122,
102120,
122,
102120, //18

102120,
102120,
112020,
112020,

330,
330,
340,
#BARLINEOFF
0, //26
#GOGOEND

#BARLINEON
2,
2,
2,

122,
102120,
122,
112,

122,
102120,
122,
112, //37

122,
102120,
122,
112,

122,
102120,
122,
102120, //45

500000000000000008000000400000000000,
404,
4,
102120,

500000000000000008000000400000000000,
404,
401,
122, //53

122,
102120,
122,
102120,

110,
110,
600000000000000000000000000000000008,
#BPMCHANGE 160
#SCROLL 1.11
0
#BPMCHANGE 145
#SCROLL 1.22
0
#BPMCHANGE 136
#SCROLL 1.3
0, //61

#BPMCHANGE 200
#SCROLL 0.89
121021,
222,
121021,
222,

102210,
022,

#BPMCHANGE 193
#SCROLL 0.92
10
#BPMCHANGE 182
#SCROLL 0.98
22
#BPMCHANGE 173
#SCROLL 1.02
10,

#MEASURE 4/4
#BPMCHANGE 163
#SCROLL 1.09
700000000000
#BPMCHANGE 134
#SCROLL 1.33
000008000000
#BPMCHANGE 162
#SCROLL 1.1
200000000000
#BPMCHANGE 187
#SCROLL 0.95
200000000000, //69

#MEASURE 3/4
#BPMCHANGE 200
#SCROLL 0.89
121021,
222,
121021,
222,

122,
122,
111010,
#BPMCHANGE 189
#SCROLL 0.94
1
#BPMCHANGE 178
#SCROLL 0.99
4
#BPMCHANGE 164
#SCROLL 1.09
0, //77

#BPMCHANGE 142
#SCROLL 1.25
1
#BPMCHANGE 148
#SCROLL 1.2
1
#BPMCHANGE 153
#SCROLL 1.16
1,
#BPMCHANGE 165
#SCROLL 1.08
1
#BPMCHANGE 176
#SCROLL 1.01
2
#BPMCHANGE 187
#SCROLL 0.95
2,
#BPMCHANGE 195
#SCROLL 0.91
111,
122,

101110,
122,
101110,
122, //85

102220,
102220,
102220,
121,

112020,
112020,
111010,
122,
112020, //94

#GOGOSTART
122,
100022202000,
102220,
201122,

122,
100022202000,
102220,
201122, //102

122,
100022202000,
102220,
2,

100022202000,
102222,
122,
102220,

1,
7,
8, //113
#GOGOEND

#BPMCHANGE 190
#SCROLL 0.94
2,
#BPMCHANGE 210
#SCROLL 0.85
2,
#BPMCHANGE 220
#SCROLL 0.81
2,

122,
102120,
122,
112,

122,
102120,
122,
112, //124

122,
102120,
122,
112,

122,
102120,
122,
112, //132

500000000000000008000000400000000000,
404,
4,
112,

500000000000000008000000400000000000,
404,
401,
122, //140

122,
102120,
122,
102120,
112,
112,

301112,
201112,
201112,
222, //150

#BPMCHANGE 240
#SCROLL 0.74
#GOGOSTART
6,
0,
0,
000000000000000000000008000000000000,

3,
3,
3,
3, //158

3,
#BPMCHANGE 165
#SCROLL 1.08
400003,
6,
#BARLINEOFF
000000000000000000000000000000000008,
#GOGOEND
0,
0,
0, //165
#END


COURSE:Normal
LEVEL:4
BALLOON:7,7,5,4,4,4,7
SCOREINIT:520,4020
SCOREDIFF:135

#START
#MEASURE 3/4
#SCROLL 0.7
#GOGOSTART
110,
110,
110,
222,

110,
110,
110,
222,
#MEASURE 2/4
3, //9

#MEASURE 3/4
0,
222,
2,
222,
2,

110,
110,
110,
110, //18

110,
110,
111,
222,

330,
330,
340,
#BARLINEOFF
0, //26
#GOGOEND

#BARLINEON
2,
2,
2,

103,
303,
3,
220,

103,
303,
3,
220, //37

103,
303,
3,
220,

103,
303,
3,
222, //45

304,
404,
4,
222,

304,
404,
4,
0, //53

101,
101,
101,
101,

303,
303,
3,
#BPMCHANGE 160
#SCROLL 0.92
0
#BPMCHANGE 145
#SCROLL 1.01
0
#BPMCHANGE 136
#SCROLL 1.08
0, //61

#BPMCHANGE 200
#SCROLL 0.74
111,
022,
111,
022,

7,
822,
#BPMCHANGE 193
#SCROLL 0.76
7
#BPMCHANGE 182
#SCROLL 0.81
0
#BPMCHANGE 173
#SCROLL 0.85
0,
#MEASURE 4/4
#BPMCHANGE 163
#SCROLL 0.9
8
#BPMCHANGE 134
#SCROLL 1.09
0
#BPMCHANGE 162
#SCROLL 0.89
0
#BPMCHANGE 187
#SCROLL 0.78
0, //69

#MEASURE 3/4
#BPMCHANGE 200
#SCROLL 0.74
111,
022,
111,
022,

110,
110,
303,
#BPMCHANGE 189
#SCROLL 0.78
3
#BPMCHANGE 178
#SCROLL 0.82
4
#BPMCHANGE 164
#SCROLL 0.9
0, //77

#BPMCHANGE 142
#SCROLL 1.03
900000000000
#BPMCHANGE 148
#SCROLL 0.99
000000900000
#BPMCHANGE 153
#SCROLL 0.96
080000000000,
#BPMCHANGE 165
#SCROLL 0.89
0
#BPMCHANGE 176
#SCROLL 0.83
0
#BPMCHANGE 187
#SCROLL 0.78
0,
#BPMCHANGE 195
#SCROLL 0.75
900980,
0,

900980,
0,
900980,
0, //85

101,
101,
1,
1,

110,
110,
333,
3,
0, //94

#GOGOSTART
101,
101,
500000000000000000000000000000000008,
0,

101,
101,
500000000000000000000000000000000008,
0, //102

101,
101,
110,
1,

101,
110,
110,
110,

1,
7,
8, //113
#GOGOEND

#BPMCHANGE 190
#SCROLL 0.77
2,
#BPMCHANGE 210
#SCROLL 0.7
2,
#BPMCHANGE 220
#SCROLL 0.67
2,

103,
303,
3,
220,

103,
303,
3,
220, //124

103,
303,
3,
220,

103,
303,
3,
222, //132

304,
404,
4,
222,

304,
404,
4,
0, //140

101,
101,
101,
101,

303,
303,
3,
3,
3,
344, //150

#BPMCHANGE 240
#SCROLL 0.61
#GOGOSTART
6,
0,
0,
000000000000000000000008000000000000,

3,
3,
3,
3, //158

3,
#BPMCHANGE 165
#SCROLL 0.89
400003,
6,
#BARLINEOFF
000000000000000000000000000000000008,
#GOGOEND
0,
0,
0, //165
#END


COURSE:Easy
LEVEL:4
BALLOON:5,5,4,3,3,3,5
SCOREINIT:440,5680
SCOREDIFF:113

//RC score: 440/108

#START
#MEASURE 3/4
#SCROLL 0.7
#GOGOSTART
3,
3,
3,
220,

3,
3,
3,
220,
#MEASURE 2/4
3, //9

#MEASURE 3/4
0,
220,
2,
110,
1,

1,
1,
1,
110, //18

110,
110,
5,
000000000000000008000000000000000000,

3,
3,
3,
#BARLINEOFF
0, //26
#GOGOEND

#BARLINEON
5,
0,
000000000000000000000000000000000008,

003,
303,
3,
220,

203,
303,
3,
220, //37

203,
303,
3,
220,

203,
303,
3,
110, //45

104,
404,
4,
110,

104,
404,
4,
0, //53

1,
101,
1,
1,

303,
303,
3,
#BPMCHANGE 160
#SCROLL 0.92
0
#BPMCHANGE 145
#SCROLL 1.01
0
#BPMCHANGE 136
#SCROLL 1.08
0, //61

#BPMCHANGE 200
#SCROLL 0.74
222,
0,
222,
0,

7,
8,
#BPMCHANGE 193
#SCROLL 0.76
7
#BPMCHANGE 182
#SCROLL 0.81
0
#BPMCHANGE 173
#SCROLL 0.85
0,
#MEASURE 4/4
#BPMCHANGE 163
#SCROLL 0.9
8
#BPMCHANGE 134
#SCROLL 1.1
0
#BPMCHANGE 162
#SCROLL 0.9
0
#BPMCHANGE 187
#SCROLL 0.78
0, //69

#MEASURE 3/4
#BPMCHANGE 200
#SCROLL 0.74
222,
0,
222,
0,

110,
110,
303,
#BPMCHANGE 189
#SCROLL 0.78
3
#BPMCHANGE 178
#SCROLL 0.82
0
#BPMCHANGE 164
#SCROLL 0.9
0, //77

#BPMCHANGE 142
#SCROLL 1.03
900000000000
#BPMCHANGE 148
#SCROLL 0.99
000000900000
#BPMCHANGE 153
#SCROLL 0.96
080000000000,
#BPMCHANGE 165
#SCROLL 0.89
0
#BPMCHANGE 176
#SCROLL 0.84
0
#BPMCHANGE 187
#SCROLL 0.79
0,
#BPMCHANGE 195
#SCROLL 0.75
900980,
0,

900980,
0,
900980,
0, //85

3,
3,
330,
3,

1,
1,
303,
3,
0, //94

#GOGOSTART
1,
1,
500000000000000000000000000000000008,
0,

1,
1,
500000000000000000000000000000000008,
0, //102

1,
1,
500000000000000000000000000000000008,
0,
101,
1,

3,
3,
3,
7,
008, //113
#GOGOEND

#BPMCHANGE 190
#SCROLL 0.77
0,
#BPMCHANGE 210
#SCROLL 0.7
0,
#BPMCHANGE 220
#SCROLL 0.67
0,

003,
303,
3,
220,

203,
303,
3,
220, //124

203,
303,
3,
220,

203,
303,
3,
110, //132

104,
404,
4,
110,

104,
404,
4,
0, //140

101,
1,
101,
1,
303,
303,

6,
000008000000000000000000000000000000,
6,
000008000000000000000000000000000000, //150

#BPMCHANGE 240
#SCROLL 0.61
#GOGOSTART
6,
0,
0,
000000000008000000000000000000000000,

3,
3,
3,
3, //158

3,
#BPMCHANGE 165
#SCROLL 0.89
4,
6,
#BARLINEOFF
000000000000000000000000000000000008,
#GOGOEND
0,
0,
0, //165
#END