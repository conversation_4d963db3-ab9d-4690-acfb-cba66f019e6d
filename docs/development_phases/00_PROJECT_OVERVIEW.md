# TJA Generator - Project Overview and Phase Planning

## Project Objective
Develop a deep learning-based TJA (Taiko no Tatsufin) rhythm chart generation system that can automatically generate high-quality Taiko charts from audio files.

## Current Project Status
- **Data Resources**: data/raw/ese/ contains TJA files and audio files across 9 music genres
- **Development Status**: Brand new project, no existing code
- **Target Hardware**: RTX 3070 (8GB VRAM), 16 logical cores, 32GB RAM

## Phase Planning Overview

### Phase Group 1: Foundation Setup (1-2 weeks)
**Goal**: Establish project infrastructure and development environment

#### Stage 1.1: Project Architecture Setup (2-3 days)
- Establish standardized project directory structure
- Set up development environment and dependency management
- Build configuration management system
- Establish logging and error handling framework

#### Stage 1.2: Data Exploration and Analysis (2-3 days)
- Analyze existing TJA file formats and structures
- Statistics on dataset scale and distribution
- Identify data quality issues
- Establish data validation rules

#### Stage 1.3: TJA Parser Development (3-4 days)
- Develop basic TJA file parser
- Handle different encoding formats (UTF-8, Shift-JIS)
- Parse metadata and chart data
- Build parsing error handling mechanisms

### Phase Group 2: Data Processing (2-3 weeks)
**Goal**: Build complete data preprocessing pipeline

#### Stage 2.1: Audio File Processing (3-4 days)
- Develop audio loading and validation system
- Implement resampling and normalization
- Build audio quality detection
- Handle different audio formats (.ogg, .mp3, .wav)

#### Stage 2.2: TJA-Audio Pairing System (2-3 days)
- Implement WAVE field parsing
- Build file pairing validation
- Handle pairing failure cases
- Generate pairing reports

#### Stage 2.3: Data Cleaning and Validation (3-4 days)
- Build data quality assessment system
- Implement automated data cleaning
- Build anomaly data detection
- Generate data quality reports

#### Stage 2.4: Dataset Splitting and Organization (2-3 days)
- Implement train/validation/test set splitting
- Build data version management
- Implement data caching mechanisms
- Build data loaders

### Phase Group 3: Feature Engineering (2-3 weeks)
**Goal**: Develop audio and chart feature extraction systems

#### Stage 3.1: Basic Audio Feature Extraction (3-4 days)
- Implement STFT and Mel-spectrogram
- Develop beat detection algorithms
- Implement BPM estimation
- Build feature normalization

#### Stage 3.2: Advanced Audio Features (3-4 days)
- Implement MFCC and Chroma features
- Develop rhythm pattern recognition
- Implement audio segmentation
- Build feature fusion mechanisms

#### Stage 3.3: TJA Chart Encoding (3-4 days)
- Develop note sequence encoding
- Implement timeline alignment
- Handle difficulty branching
- Build chart feature vectors

#### Stage 3.4: Feature Alignment and Synchronization (2-3 days)
- Implement audio-chart temporal alignment
- Build synchronization validation mechanisms
- Handle time offsets
- Generate alignment quality reports

### Phase Group 4: Model Development (3-4 weeks)
**Goal**: Develop and train deep learning models

#### Stage 4.1: Basic Model Architecture (4-5 days)
- Design Transformer base architecture
- Implement audio encoder
- Develop sequence decoder
- Build attention mechanisms

#### Stage 4.2: Model Optimization (3-4 days)
- Implement memory optimization
- Develop batch processing
- Build gradient accumulation
- Implement mixed precision training

#### Stage 4.3: Training Pipeline Development (4-5 days)
- Build training loop
- Implement loss functions
- Develop evaluation metrics
- Build checkpoint system

#### Stage 4.4: Hyperparameter Tuning (3-4 days)
- Implement hyperparameter search
- Build experiment tracking
- Develop model comparison
- Build best model selection

### Phase Group 5: System Integration (2-3 weeks)
**Goal**: Build complete inference and validation system

#### Stage 5.1: Inference System Development (3-4 days)
- Build model inference pipeline
- Implement batch inference
- Develop real-time inference
- Build inference optimization

#### Stage 5.2: Post-processing System (3-4 days)
- Develop TJA format generation
- Implement chart post-processing
- Build quality checking
- Implement format validation

#### Stage 5.3: Evaluation and Validation (3-4 days)
- Build automated evaluation system
- Implement human evaluation framework
- Develop quality metrics
- Build benchmark testing

#### Stage 5.4: System Optimization (2-3 days)
- Implement performance optimization
- Build monitoring system
- Develop error handling
- Build system documentation

## Total Timeline Estimate
- **Total Development Time**: 10-15 weeks
- **Milestone Reviews**: After each phase group completion
- **Risk Buffer**: 20% additional time reserved for each phase group

## Success Criteria
1. **Data Processing**: Able to process >95% of raw data
2. **Feature Quality**: Audio-chart alignment accuracy >90%
3. **Model Performance**: Generated chart similarity to human charts >80%
4. **System Stability**: Inference success rate >95%
5. **Performance Requirements**: Single song inference time <30 seconds

## Next Steps
Recommend starting with **Stage 1.1: Project Architecture Setup**, which will establish a solid foundation for the entire project.
