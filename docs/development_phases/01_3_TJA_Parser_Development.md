# Stage 1.3: TJA Parser Development
## Implementation Specification

### Overview
Develop a robust, comprehensive TJA file parser that can handle all format variations, encoding types, and edge cases identified in the data analysis phase. This parser will be the foundation for all subsequent data processing.

### Duration: 3-4 days

## Objectives
1. Implement multi-encoding TJA file parser
2. Handle all TJA format variations and edge cases
3. Separate metadata from chart notation data
4. Implement comprehensive error handling and recovery
5. Create validation and testing framework

## Detailed Tasks

### Task 1.3.1: Core Parser Architecture (8-10 hours)
Design and implement the foundational parser architecture:

**Parser Class Structure:**
`python
class TJAParser:
    def __init__(self, file_path: str, encoding: str = None)
    def parse(self) -> TJADocument
    def validate(self) -> ValidationResult
    def get_metadata(self) -> TJAMetadata
    def get_charts(self) -> List[TJAChart]
    def get_audio_path(self) -> Optional[str]
`

**Core Components:**
- Encoding detection and handling
- Line-by-line parsing with state management
- Metadata extraction and validation
- Chart data parsing and structure building
- Error collection and reporting

**Encoding Support:**
- UTF-8 with BOM detection
- UTF-8 without BOM
- Shift-JIS (Japanese encoding)
- Automatic encoding detection with fallback
- Encoding error handling and recovery

**Deliverables:**
- Core TJAParser class implementation
- Encoding detection utilities
- Basic parsing workflow
- Unit tests for core functionality

### Task 1.3.2: Metadata Parsing Implementation (6-8 hours)
Implement comprehensive metadata extraction:

**Supported Metadata Fields:**
`python
# Song Information
TITLE, TITLEEN, SUBTITLE, SUBTITLEEN
ARTIST, ARTISTEN, CREATOR, MAKER
GENRE, STYLE, LYRICS

# Audio Configuration  
WAVE, DEMOSTART, SONGVOL, SEVOL
OFFSET, BPM, BPMCHANGE

# Visual Elements
BGIMAGE, BGMOVIE, MOVIEOFFSET
SIDE, TAIKOWEBSKIN

# Course Configuration
COURSE, LEVEL, BALLOON, SCOREINIT, SCOREDIFF
SCOREMODE, LIFE, GAME
`

**Metadata Processing Features:**
- Type conversion and validation
- Default value handling
- Multi-language support (Japanese/English)
- Numeric field validation
- Path resolution for referenced files

**Audio File Association:**
- WAVE field parsing and path resolution
- Relative path handling
- File existence validation
- Multiple audio format support
- Fallback strategies for missing audio

**Deliverables:**
- Complete metadata parsing implementation
- Audio file association logic
- Metadata validation functions
- Comprehensive test coverage

### Task 1.3.3: Chart Data Parsing (8-10 hours)
Implement detailed chart notation parsing:

**Note Type Support:**
`python
NOTE_TYPES = {
    '0': 'blank',      # Empty space
    '1': 'don',        # Red note (don)
    '2': 'ka',         # Blue note (ka)  
    '3': 'don_big',    # Big red note
    '4': 'ka_big',     # Big blue note
    '5': 'drumroll',   # Drumroll start
    '6': 'drumroll_big', # Big drumroll start
    '7': 'balloon',    # Balloon note
    '8': 'end_roll',   # End drumroll/balloon
    '9': 'kusudama',   # Special balloon
    'A': 'don_both',   # Both hands don
    'B': 'ka_both',    # Both hands ka
    'F': 'adlib'       # Hidden note
}
`

**Command Processing:**
`python
COMMANDS = {
    '#START': 'chart_start',
    '#END': 'chart_end', 
    '#BPMCHANGE': 'bpm_change',
    '#MEASURE': 'time_signature',
    '#SCROLL': 'scroll_speed',
    '#GOGOSTART': 'gogo_start',
    '#GOGOEND': 'gogo_end',
    '#DELAY': 'timing_delay',
    '#BRANCHSTART': 'branch_start',
    '#BRANCHEND': 'branch_end',
    '#N': 'normal_branch',
    '#E': 'expert_branch', 
    '#M': 'master_branch',
    '#SECTION': 'section_marker'
}
`

**Chart Structure Building:**
- Measure-based organization
- Timing calculation and validation
- Branch handling for difficulty variations
- Command sequence tracking
- Note density analysis

**Deliverables:**
- Complete chart parsing implementation
- Command processing system
- Chart structure validation
- Timing calculation utilities

### Task 1.3.4: Error Handling and Recovery (4-6 hours)
Implement robust error handling for real-world data:

**Error Categories:**
`python
class TJAParsingError(Exception):
    pass

class EncodingError(TJAParsingError):
    pass

class MetadataError(TJAParsingError):
    pass

class ChartFormatError(TJAParsingError):
    pass

class ValidationError(TJAParsingError):
    pass
`

**Error Handling Strategies:**
- Graceful degradation for minor errors
- Error context preservation
- Recovery mechanisms for common issues
- Detailed error reporting
- Partial parsing support

**Validation Framework:**
- Required field checking
- Value range validation
- Format consistency checking
- Cross-field validation
- Chart integrity verification

**Deliverables:**
- Comprehensive error handling system
- Error recovery mechanisms
- Validation framework
- Error reporting utilities

### Task 1.3.5: Testing and Quality Assurance (6-8 hours)
Create comprehensive testing framework:

**Test Categories:**
- Unit tests for individual components
- Integration tests for complete parsing
- Edge case testing with problematic files
- Performance testing with large datasets
- Regression testing for format variations

**Test Data Preparation:**
- Sample files representing all format variations
- Synthetic test cases for edge conditions
- Corrupted file handling tests
- Encoding variation tests
- Large file performance tests

**Quality Metrics:**
- Parsing success rate (target: >95%)
- Error handling coverage
- Performance benchmarks
- Memory usage profiling
- Code coverage analysis

**Deliverables:**
- Complete test suite
- Test data collection
- Performance benchmarks
- Quality assurance reports

## Implementation Structure

### Core Parser Module (src/data/parsers/tja_parser.py):
`python
class TJAParser:
    " Main TJA file parser with comprehensive format support.
    
class TJADocument:
    Represents a complete parsed TJA document.
    
class TJAMetadata:
    Container for TJA metadata fields.
    
class TJAChart:
    Represents a single difficulty chart.
    
class TJANote:
    Individual note with timing and type information.
    
class TJACommand:
    Chart command with parameters and timing.
`

### Utility Modules:
`python
# src/data/parsers/encoding_detector.py
class EncodingDetector:
    Automatic encoding detection for TJA files.

# src/data/parsers/tja_validator.py  
class TJAValidator:
    Validation rules and checking for TJA content.

# src/data/parsers/audio_resolver.py
class AudioResolver:
    Audio file path resolution and validation.
`

## Validation Criteria
1. **Format Support**: Parser handles all TJA format variations found in dataset
2. **Encoding Handling**: Correctly processes UTF-8, UTF-8 BOM, and Shift-JIS files
3. **Error Resilience**: Gracefully handles malformed files with useful error messages
4. **Performance**: Parses typical TJA files in <100ms each
5. **Accuracy**: Parsing results match manual verification on sample files

## Success Metrics
- **Parsing Success Rate**: >95% of dataset files parse successfully
- **Error Handling**: All error cases produce meaningful error messages
- **Performance**: Average parsing time <50ms per file
- **Test Coverage**: >90% code coverage with comprehensive test suite
- **Validation Accuracy**: 100% accuracy on manually verified sample files

## Expected Challenges and Solutions
1. **Encoding Issues**: Implement robust encoding detection with fallbacks
2. **Format Variations**: Use flexible parsing with format-specific handlers
3. **Malformed Files**: Implement error recovery and partial parsing
4. **Performance**: Optimize parsing for large datasets with caching
5. **Japanese Text**: Proper Unicode handling and character encoding

## Deliverables Summary
1. Complete TJA parser implementation with multi-encoding support
2. Metadata extraction and validation system
3. Chart data parsing with full command support
4. Comprehensive error handling and recovery mechanisms
5. Complete test suite with edge case coverage
6. Performance benchmarks and optimization
7. Documentation and usage examples

## Next Stage Dependencies
This parser will be used by:
- Stage 2.1: Audio file processing for TJA-audio pairing
- Stage 2.2: TJA-audio pairing system for file association
- Stage 2.3: Data cleaning for content validation
- All subsequent stages requiring TJA data access

## Risk Mitigation
- **Complex Format Variations**: Start with common cases, expand to edge cases
- **Encoding Problems**: Implement multiple detection strategies
- **Performance Issues**: Profile early and optimize critical paths
- **Error Handling**: Test with known problematic files from dataset
- **Maintainability**: Use clear abstractions and comprehensive documentation
